{"version": 3, "names": ["VISITOR_KEYS", "TraversalContext", "constructor", "scope", "opts", "state", "parentPath", "queue", "priorityQueue", "shouldVisit", "node", "enter", "exit", "type", "keys", "length", "key", "create", "container", "<PERSON><PERSON><PERSON>", "NodePath", "get", "parent", "maybeQueue", "path", "notPriority", "push", "visitMultiple", "visitQueue", "visitSingle", "visited", "WeakSet", "stop", "resync", "contexts", "pushContext", "has", "add", "visit", "popContext", "nodes", "Array", "isArray"], "sources": ["../src/context.ts"], "sourcesContent": ["import NodePath from \"./path\";\nimport { VISITOR_KEYS } from \"@babel/types\";\nimport type Scope from \"./scope\";\nimport type { TraverseOptions } from \".\";\nimport type * as t from \"@babel/types\";\nimport type { Visitor } from \"./types\";\n\nexport default class TraversalContext<S = unknown> {\n  constructor(\n    scope: Scope,\n    opts: TraverseOptions,\n    state: S,\n    parentPath: NodePath,\n  ) {\n    this.parentPath = parentPath;\n    this.scope = scope;\n    this.state = state;\n    this.opts = opts;\n  }\n\n  declare parentPath: NodePath;\n  declare scope: Scope;\n  declare state: S;\n  declare opts: TraverseOptions;\n  queue: Array<NodePath> | null = null;\n  priorityQueue: Array<NodePath> | null = null;\n\n  /**\n   * This method does a simple check to determine whether or not we really need to attempt\n   * visit a node. This will prevent us from constructing a NodePath.\n   */\n\n  shouldVisit(node: t.Node): boolean {\n    const opts = this.opts as Visitor;\n    if (opts.enter || opts.exit) return true;\n\n    // check if we have a visitor for this node\n    if (opts[node.type]) return true;\n\n    // check if we're going to traverse into this node\n    const keys: Array<string> | undefined = VISITOR_KEYS[node.type];\n    if (!keys?.length) return false;\n\n    // we need to traverse into this node so ensure that it has children to traverse into!\n    for (const key of keys) {\n      if (\n        // @ts-expect-error key is from visitor keys\n        node[key]\n      ) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  create(\n    node: t.Node,\n    container: t.Node | t.Node[],\n    key: string | number,\n    listKey?: string,\n  ): NodePath {\n    // We don't need to `.setContext()` here, since `.visitQueue()` already\n    // calls `.pushContext`.\n    return NodePath.get({\n      parentPath: this.parentPath,\n      parent: node,\n      container,\n      key: key,\n      listKey,\n    });\n  }\n\n  maybeQueue(path: NodePath, notPriority?: boolean) {\n    if (this.queue) {\n      if (notPriority) {\n        this.queue.push(path);\n      } else {\n        this.priorityQueue.push(path);\n      }\n    }\n  }\n\n  visitMultiple(container: t.Node[], parent: t.Node, listKey: string) {\n    // nothing to traverse!\n    if (container.length === 0) return false;\n\n    const queue = [];\n\n    // build up initial queue\n    for (let key = 0; key < container.length; key++) {\n      const node = container[key];\n      if (node && this.shouldVisit(node)) {\n        queue.push(this.create(parent, container, key, listKey));\n      }\n    }\n\n    return this.visitQueue(queue);\n  }\n\n  visitSingle(node: t.Node, key: string): boolean {\n    if (\n      this.shouldVisit(\n        // @ts-expect-error key may not index node\n        node[key],\n      )\n    ) {\n      return this.visitQueue([this.create(node, node, key)]);\n    } else {\n      return false;\n    }\n  }\n\n  visitQueue(queue: Array<NodePath>): boolean {\n    // set queue\n    this.queue = queue;\n    this.priorityQueue = [];\n\n    const visited = new WeakSet();\n    let stop = false;\n\n    // visit the queue\n    for (const path of queue) {\n      path.resync();\n\n      if (\n        path.contexts.length === 0 ||\n        path.contexts[path.contexts.length - 1] !== this\n      ) {\n        // The context might already have been pushed when this path was inserted and queued.\n        // If we always re-pushed here, we could get duplicates and risk leaving contexts\n        // on the stack after the traversal has completed, which could break things.\n        path.pushContext(this);\n      }\n\n      // this path no longer belongs to the tree\n      if (path.key === null) continue;\n\n      // ensure we don't visit the same node twice\n      const { node } = path;\n      if (visited.has(node)) continue;\n      if (node) visited.add(node);\n\n      if (path.visit()) {\n        stop = true;\n        break;\n      }\n\n      if (this.priorityQueue.length) {\n        stop = this.visitQueue(this.priorityQueue);\n        this.priorityQueue = [];\n        this.queue = queue;\n        if (stop) break;\n      }\n    }\n\n    // clear queue\n    for (const path of queue) {\n      path.popContext();\n    }\n\n    // clear queue\n    this.queue = null;\n\n    return stop;\n  }\n\n  visit(node: t.Node, key: string) {\n    // @ts-expect-error key may not index node\n    const nodes = node[key] as t.Node | t.Node[] | null;\n    if (!nodes) return false;\n\n    if (Array.isArray(nodes)) {\n      return this.visitMultiple(nodes, node, key);\n    } else {\n      return this.visitSingle(node, key);\n    }\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;;EAASA;;;AAMM,MAAMC,gBAAN,CAAoC;EACjDC,WAAW,CACTC,KADS,EAETC,IAFS,EAGTC,KAHS,EAITC,UAJS,EAKT;IAAA,KAWFC,KAXE,GAW8B,IAX9B;IAAA,KAYFC,aAZE,GAYsC,IAZtC;IACA,KAAKF,UAAL,GAAkBA,UAAlB;IACA,KAAKH,KAAL,GAAaA,KAAb;IACA,KAAKE,KAAL,GAAaA,KAAb;IACA,KAAKD,IAAL,GAAYA,IAAZ;EACD;;EAcDK,WAAW,CAACC,IAAD,EAAwB;IACjC,MAAMN,IAAI,GAAG,KAAKA,IAAlB;IACA,IAAIA,IAAI,CAACO,KAAL,IAAcP,IAAI,CAACQ,IAAvB,EAA6B,OAAO,IAAP;IAG7B,IAAIR,IAAI,CAACM,IAAI,CAACG,IAAN,CAAR,EAAqB,OAAO,IAAP;IAGrB,MAAMC,IAA+B,GAAGd,YAAY,CAACU,IAAI,CAACG,IAAN,CAApD;IACA,IAAI,EAACC,IAAD,YAACA,IAAI,CAAEC,MAAP,CAAJ,EAAmB,OAAO,KAAP;;IAGnB,KAAK,MAAMC,GAAX,IAAkBF,IAAlB,EAAwB;MACtB,IAEEJ,IAAI,CAACM,GAAD,CAFN,EAGE;QACA,OAAO,IAAP;MACD;IACF;;IAED,OAAO,KAAP;EACD;;EAEDC,MAAM,CACJP,IADI,EAEJQ,SAFI,EAGJF,GAHI,EAIJG,OAJI,EAKM;IAGV,OAAOC,aAAA,CAASC,GAAT,CAAa;MAClBf,UAAU,EAAE,KAAKA,UADC;MAElBgB,MAAM,EAAEZ,IAFU;MAGlBQ,SAHkB;MAIlBF,GAAG,EAAEA,GAJa;MAKlBG;IALkB,CAAb,CAAP;EAOD;;EAEDI,UAAU,CAACC,IAAD,EAAiBC,WAAjB,EAAwC;IAChD,IAAI,KAAKlB,KAAT,EAAgB;MACd,IAAIkB,WAAJ,EAAiB;QACf,KAAKlB,KAAL,CAAWmB,IAAX,CAAgBF,IAAhB;MACD,CAFD,MAEO;QACL,KAAKhB,aAAL,CAAmBkB,IAAnB,CAAwBF,IAAxB;MACD;IACF;EACF;;EAEDG,aAAa,CAACT,SAAD,EAAsBI,MAAtB,EAAsCH,OAAtC,EAAuD;IAElE,IAAID,SAAS,CAACH,MAAV,KAAqB,CAAzB,EAA4B,OAAO,KAAP;IAE5B,MAAMR,KAAK,GAAG,EAAd;;IAGA,KAAK,IAAIS,GAAG,GAAG,CAAf,EAAkBA,GAAG,GAAGE,SAAS,CAACH,MAAlC,EAA0CC,GAAG,EAA7C,EAAiD;MAC/C,MAAMN,IAAI,GAAGQ,SAAS,CAACF,GAAD,CAAtB;;MACA,IAAIN,IAAI,IAAI,KAAKD,WAAL,CAAiBC,IAAjB,CAAZ,EAAoC;QAClCH,KAAK,CAACmB,IAAN,CAAW,KAAKT,MAAL,CAAYK,MAAZ,EAAoBJ,SAApB,EAA+BF,GAA/B,EAAoCG,OAApC,CAAX;MACD;IACF;;IAED,OAAO,KAAKS,UAAL,CAAgBrB,KAAhB,CAAP;EACD;;EAEDsB,WAAW,CAACnB,IAAD,EAAeM,GAAf,EAAqC;IAC9C,IACE,KAAKP,WAAL,CAEEC,IAAI,CAACM,GAAD,CAFN,CADF,EAKE;MACA,OAAO,KAAKY,UAAL,CAAgB,CAAC,KAAKX,MAAL,CAAYP,IAAZ,EAAkBA,IAAlB,EAAwBM,GAAxB,CAAD,CAAhB,CAAP;IACD,CAPD,MAOO;MACL,OAAO,KAAP;IACD;EACF;;EAEDY,UAAU,CAACrB,KAAD,EAAkC;IAE1C,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKC,aAAL,GAAqB,EAArB;IAEA,MAAMsB,OAAO,GAAG,IAAIC,OAAJ,EAAhB;IACA,IAAIC,IAAI,GAAG,KAAX;;IAGA,KAAK,MAAMR,IAAX,IAAmBjB,KAAnB,EAA0B;MACxBiB,IAAI,CAACS,MAAL;;MAEA,IACET,IAAI,CAACU,QAAL,CAAcnB,MAAd,KAAyB,CAAzB,IACAS,IAAI,CAACU,QAAL,CAAcV,IAAI,CAACU,QAAL,CAAcnB,MAAd,GAAuB,CAArC,MAA4C,IAF9C,EAGE;QAIAS,IAAI,CAACW,WAAL,CAAiB,IAAjB;MACD;;MAGD,IAAIX,IAAI,CAACR,GAAL,KAAa,IAAjB,EAAuB;MAGvB,MAAM;QAAEN;MAAF,IAAWc,IAAjB;MACA,IAAIM,OAAO,CAACM,GAAR,CAAY1B,IAAZ,CAAJ,EAAuB;MACvB,IAAIA,IAAJ,EAAUoB,OAAO,CAACO,GAAR,CAAY3B,IAAZ;;MAEV,IAAIc,IAAI,CAACc,KAAL,EAAJ,EAAkB;QAChBN,IAAI,GAAG,IAAP;QACA;MACD;;MAED,IAAI,KAAKxB,aAAL,CAAmBO,MAAvB,EAA+B;QAC7BiB,IAAI,GAAG,KAAKJ,UAAL,CAAgB,KAAKpB,aAArB,CAAP;QACA,KAAKA,aAAL,GAAqB,EAArB;QACA,KAAKD,KAAL,GAAaA,KAAb;QACA,IAAIyB,IAAJ,EAAU;MACX;IACF;;IAGD,KAAK,MAAMR,IAAX,IAAmBjB,KAAnB,EAA0B;MACxBiB,IAAI,CAACe,UAAL;IACD;;IAGD,KAAKhC,KAAL,GAAa,IAAb;IAEA,OAAOyB,IAAP;EACD;;EAEDM,KAAK,CAAC5B,IAAD,EAAeM,GAAf,EAA4B;IAE/B,MAAMwB,KAAK,GAAG9B,IAAI,CAACM,GAAD,CAAlB;IACA,IAAI,CAACwB,KAAL,EAAY,OAAO,KAAP;;IAEZ,IAAIC,KAAK,CAACC,OAAN,CAAcF,KAAd,CAAJ,EAA0B;MACxB,OAAO,KAAKb,aAAL,CAAmBa,KAAnB,EAA0B9B,IAA1B,EAAgCM,GAAhC,CAAP;IACD,CAFD,MAEO;MACL,OAAO,KAAKa,WAAL,CAAiBnB,IAAjB,EAAuBM,GAAvB,CAAP;IACD;EACF;;AA1KgD"}