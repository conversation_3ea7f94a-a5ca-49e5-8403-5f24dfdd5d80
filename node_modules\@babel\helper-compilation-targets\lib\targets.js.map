{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "safari", "browserNameMap", "and_chr", "and_ff", "android", "chrome", "edge", "firefox", "ie", "ie_mob", "ios_saf", "node", "op_mob", "opera", "samsung"], "sources": ["../src/targets.ts"], "sourcesContent": ["export const unreleasedLabels = {\n  safari: \"tp\",\n} as const;\n\nimport type { Target } from \"./types\";\n\n// Map from browserslist|@mdn/browser-compat-data browser names to @kangax/compat-table browser names\nexport const browserNameMap: Record<string, Target> = {\n  and_chr: \"chrome\",\n  and_ff: \"firefox\",\n  android: \"android\",\n  chrome: \"chrome\",\n  edge: \"edge\",\n  firefox: \"firefox\",\n  ie: \"ie\",\n  ie_mob: \"ie\",\n  ios_saf: \"ios\",\n  node: \"node\",\n  op_mob: \"opera\",\n  opera: \"opera\",\n  safari: \"safari\",\n  samsung: \"samsung\",\n} as const;\n\nexport type BrowserslistBrowserName = keyof typeof browserNameMap;\n"], "mappings": ";;;;;;AAAO,MAAMA,gBAAgB,GAAG;EAC9BC,MAAM,EAAE;AADsB,CAAzB;;AAOA,MAAMC,cAAsC,GAAG;EACpDC,OAAO,EAAE,QAD2C;EAEpDC,MAAM,EAAE,SAF4C;EAGpDC,OAAO,EAAE,SAH2C;EAIpDC,MAAM,EAAE,QAJ4C;EAKpDC,IAAI,EAAE,MAL8C;EAMpDC,OAAO,EAAE,SAN2C;EAOpDC,EAAE,EAAE,IAPgD;EAQpDC,MAAM,EAAE,IAR4C;EASpDC,OAAO,EAAE,KAT2C;EAUpDC,IAAI,EAAE,MAV8C;EAWpDC,MAAM,EAAE,OAX4C;EAYpDC,KAAK,EAAE,OAZ6C;EAapDb,MAAM,EAAE,QAb4C;EAcpDc,OAAO,EAAE;AAd2C,CAA/C"}