{"version": 3, "names": ["VALIDATORS", "name", "assertString", "manipulateOptions", "assertFunction", "pre", "post", "inherits", "visitor", "assertVisitorMap", "parserOverride", "generatorOverride", "loc", "value", "obj", "assertObject", "Object", "keys", "for<PERSON>ach", "prop", "assertVisitorHandler", "enter", "exit", "Error", "msg", "key", "handler", "validatePluginObject", "rootPath", "type", "source", "validator", "optLoc", "parent", "invalidPluginPropertyError", "code"], "sources": ["../../../src/config/validation/plugins.ts"], "sourcesContent": ["import {\n  assertString,\n  assertFunction,\n  assertObject,\n  msg,\n} from \"./option-assertions\";\n\nimport type {\n  ValidatorSet,\n  Validator,\n  OptionPath,\n  RootPath,\n} from \"./option-assertions\";\nimport type { ParserOptions } from \"@babel/parser\";\nimport type { Visitor } from \"@babel/traverse\";\nimport type { ValidatedOptions } from \"./options\";\nimport type { File, PluginPass } from \"../../index\";\n\n// Note: The casts here are just meant to be static assertions to make sure\n// that the assertion functions actually assert that the value's type matches\n// the declared types.\nconst VALIDATORS: ValidatorSet = {\n  name: assertString as Validator<PluginObject[\"name\"]>,\n  manipulateOptions: assertFunction as Validator<\n    PluginObject[\"manipulateOptions\"]\n  >,\n  pre: assertFunction as Validator<PluginObject[\"pre\"]>,\n  post: assertFunction as Validator<PluginObject[\"post\"]>,\n  inherits: assertFunction as Validator<PluginObject[\"inherits\"]>,\n  visitor: assertVisitorMap as Validator<PluginObject[\"visitor\"]>,\n\n  parserOverride: assertFunction as Validator<PluginObject[\"parserOverride\"]>,\n  generatorOverride: assertFunction as Validator<\n    PluginObject[\"generatorOverride\"]\n  >,\n};\n\nfunction assertVisitorMap(loc: OptionPath, value: unknown): Visitor {\n  const obj = assertObject(loc, value);\n  if (obj) {\n    Object.keys(obj).forEach(prop => assertVisitorHandler(prop, obj[prop]));\n\n    if (obj.enter || obj.exit) {\n      throw new Error(\n        `${msg(\n          loc,\n        )} cannot contain catch-all \"enter\" or \"exit\" handlers. Please target individual nodes.`,\n      );\n    }\n  }\n  return obj as Visitor;\n}\n\nfunction assertVisitorHandler(\n  key: string,\n  value: unknown,\n): VisitorHandler | void {\n  if (value && typeof value === \"object\") {\n    Object.keys(value).forEach((handler: string) => {\n      if (handler !== \"enter\" && handler !== \"exit\") {\n        throw new Error(\n          `.visitor[\"${key}\"] may only have .enter and/or .exit handlers.`,\n        );\n      }\n    });\n  } else if (typeof value !== \"function\") {\n    throw new Error(`.visitor[\"${key}\"] must be a function`);\n  }\n\n  return value as any;\n}\n\ntype VisitorHandler =\n  | Function\n  | {\n      enter?: Function;\n      exit?: Function;\n    };\n\nexport type PluginObject<S extends PluginPass = PluginPass> = {\n  name?: string;\n  manipulateOptions?: (\n    options: ValidatedOptions,\n    parserOpts: ParserOptions,\n  ) => void;\n  pre?: (this: S, file: File) => void;\n  post?: (this: S, file: File) => void;\n  inherits?: Function;\n  visitor?: Visitor<S>;\n  parserOverride?: Function;\n  generatorOverride?: Function;\n};\n\nexport function validatePluginObject(obj: {\n  [key: string]: unknown;\n}): PluginObject {\n  const rootPath: RootPath = {\n    type: \"root\",\n    source: \"plugin\",\n  };\n  Object.keys(obj).forEach((key: string) => {\n    const validator = VALIDATORS[key];\n\n    if (validator) {\n      const optLoc: OptionPath = {\n        type: \"option\",\n        name: key,\n        parent: rootPath,\n      };\n      validator(optLoc, obj[key]);\n    } else {\n      const invalidPluginPropertyError = new Error(\n        `.${key} is not a valid Plugin property`,\n      );\n      // @ts-expect-error todo(flow->ts) consider additing BabelConfigError with code field\n      invalidPluginPropertyError.code = \"BABEL_UNKNOWN_PLUGIN_PROPERTY\";\n      throw invalidPluginPropertyError;\n    }\n  });\n\n  return obj as any;\n}\n"], "mappings": ";;;;;;;AAAA;;AAqBA,MAAMA,UAAwB,GAAG;EAC/BC,IAAI,EAAEC,8BADyB;EAE/BC,iBAAiB,EAAEC,gCAFY;EAK/BC,GAAG,EAAED,gCAL0B;EAM/BE,IAAI,EAAEF,gCANyB;EAO/BG,QAAQ,EAAEH,gCAPqB;EAQ/BI,OAAO,EAAEC,gBARsB;EAU/BC,cAAc,EAAEN,gCAVe;EAW/BO,iBAAiB,EAAEP;AAXY,CAAjC;;AAgBA,SAASK,gBAAT,CAA0BG,GAA1B,EAA2CC,KAA3C,EAAoE;EAClE,MAAMC,GAAG,GAAG,IAAAC,8BAAA,EAAaH,GAAb,EAAkBC,KAAlB,CAAZ;;EACA,IAAIC,GAAJ,EAAS;IACPE,MAAM,CAACC,IAAP,CAAYH,GAAZ,EAAiBI,OAAjB,CAAyBC,IAAI,IAAIC,oBAAoB,CAACD,IAAD,EAAOL,GAAG,CAACK,IAAD,CAAV,CAArD;;IAEA,IAAIL,GAAG,CAACO,KAAJ,IAAaP,GAAG,CAACQ,IAArB,EAA2B;MACzB,MAAM,IAAIC,KAAJ,CACH,GAAE,IAAAC,qBAAA,EACDZ,GADC,CAED,uFAHE,CAAN;IAKD;EACF;;EACD,OAAOE,GAAP;AACD;;AAED,SAASM,oBAAT,CACEK,GADF,EAEEZ,KAFF,EAGyB;EACvB,IAAIA,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA9B,EAAwC;IACtCG,MAAM,CAACC,IAAP,CAAYJ,KAAZ,EAAmBK,OAAnB,CAA4BQ,OAAD,IAAqB;MAC9C,IAAIA,OAAO,KAAK,OAAZ,IAAuBA,OAAO,KAAK,MAAvC,EAA+C;QAC7C,MAAM,IAAIH,KAAJ,CACH,aAAYE,GAAI,gDADb,CAAN;MAGD;IACF,CAND;EAOD,CARD,MAQO,IAAI,OAAOZ,KAAP,KAAiB,UAArB,EAAiC;IACtC,MAAM,IAAIU,KAAJ,CAAW,aAAYE,GAAI,uBAA3B,CAAN;EACD;;EAED,OAAOZ,KAAP;AACD;;AAuBM,SAASc,oBAAT,CAA8Bb,GAA9B,EAEU;EACf,MAAMc,QAAkB,GAAG;IACzBC,IAAI,EAAE,MADmB;IAEzBC,MAAM,EAAE;EAFiB,CAA3B;EAIAd,MAAM,CAACC,IAAP,CAAYH,GAAZ,EAAiBI,OAAjB,CAA0BO,GAAD,IAAiB;IACxC,MAAMM,SAAS,GAAG/B,UAAU,CAACyB,GAAD,CAA5B;;IAEA,IAAIM,SAAJ,EAAe;MACb,MAAMC,MAAkB,GAAG;QACzBH,IAAI,EAAE,QADmB;QAEzB5B,IAAI,EAAEwB,GAFmB;QAGzBQ,MAAM,EAAEL;MAHiB,CAA3B;MAKAG,SAAS,CAACC,MAAD,EAASlB,GAAG,CAACW,GAAD,CAAZ,CAAT;IACD,CAPD,MAOO;MACL,MAAMS,0BAA0B,GAAG,IAAIX,KAAJ,CAChC,IAAGE,GAAI,iCADyB,CAAnC;MAIAS,0BAA0B,CAACC,IAA3B,GAAkC,+BAAlC;MACA,MAAMD,0BAAN;IACD;EACF,CAlBD;EAoBA,OAAOpB,GAAP;AACD"}