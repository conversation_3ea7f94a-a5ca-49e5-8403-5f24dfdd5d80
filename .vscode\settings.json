{"css.validate": false, "less.validate": false, "scss.validate": false, "css.lint.unknownAtRules": "ignore", "tailwindCSS.includeLanguages": {"html": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascript", "typescriptreact": "typescript"}, "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["className\\s*=\\s*[\"']([^\"']*)[\"']", "([a-zA-Z0-9\\-:]+)"]]}