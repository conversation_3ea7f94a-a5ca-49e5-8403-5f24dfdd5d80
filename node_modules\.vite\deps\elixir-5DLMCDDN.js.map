{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/elixir/elixir.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/elixir/elixir.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"comment\"] },\n    { open: '\"\"\"', close: '\"\"\"' },\n    { open: \"`\", close: \"`\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"<<\", close: \">>\" }\n  ],\n  indentationRules: {\n    increaseIndentPattern: /^\\s*(after|else|catch|rescue|fn|[^#]*(do|<\\-|\\->|\\{|\\[|\\=))\\s*$/,\n    decreaseIndentPattern: /^\\s*((\\}|\\])\\s*$|(after|else|catch|rescue|end)\\b)/\n  }\n};\nvar language = {\n  defaultToken: \"source\",\n  tokenPostfix: \".elixir\",\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"<<\", close: \">>\", token: \"delimiter.angle.special\" }\n  ],\n  declarationKeywords: [\n    \"def\",\n    \"defp\",\n    \"defn\",\n    \"defnp\",\n    \"defguard\",\n    \"defguardp\",\n    \"defmacro\",\n    \"defmacrop\",\n    \"defdelegate\",\n    \"defcallback\",\n    \"defmacrocallback\",\n    \"defmodule\",\n    \"defprotocol\",\n    \"defexception\",\n    \"defimpl\",\n    \"defstruct\"\n  ],\n  operatorKeywords: [\"and\", \"in\", \"not\", \"or\", \"when\"],\n  namespaceKeywords: [\"alias\", \"import\", \"require\", \"use\"],\n  otherKeywords: [\n    \"after\",\n    \"case\",\n    \"catch\",\n    \"cond\",\n    \"do\",\n    \"else\",\n    \"end\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"quote\",\n    \"raise\",\n    \"receive\",\n    \"rescue\",\n    \"super\",\n    \"throw\",\n    \"try\",\n    \"unless\",\n    \"unquote_splicing\",\n    \"unquote\",\n    \"with\"\n  ],\n  constants: [\"true\", \"false\", \"nil\"],\n  nameBuiltin: [\"__MODULE__\", \"__DIR__\", \"__ENV__\", \"__CALLER__\", \"__STACKTRACE__\"],\n  operator: /-[->]?|!={0,2}|\\*{1,2}|\\/|\\\\\\\\|&{1,3}|\\.\\.?|\\^(?:\\^\\^)?|\\+\\+?|<(?:-|<<|=|>|\\|>|~>?)?|=~|={1,3}|>(?:=|>>)?|\\|~>|\\|>|\\|{1,3}|~>>?|~~~|::/,\n  variableName: /[a-z_][a-zA-Z0-9_]*[?!]?/,\n  atomName: /[a-zA-Z_][a-zA-Z0-9_@]*[?!]?|@specialAtomName|@operator/,\n  specialAtomName: /\\.\\.\\.|<<>>|%\\{\\}|%|\\{\\}/,\n  aliasPart: /[A-Z][a-zA-Z0-9_]*/,\n  moduleName: /@aliasPart(?:\\.@aliasPart)*/,\n  sigilSymmetricDelimiter: /\"\"\"|'''|\"|'|\\/|\\|/,\n  sigilStartDelimiter: /@sigilSymmetricDelimiter|<|\\{|\\[|\\(/,\n  sigilEndDelimiter: /@sigilSymmetricDelimiter|>|\\}|\\]|\\)/,\n  sigilModifiers: /[a-zA-Z0-9]*/,\n  decimal: /\\d(?:_?\\d)*/,\n  hex: /[0-9a-fA-F](_?[0-9a-fA-F])*/,\n  octal: /[0-7](_?[0-7])*/,\n  binary: /[01](_?[01])*/,\n  escape: /\\\\u[0-9a-fA-F]{4}|\\\\x[0-9a-fA-F]{2}|\\\\./,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      { include: \"@comments\" },\n      { include: \"@keywordsShorthand\" },\n      { include: \"@numbers\" },\n      { include: \"@identifiers\" },\n      { include: \"@strings\" },\n      { include: \"@atoms\" },\n      { include: \"@sigils\" },\n      { include: \"@attributes\" },\n      { include: \"@symbols\" }\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [[/(#)(.*)/, [\"comment.punctuation\", \"comment\"]]],\n    keywordsShorthand: [\n      [/(@atomName)(:)(\\s+)/, [\"constant\", \"constant.punctuation\", \"white\"]],\n      [\n        /\"(?=([^\"]|#\\{.*?\\}|\\\\\")*\":)/,\n        { token: \"constant.delimiter\", next: \"@doubleQuotedStringKeyword\" }\n      ],\n      [\n        /'(?=([^']|#\\{.*?\\}|\\\\')*':)/,\n        { token: \"constant.delimiter\", next: \"@singleQuotedStringKeyword\" }\n      ]\n    ],\n    doubleQuotedStringKeyword: [\n      [/\":/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringKeyword: [\n      [/':/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    numbers: [\n      [/0b@binary/, \"number.binary\"],\n      [/0o@octal/, \"number.octal\"],\n      [/0x@hex/, \"number.hex\"],\n      [/@decimal\\.@decimal([eE]-?@decimal)?/, \"number.float\"],\n      [/@decimal/, \"number\"]\n    ],\n    identifiers: [\n      [\n        /\\b(defp?|defnp?|defmacrop?|defguardp?|defdelegate)(\\s+)(@variableName)(?!\\s+@operator)/,\n        [\n          \"keyword.declaration\",\n          \"white\",\n          {\n            cases: {\n              unquote: \"keyword\",\n              \"@default\": \"function\"\n            }\n          }\n        ]\n      ],\n      [\n        /(@variableName)(?=\\s*\\.?\\s*\\()/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@default\": \"function.call\"\n          }\n        }\n      ],\n      [\n        /(@moduleName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"type.identifier\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        /(:)(@atomName)(\\s*)(\\.)(\\s*)(@variableName)/,\n        [\"constant.punctuation\", \"constant\", \"white\", \"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        /(\\|>)(\\s*)(@variableName)/,\n        [\n          \"operator\",\n          \"white\",\n          {\n            cases: {\n              \"@otherKeywords\": \"keyword\",\n              \"@default\": \"function.call\"\n            }\n          }\n        ]\n      ],\n      [\n        /(&)(\\s*)(@variableName)/,\n        [\"operator\", \"white\", \"function.call\"]\n      ],\n      [\n        /@variableName/,\n        {\n          cases: {\n            \"@declarationKeywords\": \"keyword.declaration\",\n            \"@operatorKeywords\": \"keyword.operator\",\n            \"@namespaceKeywords\": \"keyword\",\n            \"@otherKeywords\": \"keyword\",\n            \"@constants\": \"constant.language\",\n            \"@nameBuiltin\": \"variable.language\",\n            \"_.*\": \"comment.unused\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/@moduleName/, \"type.identifier\"]\n    ],\n    strings: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@doubleQuotedHeredoc\" }],\n      [/'''/, { token: \"string.delimiter\", next: \"@singleQuotedHeredoc\" }],\n      [/\"/, { token: \"string.delimiter\", next: \"@doubleQuotedString\" }],\n      [/'/, { token: \"string.delimiter\", next: \"@singleQuotedString\" }]\n    ],\n    doubleQuotedHeredoc: [\n      [/\"\"\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedHeredoc: [\n      [/'''/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    doubleQuotedString: [\n      [/\"/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    singleQuotedString: [\n      [/'/, { token: \"string.delimiter\", next: \"@pop\" }],\n      { include: \"@stringContentInterpol\" }\n    ],\n    atoms: [\n      [/(:)(@atomName)/, [\"constant.punctuation\", \"constant\"]],\n      [/:\"/, { token: \"constant.delimiter\", next: \"@doubleQuotedStringAtom\" }],\n      [/:'/, { token: \"constant.delimiter\", next: \"@singleQuotedStringAtom\" }]\n    ],\n    doubleQuotedStringAtom: [\n      [/\"/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    singleQuotedStringAtom: [\n      [/'/, { token: \"constant.delimiter\", next: \"@pop\" }],\n      { include: \"@stringConstantContentInterpol\" }\n    ],\n    sigils: [\n      [/~[a-z]@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.interpol\" }],\n      [/~([A-Z]+)@sigilStartDelimiter/, { token: \"@rematch\", next: \"@sigil.noInterpol\" }]\n    ],\n    sigil: [\n      [/~([a-z]|[A-Z]+)\\{/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.{.}\" }],\n      [/~([a-z]|[A-Z]+)\\[/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.[.]\" }],\n      [/~([a-z]|[A-Z]+)\\(/, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.(.)\" }],\n      [/~([a-z]|[A-Z]+)\\</, { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.<.>\" }],\n      [\n        /~([a-z]|[A-Z]+)(@sigilSymmetricDelimiter)/,\n        { token: \"@rematch\", switchTo: \"@sigilStart.$S2.$1.$2.$2\" }\n      ]\n    ],\n    \"sigilStart.interpol.s\": [\n      [\n        /~s@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.s\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.S\": [\n      [\n        /~S@sigilStartDelimiter/,\n        {\n          token: \"string.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.S\": [\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"string\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"string.delimiter\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      { include: \"@stringContent\" }\n    ],\n    \"sigilStart.interpol.r\": [\n      [\n        /~r@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol.r\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol.R\": [\n      [\n        /~R@sigilStartDelimiter/,\n        {\n          token: \"regexp.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol.R\": [\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"regexp\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"regexp.delimiter\", next: \"@pop\" },\n            \"@default\": \"regexp\"\n          }\n        }\n      ],\n      { include: \"@regexpContent\" }\n    ],\n    \"sigilStart.interpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.interpol\": [\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContentInterpol\" }\n    ],\n    \"sigilStart.noInterpol\": [\n      [\n        /~([a-z]|[A-Z]+)@sigilStartDelimiter/,\n        {\n          token: \"sigil.delimiter\",\n          switchTo: \"@sigilContinue.$S2.$S3.$S4.$S5\"\n        }\n      ]\n    ],\n    \"sigilContinue.noInterpol\": [\n      [/(^|[^\\\\])\\\\@sigilEndDelimiter/, \"sigil\"],\n      [\n        /(@sigilEndDelimiter)@sigilModifiers/,\n        {\n          cases: {\n            \"$1==$S5\": { token: \"sigil.delimiter\", next: \"@pop\" },\n            \"@default\": \"sigil\"\n          }\n        }\n      ],\n      { include: \"@sigilContent\" }\n    ],\n    attributes: [\n      [\n        /\\@(module|type)?doc (~[sS])?\"\"\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'''/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedHeredocDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?\"/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@doubleQuotedStringDocstring\"\n        }\n      ],\n      [\n        /\\@(module|type)?doc (~[sS])?'/,\n        {\n          token: \"comment.block.documentation\",\n          next: \"@singleQuotedStringDocstring\"\n        }\n      ],\n      [/\\@(module|type)?doc false/, \"comment.block.documentation\"],\n      [/\\@(@variableName)/, \"variable\"]\n    ],\n    doubleQuotedHeredocDocstring: [\n      [/\"\"\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedHeredocDocstring: [\n      [/'''/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    doubleQuotedStringDocstring: [\n      [/\"/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    singleQuotedStringDocstring: [\n      [/'/, { token: \"comment.block.documentation\", next: \"@pop\" }],\n      { include: \"@docstringContent\" }\n    ],\n    symbols: [\n      [/\\?(\\\\.|[^\\\\\\s])/, \"number.constant\"],\n      [/&\\d+/, \"operator\"],\n      [/<<<|>>>/, \"operator\"],\n      [/[()\\[\\]\\{\\}]|<<|>>/, \"@brackets\"],\n      [/\\.\\.\\./, \"identifier\"],\n      [/=>/, \"punctuation\"],\n      [/@operator/, \"operator\"],\n      [/[:;,.%]/, \"punctuation\"]\n    ],\n    stringContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringContent\" }\n    ],\n    stringContent: [[/./, \"string\"]],\n    stringConstantContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@stringConstantContent\" }\n    ],\n    stringConstantContent: [[/./, \"constant\"]],\n    regexpContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@regexpContent\" }\n    ],\n    regexpContent: [\n      [/(\\s)(#)(\\s.*)$/, [\"white\", \"comment.punctuation\", \"comment\"]],\n      [/./, \"regexp\"]\n    ],\n    sigilContentInterpol: [\n      { include: \"@interpolation\" },\n      { include: \"@escapeChar\" },\n      { include: \"@sigilContent\" }\n    ],\n    sigilContent: [[/./, \"sigil\"]],\n    docstringContent: [[/./, \"comment.block.documentation\"]],\n    escapeChar: [[/@escape/, \"constant.character.escape\"]],\n    interpolation: [[/#{/, { token: \"delimiter.bracket.embed\", next: \"@interpolationContinue\" }]],\n    interpolationContinue: [\n      [/}/, { token: \"delimiter.bracket.embed\", next: \"@pop\" }],\n      { include: \"@root\" }\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,SAAS,EAAE;AAAA,IAC5C,EAAE,MAAM,OAAO,OAAO,MAAM;AAAA,IAC5B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAAA,IAChB,uBAAuB;AAAA,IACvB,uBAAuB;AAAA,EACzB;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,MAAM,OAAO,MAAM,OAAO,0BAA0B;AAAA,EAC9D;AAAA,EACA,qBAAqB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC,OAAO,MAAM,OAAO,MAAM,MAAM;AAAA,EACnD,mBAAmB,CAAC,SAAS,UAAU,WAAW,KAAK;AAAA,EACvD,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,SAAS,KAAK;AAAA,EAClC,aAAa,CAAC,cAAc,WAAW,WAAW,cAAc,gBAAgB;AAAA,EAChF,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,qBAAqB;AAAA,MAChC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,eAAe;AAAA,MAC1B,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,SAAS;AAAA,MACpB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,IACxB;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU,CAAC,CAAC,WAAW,CAAC,uBAAuB,SAAS,CAAC,CAAC;AAAA,IAC1D,mBAAmB;AAAA,MACjB,CAAC,uBAAuB,CAAC,YAAY,wBAAwB,OAAO,CAAC;AAAA,MACrE;AAAA,QACE;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACpE;AAAA,MACA;AAAA,QACE;AAAA,QACA,EAAE,OAAO,sBAAsB,MAAM,6BAA6B;AAAA,MACpE;AAAA,IACF;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,2BAA2B;AAAA,MACzB,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACpD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,SAAS;AAAA,MACP,CAAC,aAAa,eAAe;AAAA,MAC7B,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,uCAAuC,cAAc;AAAA,MACtD,CAAC,YAAY,QAAQ;AAAA,IACvB;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,wBAAwB;AAAA,YACxB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,mBAAmB,SAAS,YAAY,SAAS,eAAe;AAAA,MACnE;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,wBAAwB,YAAY,SAAS,YAAY,SAAS,eAAe;AAAA,MACpF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL,kBAAkB;AAAA,cAClB,YAAY;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,YAAY,SAAS,eAAe;AAAA,MACvC;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,wBAAwB;AAAA,YACxB,qBAAqB;AAAA,YACrB,sBAAsB;AAAA,YACtB,kBAAkB;AAAA,YAClB,cAAc;AAAA,YACd,gBAAgB;AAAA,YAChB,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,eAAe,iBAAiB;AAAA,IACnC;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MACnE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,MAChE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,sBAAsB,CAAC;AAAA,IAClE;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,MACjD,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,OAAO;AAAA,MACL,CAAC,kBAAkB,CAAC,wBAAwB,UAAU,CAAC;AAAA,MACvD,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,MACvE,CAAC,MAAM,EAAE,OAAO,sBAAsB,MAAM,0BAA0B,CAAC;AAAA,IACzE;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,wBAAwB;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,sBAAsB,MAAM,OAAO,CAAC;AAAA,MACnD,EAAE,SAAS,iCAAiC;AAAA,IAC9C;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,8BAA8B,EAAE,OAAO,YAAY,MAAM,kBAAkB,CAAC;AAAA,MAC7E,CAAC,iCAAiC,EAAE,OAAO,YAAY,MAAM,oBAAoB,CAAC;AAAA,IACpF;AAAA,IACA,OAAO;AAAA,MACL,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E,CAAC,qBAAqB,EAAE,OAAO,YAAY,UAAU,yBAAyB,CAAC;AAAA,MAC/E;AAAA,QACE;AAAA,QACA,EAAE,OAAO,YAAY,UAAU,2BAA2B;AAAA,MAC5D;AAAA,IACF;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,2BAA2B;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,iCAAiC,QAAQ;AAAA,MAC1C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,oBAAoB,MAAM,OAAO;AAAA,YACrD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,uBAAuB;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,0BAA0B;AAAA,MACxB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,wBAAwB;AAAA,IACrC;AAAA,IACA,yBAAyB;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,4BAA4B;AAAA,MAC1B,CAAC,iCAAiC,OAAO;AAAA,MACzC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,mBAAmB,MAAM,OAAO;AAAA,YACpD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF;AAAA,MACA,CAAC,6BAA6B,6BAA6B;AAAA,MAC3D,CAAC,qBAAqB,UAAU;AAAA,IAClC;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC9D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,8BAA8B;AAAA,MAC5B,CAAC,OAAO,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC9D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,6BAA6B;AAAA,MAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC5D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,6BAA6B;AAAA,MAC3B,CAAC,KAAK,EAAE,OAAO,+BAA+B,MAAM,OAAO,CAAC;AAAA,MAC5D,EAAE,SAAS,oBAAoB;AAAA,IACjC;AAAA,IACA,SAAS;AAAA,MACP,CAAC,mBAAmB,iBAAiB;AAAA,MACrC,CAAC,QAAQ,UAAU;AAAA,MACnB,CAAC,WAAW,UAAU;AAAA,MACtB,CAAC,sBAAsB,WAAW;AAAA,MAClC,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,MAAM,aAAa;AAAA,MACpB,CAAC,aAAa,UAAU;AAAA,MACxB,CAAC,WAAW,aAAa;AAAA,IAC3B;AAAA,IACA,uBAAuB;AAAA,MACrB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,eAAe,CAAC,CAAC,KAAK,QAAQ,CAAC;AAAA,IAC/B,+BAA+B;AAAA,MAC7B,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,yBAAyB;AAAA,IACtC;AAAA,IACA,uBAAuB,CAAC,CAAC,KAAK,UAAU,CAAC;AAAA,IACzC,uBAAuB;AAAA,MACrB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,eAAe;AAAA,MACb,CAAC,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,CAAC;AAAA,MAC9D,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,sBAAsB;AAAA,MACpB,EAAE,SAAS,iBAAiB;AAAA,MAC5B,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA,cAAc,CAAC,CAAC,KAAK,OAAO,CAAC;AAAA,IAC7B,kBAAkB,CAAC,CAAC,KAAK,6BAA6B,CAAC;AAAA,IACvD,YAAY,CAAC,CAAC,WAAW,2BAA2B,CAAC;AAAA,IACrD,eAAe,CAAC,CAAC,MAAM,EAAE,OAAO,2BAA2B,MAAM,yBAAyB,CAAC,CAAC;AAAA,IAC5F,uBAAuB;AAAA,MACrB,CAAC,KAAK,EAAE,OAAO,2BAA2B,MAAM,OAAO,CAAC;AAAA,MACxD,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,EACF;AACF;", "names": []}