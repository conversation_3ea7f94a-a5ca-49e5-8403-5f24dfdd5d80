{"version": 3, "names": ["SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "NON_DECIMAL_LITERAL", "PURE_ANNOTATION_RE", "needsParens", "needsWhitespaceAfter", "needsWhitespaceBefore", "n", "Printer", "constructor", "format", "map", "inForStatementInitCounter", "_printStack", "_indent", "_indentChar", "_indentRepeat", "_insideAux", "_parenPushNewlineState", "_noLineTerminator", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_buf", "<PERSON><PERSON><PERSON>", "indent", "style", "charCodeAt", "length", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "minified", "removeLastSemicolon", "token", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "endsWith", "_append", "number", "Number", "isInteger", "test", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "charBeforeNewline", "endsWithCharAndNewline", "j", "_newline", "removeTrailingNewline", "exactSource", "loc", "cb", "_catchUp", "source", "prop", "withSource", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_maybeIndent", "append", "_maybeAddParenChar", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "parenPushNewlineState", "printed", "len", "cha", "chaPost", "slice", "pos", "line", "count", "getCurrentLine", "printTerminatorless", "node", "parent", "isLabel", "terminatorState", "noLineTerminator", "nodeType", "type", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "push", "oldInAux", "shouldPrintParens", "retainFunctionParens", "extra", "parenthesized", "_printLeadingComments", "bind", "_printTrailingComments", "pop", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "value", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "newlineOpts", "addNewlines", "statement", "_printNewline", "iterator", "separator", "call", "printAndIndentOnComments", "leadingComments", "printBlock", "body", "_printComments", "_getComments", "printInnerComments", "innerComments", "printSequence", "printList", "items", "commaSeparator", "leading", "lines", "needs", "Math", "min", "trailingComments", "skipNewLines", "ignore", "has", "shouldPrintComment", "add", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "offset", "start", "column", "newlineRegex", "RegExp", "replace", "indentSize", "max", "getCurrentColumn", "repeat", "comments", "inlinePureAnnotation", "printAssertions", "assertions", "Object", "assign", "prototype", "generatorFunctions", "Noop"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer from \"./buffer\";\nimport type { Loc } from \"./buffer\";\nimport * as n from \"./node\";\nimport type * as t from \"@babel/types\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport * as generatorFunctions from \"./generators\";\nimport type SourceMap from \"./source-map\";\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst NON_DECIMAL_LITERAL = /^0[box]/;\nconst PURE_ANNOTATION_RE = /^\\s*[@#]__PURE__\\s*$/;\n\nconst { needsParens, needsWhitespaceAfter, needsWhitespaceBefore } = n;\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  recordAndTupleSyntaxType: RecordAndTuplePluginOptions[\"syntaxType\"];\n  jsescOption: jsescOptions;\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\ntype PrintJoinOptions = PrintListOptions &\n  PrintSequenceOptions & {\n    iterator?: (node: t.Node, index: number) => void;\n  };\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n    this._buf = new Buffer(map);\n\n    this._indentChar = format.indent.style.charCodeAt(0);\n    this._indentRepeat = format.indent.style.length;\n  }\n\n  declare format: Format;\n  inForStatementInitCounter: number = 0;\n\n  declare _buf: Buffer;\n  _printStack: Array<t.Node> = [];\n  _indent: number = 0;\n  _indentChar: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _parenPushNewlineState: { printed: boolean } | null = null;\n  _noLineTerminator: boolean = false;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.token(\"}\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string): void {\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (str.charCodeAt(0) === charCodes.slash && this.endsWith(charCodes.slash))\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string): void {\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(+str) &&\n      !NON_DECIMAL_LITERAL.test(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n\n  token(str: string, maybeNewline = false): void {\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark && str === \"--\") ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n  }\n\n  tokenChar(char: number): void {\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   */\n\n  newline(i: number = 1): void {\n    if (this.format.retainLines || this.format.compact) return;\n\n    if (this.format.concise) {\n      this.space();\n      return;\n    }\n\n    const charBeforeNewline = this.endsWithCharAndNewline();\n    // never allow more than two lines\n    if (charBeforeNewline === charCodes.lineFeed) return;\n\n    if (\n      charBeforeNewline === charCodes.leftCurlyBrace ||\n      charBeforeNewline === charCodes.colon\n    ) {\n      i--;\n    }\n    if (i <= 0) return;\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  withSource(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    cb: () => void,\n  ): void {\n    this._catchUp(prop, loc);\n\n    this._buf.withSource(prop, loc, cb);\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeAddParen(str);\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _queue(char: number) {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._indentChar, this._getIndent());\n    }\n  }\n\n  _maybeAddParenChar(char: number): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    if (char === charCodes.space) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    if (char !== charCodes.lineFeed) {\n      this._parenPushNewlineState = null;\n      return;\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _maybeAddParen(str: string): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    const len = str.length;\n\n    let i;\n    for (i = 0; i < len && str.charCodeAt(i) === charCodes.space; i++) continue;\n    if (i === len) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    const cha = str.charCodeAt(i);\n    if (cha !== charCodes.lineFeed) {\n      if (\n        // This is not a comment (it doesn't start with /)\n        cha !== charCodes.slash ||\n        // This is not a comment (it's a / operator)\n        i + 1 === len\n      ) {\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n\n      const chaPost = str.charCodeAt(i + 1);\n\n      if (chaPost === charCodes.asterisk) {\n        // This is a block comment\n\n        if (PURE_ANNOTATION_RE.test(str.slice(i + 2, len - 2))) {\n          // We avoid printing newlines after #__PURE__ comments (we treat\n          // then as unary operators), but we must keep the old\n          // parenPushNewlineState because, if a newline was forbidden, it is\n          // still forbidden after the comment.\n          return;\n        }\n\n        // NOTE: code flow continues from here to after these if/elses\n      } else if (chaPost !== charCodes.slash) {\n        // This is neither a block comment, nor a line comment.\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc ? loc[prop] : null;\n    if (pos?.line != null) {\n      const count = pos.line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  printTerminatorless(node: t.Node, parent: t.Node, isLabel: boolean) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    if (isLabel) {\n      this._noLineTerminator = true;\n      this.print(node, parent);\n      this._noLineTerminator = false;\n    } else {\n      const terminatorState = {\n        printed: false,\n      };\n      this._parenPushNewlineState = terminatorState;\n      this.print(node, parent);\n      /**\n       * Print an ending parentheses if a starting one has been printed.\n       */\n      if (terminatorState.printed) {\n        this.dedent();\n        this.newline();\n        this.token(\")\");\n      }\n    }\n  }\n\n  print(node: t.Node | null, parent?: t.Node, noLineTerminator?: boolean) {\n    if (!node) return;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    this._printStack.push(node);\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == undefined;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    let shouldPrintParens: boolean;\n    if (\n      format.retainFunctionParens &&\n      nodeType === \"FunctionExpression\" &&\n      node.extra &&\n      node.extra.parenthesized\n    ) {\n      shouldPrintParens = true;\n    } else {\n      shouldPrintParens = needsParens(node, parent, this._printStack);\n    }\n    if (shouldPrintParens) this.token(\"(\");\n\n    this._printLeadingComments(node);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.withSource(\"start\", loc, printMethod.bind(this, node, parent));\n\n    if (noLineTerminator && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node);\n      this._noLineTerminator = false;\n    } else {\n      this._printTrailingComments(node);\n    }\n\n    if (shouldPrintParens) this.token(\")\");\n\n    // end\n    this._printStack.pop();\n\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment({\n        type: \"CommentBlock\",\n        value: comment,\n      });\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment({\n        type: \"CommentBlock\",\n        value: comment,\n      });\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DecimalLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra &&\n      extra.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    parent: t.Node,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    if (opts.indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n    };\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(true, node, parent, newlineOpts);\n\n      this.print(node, parent);\n\n      if (opts.iterator) {\n        opts.iterator(node, i);\n      }\n\n      if (opts.separator && i < len - 1) {\n        opts.separator.call(this);\n      }\n\n      if (opts.statement) this._printNewline(false, node, parent, newlineOpts);\n    }\n\n    if (opts.indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node, parent: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node, parent);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node, parent);\n  }\n\n  _printTrailingComments(node: t.Node) {\n    this._printComments(this._getComments(false, node));\n  }\n\n  _printLeadingComments(node: t.Node) {\n    this._printComments(\n      this._getComments(true, node),\n      // Don't add leading/trailing new lines to #__PURE__ annotations\n      true,\n    );\n  }\n\n  printInnerComments(node: t.Node, indent = true) {\n    if (!node.innerComments?.length) return;\n    if (indent) this.indent();\n    this._printComments(node.innerComments);\n    if (indent) this.dedent();\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    parent: t.Node,\n    opts: PrintSequenceOptions = {},\n  ) {\n    opts.statement = true;\n    return this.printJoin(nodes, parent, opts);\n  }\n\n  printList(items: t.Node[], parent: t.Node, opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    return this.printJoin(items, parent, opts);\n  }\n\n  _printNewline(\n    leading: boolean,\n    node: t.Node,\n    parent: t.Node,\n    opts: AddNewlinesOptions,\n  ) {\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (this.format.retainLines || this.format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (this.format.concise) {\n      this.space();\n      return;\n    }\n\n    let lines = 0;\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      if (!leading) lines++; // always include at least a single line after\n      if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      if (needs(node, parent)) lines++;\n    }\n\n    this.newline(Math.min(2, lines));\n  }\n\n  _getComments(leading: boolean, node: t.Node) {\n    // Note, we use a boolean flag here instead of passing in the attribute name as it is faster\n    // because this is called extremely frequently.\n    return (\n      (node && (leading ? node.leadingComments : node.trailingComments)) || null\n    );\n  }\n\n  _printComment(comment: t.Comment, skipNewLines?: boolean) {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return;\n\n    if (this._printedComments.has(comment)) return;\n\n    if (!this.format.shouldPrintComment(comment.value)) return;\n\n    this._printedComments.add(comment);\n\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment && !skipNewLines && !this._noLineTerminator;\n\n    if (printNewLines && this._buf.hasContent()) this.newline(1);\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace\n    ) {\n      this.space();\n    }\n\n    let val;\n    let maybeNewline = false;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n\n        const indentSize = Math.max(\n          this._getIndent(),\n          this.format.retainLines ? 0 : this._buf.getCurrentColumn(),\n        );\n        val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n\n        maybeNewline = true;\n      }\n    } else if (!this._noLineTerminator) {\n      val = `//${comment.value}\\n`;\n      maybeNewline = true;\n    } else {\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid creating //* comments\n    if (this.endsWith(charCodes.slash)) this._space();\n\n    this.withSource(\n      \"start\",\n      comment.loc,\n      this._append.bind(this, val, maybeNewline),\n    );\n\n    if (printNewLines) this.newline(1);\n  }\n\n  _printComments(\n    comments?: readonly t.Comment[],\n    inlinePureAnnotation?: boolean,\n  ) {\n    if (!comments?.length) return;\n\n    if (\n      inlinePureAnnotation &&\n      comments.length === 1 &&\n      PURE_ANNOTATION_RE.test(comments[0].value)\n    ) {\n      this._printComment(\n        comments[0],\n        // Keep newlines if the comment marks a standalone call\n        this._buf.hasContent() && !this.endsWith(charCodes.lineFeed),\n      );\n    } else {\n      for (const comment of comments) {\n        this._printComment(comment);\n      }\n    }\n  }\n  // todo(flow->ts): was Node\n  printAssertions(node: Extract<t.Node, { assertions?: t.ImportAttribute[] }>) {\n    if (node.assertions?.length) {\n      this.space();\n      this.word(\"assert\");\n      this.space();\n      this.token(\"{\");\n      this.space();\n      this.printList(node.assertions, node);\n      this.space();\n      this.token(\"}\");\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;;AAAA;;AAEA;;AAQA;;AAIA,MAAMA,mBAAmB,GAAG,IAA5B;AACA,MAAMC,oBAAoB,GAAG,OAA7B;AACA,MAAMC,mBAAmB,GAAG,SAA5B;AACA,MAAMC,kBAAkB,GAAG,sBAA3B;AAEA,MAAM;EAAEC,WAAF;EAAeC,oBAAf;EAAqCC;AAArC,IAA+DC,CAArE;;AAiDA,MAAMC,OAAN,CAAc;EACZC,WAAW,CAACC,MAAD,EAAiBC,GAAjB,EAAiC;IAAA,KAS5CC,yBAT4C,GASR,CATQ;IAAA,KAY5CC,WAZ4C,GAYf,EAZe;IAAA,KAa5CC,OAb4C,GAa1B,CAb0B;IAAA,KAc5CC,WAd4C,GActB,CAdsB;IAAA,KAe5CC,aAf4C,GAepB,CAfoB;IAAA,KAgB5CC,UAhB4C,GAgBtB,KAhBsB;IAAA,KAiB5CC,sBAjB4C,GAiBU,IAjBV;IAAA,KAkB5CC,iBAlB4C,GAkBf,KAlBe;IAAA,KAmB5CC,4BAnB4C,GAmBJ,KAnBI;IAAA,KAoB5CC,gBApB4C,GAoBzB,IAAIC,GAAJ,EApByB;IAAA,KAqB5CC,gBArB4C,GAqBzB,KArByB;IAAA,KAsB5CC,aAtB4C,GAsB5B,KAtB4B;IAC1C,KAAKd,MAAL,GAAcA,MAAd;IACA,KAAKe,IAAL,GAAY,IAAIC,eAAJ,CAAWf,GAAX,CAAZ;IAEA,KAAKI,WAAL,GAAmBL,MAAM,CAACiB,MAAP,CAAcC,KAAd,CAAoBC,UAApB,CAA+B,CAA/B,CAAnB;IACA,KAAKb,aAAL,GAAqBN,MAAM,CAACiB,MAAP,CAAcC,KAAd,CAAoBE,MAAzC;EACD;;EAkBDC,QAAQ,CAACC,GAAD,EAAc;IACpB,KAAKC,KAAL,CAAWD,GAAX;;IACA,KAAKE,mBAAL;;IAEA,OAAO,KAAKT,IAAL,CAAUU,GAAV,EAAP;EACD;;EAMDR,MAAM,GAAS;IACb,IAAI,KAAKjB,MAAL,CAAY0B,OAAZ,IAAuB,KAAK1B,MAAL,CAAY2B,OAAvC,EAAgD;IAEhD,KAAKvB,OAAL;EACD;;EAMDwB,MAAM,GAAS;IACb,IAAI,KAAK5B,MAAL,CAAY0B,OAAZ,IAAuB,KAAK1B,MAAL,CAAY2B,OAAvC,EAAgD;IAEhD,KAAKvB,OAAL;EACD;;EAMDyB,SAAS,CAACC,KAAc,GAAG,KAAlB,EAA+B;IACtC,KAAKN,mBAAL;;IACA,IAAIM,KAAJ,EAAW;MACT,KAAKC,WAAL;IACD,CAFD,MAEO;MACL,KAAKC,MAAL;IACD;EACF;;EAMDC,UAAU,GAAS;IACjB,IAAI,KAAKjC,MAAL,CAAYkC,QAAhB,EAA0B;MACxB,KAAKnB,IAAL,CAAUoB,mBAAV;IACD;;IACD,KAAKC,SAAL;EACD;;EAMDC,KAAK,CAACP,KAAc,GAAG,KAAlB,EAA+B;IAClC,IAAI,KAAK9B,MAAL,CAAY0B,OAAhB,EAAyB;;IAEzB,IAAII,KAAJ,EAAW;MACT,KAAKQ,MAAL;IACD,CAFD,MAEO,IAAI,KAAKvB,IAAL,CAAUwB,UAAV,EAAJ,EAA4B;MACjC,MAAMC,MAAM,GAAG,KAAKC,WAAL,EAAf;;MACA,IAAID,MAAM,OAAN,IAA8BA,MAAM,OAAxC,EAAiE;QAC/D,KAAKF,MAAL;MACD;IACF;EACF;;EAMDI,IAAI,CAACC,GAAD,EAAoB;IAEtB,IACE,KAAK7B,aAAL,IACC6B,GAAG,CAACxB,UAAJ,CAAe,CAAf,YAAyC,KAAKyB,QAAL,IAF5C,EAGE;MACA,KAAKN,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKqB,OAAL,CAAaF,GAAb,EAAkB,KAAlB;;IAEA,KAAK7B,aAAL,GAAqB,IAArB;EACD;;EAMDgC,MAAM,CAACH,GAAD,EAAoB;IACxB,KAAKD,IAAL,CAAUC,GAAV;IAIA,KAAK9B,gBAAL,GACEkC,MAAM,CAACC,SAAP,CAAiB,CAACL,GAAlB,KACA,CAACnD,mBAAmB,CAACyD,IAApB,CAAyBN,GAAzB,CADD,IAEA,CAACrD,mBAAmB,CAAC2D,IAApB,CAAyBN,GAAzB,CAFD,IAGA,CAACpD,oBAAoB,CAAC0D,IAArB,CAA0BN,GAA1B,CAHD,IAIAA,GAAG,CAACxB,UAAJ,CAAewB,GAAG,CAACvB,MAAJ,GAAa,CAA5B,QALF;EAMD;;EAMDgB,KAAK,CAACO,GAAD,EAAcO,YAAY,GAAG,KAA7B,EAA0C;IAG7C,MAAMC,QAAQ,GAAG,KAAKV,WAAL,EAAjB;IACA,MAAMW,QAAQ,GAAGT,GAAG,CAACxB,UAAJ,CAAe,CAAf,CAAjB;;IACA,IACGgC,QAAQ,OAAR,IAA0CR,GAAG,KAAK,IAAnD,IAECS,QAAQ,OAAR,IAAmCD,QAAQ,OAF5C,IAGCC,QAAQ,OAAR,IAA+BD,QAAQ,OAHxC,IAKCC,QAAQ,OAAR,IAA8B,KAAKvC,gBANtC,EAOE;MACA,KAAKyB,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKqB,OAAL,CAAaF,GAAb,EAAkBO,YAAlB;EACD;;EAEDG,SAAS,CAACC,IAAD,EAAqB;IAG5B,MAAMH,QAAQ,GAAG,KAAKV,WAAL,EAAjB;;IACA,IAEGa,IAAI,OAAJ,IAA+BH,QAAQ,OAAxC,IACCG,IAAI,OAAJ,IAA2BH,QAAQ,OADpC,IAGCG,IAAI,OAAJ,IAA0B,KAAKzC,gBALlC,EAME;MACA,KAAKyB,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKO,WAAL,CAAiBuB,IAAjB;EACD;;EAMDC,OAAO,CAACC,CAAS,GAAG,CAAb,EAAsB;IAC3B,IAAI,KAAKxD,MAAL,CAAYyD,WAAZ,IAA2B,KAAKzD,MAAL,CAAY0B,OAA3C,EAAoD;;IAEpD,IAAI,KAAK1B,MAAL,CAAY2B,OAAhB,EAAyB;MACvB,KAAKU,KAAL;MACA;IACD;;IAED,MAAMqB,iBAAiB,GAAG,KAAKC,sBAAL,EAA1B;IAEA,IAAID,iBAAiB,OAArB,EAA8C;;IAE9C,IACEA,iBAAiB,QAAjB,IACAA,iBAAiB,OAFnB,EAGE;MACAF,CAAC;IACF;;IACD,IAAIA,CAAC,IAAI,CAAT,EAAY;;IAEZ,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,CAApB,EAAuBI,CAAC,EAAxB,EAA4B;MAC1B,KAAKC,QAAL;IACD;EACF;;EAEDjB,QAAQ,CAACU,IAAD,EAAwB;IAC9B,OAAO,KAAKb,WAAL,OAAuBa,IAA9B;EACD;;EAEDb,WAAW,GAAW;IACpB,OAAO,KAAK1B,IAAL,CAAU0B,WAAV,EAAP;EACD;;EAEDkB,sBAAsB,GAAW;IAC/B,OAAO,KAAK5C,IAAL,CAAU4C,sBAAV,EAAP;EACD;;EAEDG,qBAAqB,GAAS;IAC5B,KAAK/C,IAAL,CAAU+C,qBAAV;EACD;;EAEDC,WAAW,CAACC,GAAD,EAAuBC,EAAvB,EAAuC;IAChD,KAAKC,QAAL,CAAc,OAAd,EAAuBF,GAAvB;;IAEA,KAAKjD,IAAL,CAAUgD,WAAV,CAAsBC,GAAtB,EAA2BC,EAA3B;EACD;;EAEDE,MAAM,CAACC,IAAD,EAAwBJ,GAAxB,EAAoD;IACxD,KAAKE,QAAL,CAAcE,IAAd,EAAoBJ,GAApB;;IAEA,KAAKjD,IAAL,CAAUoD,MAAV,CAAiBC,IAAjB,EAAuBJ,GAAvB;EACD;;EAEDK,UAAU,CACRD,IADQ,EAERJ,GAFQ,EAGRC,EAHQ,EAIF;IACN,KAAKC,QAAL,CAAcE,IAAd,EAAoBJ,GAApB;;IAEA,KAAKjD,IAAL,CAAUsD,UAAV,CAAqBD,IAArB,EAA2BJ,GAA3B,EAAgCC,EAAhC;EACD;;EAED3B,MAAM,GAAS;IACb,KAAKN,MAAL;EACD;;EAED6B,QAAQ,GAAS;IACf,KAAK7B,MAAL;EACD;;EAEDa,OAAO,CAACF,GAAD,EAAcO,YAAd,EAA2C;IAChD,KAAKoB,cAAL,CAAoB3B,GAApB;;IACA,KAAK4B,YAAL,CAAkB5B,GAAG,CAACxB,UAAJ,CAAe,CAAf,CAAlB;;IAEA,KAAKJ,IAAL,CAAUyD,MAAV,CAAiB7B,GAAjB,EAAsBO,YAAtB;;IAEA,KAAKpC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAEDkB,WAAW,CAACuB,IAAD,EAAqB;IAC9B,KAAKmB,kBAAL,CAAwBnB,IAAxB;;IACA,KAAKiB,YAAL,CAAkBjB,IAAlB;;IAEA,KAAKvC,IAAL,CAAU2D,UAAV,CAAqBpB,IAArB;;IAEA,KAAKxC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAEDmB,MAAM,CAACsB,IAAD,EAAe;IACnB,KAAKmB,kBAAL,CAAwBnB,IAAxB;;IACA,KAAKiB,YAAL,CAAkBjB,IAAlB;;IAEA,KAAKvC,IAAL,CAAU4D,KAAV,CAAgBrB,IAAhB;;IAEA,KAAKxC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAED0D,YAAY,CAACK,SAAD,EAA0B;IAEpC,IACE,KAAKxE,OAAL,IACAwE,SAAS,OADT,IAEA,KAAKhC,QAAL,IAHF,EAIE;MACA,KAAK7B,IAAL,CAAU8D,gBAAV,CAA2B,KAAKxE,WAAhC,EAA6C,KAAKyE,UAAL,EAA7C;IACD;EACF;;EAEDL,kBAAkB,CAACnB,IAAD,EAAqB;IAErC,MAAMyB,qBAAqB,GAAG,KAAKvE,sBAAnC;IACA,IAAI,CAACuE,qBAAL,EAA4B;;IAS5B,IAAIzB,IAAI,OAAR,EAA8B;MAE5B;IACD;;IAGD,IAAIA,IAAI,OAAR,EAAiC;MAC/B,KAAK9C,sBAAL,GAA8B,IAA9B;MACA;IACD;;IAED,KAAK4B,SAAL;IACA,KAAKnB,MAAL;IACA8D,qBAAqB,CAACC,OAAtB,GAAgC,IAAhC;EACD;;EAEDV,cAAc,CAAC3B,GAAD,EAAoB;IAEhC,MAAMoC,qBAAqB,GAAG,KAAKvE,sBAAnC;IACA,IAAI,CAACuE,qBAAL,EAA4B;IAS5B,MAAME,GAAG,GAAGtC,GAAG,CAACvB,MAAhB;IAEA,IAAIoC,CAAJ;;IACA,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGyB,GAAJ,IAAWtC,GAAG,CAACxB,UAAJ,CAAeqC,CAAf,QAAvB,EAA8DA,CAAC,EAA/D,EAAmE;;IACnE,IAAIA,CAAC,KAAKyB,GAAV,EAAe;MAEb;IACD;;IAGD,MAAMC,GAAG,GAAGvC,GAAG,CAACxB,UAAJ,CAAeqC,CAAf,CAAZ;;IACA,IAAI0B,GAAG,OAAP,EAAgC;MAC9B,IAEEA,GAAG,OAAH,IAEA1B,CAAC,GAAG,CAAJ,KAAUyB,GAJZ,EAKE;QAEA,KAAKzE,sBAAL,GAA8B,IAA9B;QACA;MACD;;MAED,MAAM2E,OAAO,GAAGxC,GAAG,CAACxB,UAAJ,CAAeqC,CAAC,GAAG,CAAnB,CAAhB;;MAEA,IAAI2B,OAAO,OAAX,EAAoC;QAGlC,IAAI1F,kBAAkB,CAACwD,IAAnB,CAAwBN,GAAG,CAACyC,KAAJ,CAAU5B,CAAC,GAAG,CAAd,EAAiByB,GAAG,GAAG,CAAvB,CAAxB,CAAJ,EAAwD;UAKtD;QACD;MAGF,CAZD,MAYO,IAAIE,OAAO,OAAX,EAAiC;QAGtC,KAAK3E,sBAAL,GAA8B,IAA9B;QACA;MACD;IACF;;IAED,KAAK4B,SAAL;IACA,KAAKnB,MAAL;IACA8D,qBAAqB,CAACC,OAAtB,GAAgC,IAAhC;EACD;;EAEDd,QAAQ,CAACE,IAAD,EAAwBJ,GAAxB,EAAmC;IACzC,IAAI,CAAC,KAAKhE,MAAL,CAAYyD,WAAjB,EAA8B;IAG9B,MAAM4B,GAAG,GAAGrB,GAAG,GAAGA,GAAG,CAACI,IAAD,CAAN,GAAe,IAA9B;;IACA,IAAI,CAAAiB,GAAG,QAAH,YAAAA,GAAG,CAAEC,IAAL,KAAa,IAAjB,EAAuB;MACrB,MAAMC,KAAK,GAAGF,GAAG,CAACC,IAAJ,GAAW,KAAKvE,IAAL,CAAUyE,cAAV,EAAzB;;MAEA,KAAK,IAAIhC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+B,KAApB,EAA2B/B,CAAC,EAA5B,EAAgC;QAC9B,KAAKK,QAAL;MACD;IACF;EACF;;EAMDiB,UAAU,GAAW;IACnB,OAAO,KAAKxE,aAAL,GAAqB,KAAKF,OAAjC;EACD;;EAEDqF,mBAAmB,CAACC,IAAD,EAAeC,MAAf,EAA+BC,OAA/B,EAAiD;IAgBlE,IAAIA,OAAJ,EAAa;MACX,KAAKnF,iBAAL,GAAyB,IAAzB;MACA,KAAKc,KAAL,CAAWmE,IAAX,EAAiBC,MAAjB;MACA,KAAKlF,iBAAL,GAAyB,KAAzB;IACD,CAJD,MAIO;MACL,MAAMoF,eAAe,GAAG;QACtBb,OAAO,EAAE;MADa,CAAxB;MAGA,KAAKxE,sBAAL,GAA8BqF,eAA9B;MACA,KAAKtE,KAAL,CAAWmE,IAAX,EAAiBC,MAAjB;;MAIA,IAAIE,eAAe,CAACb,OAApB,EAA6B;QAC3B,KAAKpD,MAAL;QACA,KAAK2B,OAAL;QACA,KAAKnB,SAAL;MACD;IACF;EACF;;EAEDb,KAAK,CAACmE,IAAD,EAAsBC,MAAtB,EAAuCG,gBAAvC,EAAmE;IACtE,IAAI,CAACJ,IAAL,EAAW;IAEX,MAAMK,QAAQ,GAAGL,IAAI,CAACM,IAAtB;IACA,MAAMhG,MAAM,GAAG,KAAKA,MAApB;IAEA,MAAMiG,UAAU,GAAGjG,MAAM,CAAC2B,OAA1B;;IACA,IAEE+D,IAAI,CAACQ,QAFP,EAGE;MACAlG,MAAM,CAAC2B,OAAP,GAAiB,IAAjB;IACD;;IAED,MAAMwE,WAAW,GACf,KACEJ,QADF,CADF;;IAUA,IAAII,WAAW,KAAKC,SAApB,EAA+B;MAC7B,MAAM,IAAIC,cAAJ,CACH,wBAAuBC,IAAI,CAACC,SAAL,CACtBR,QADsB,CAEtB,qBAAoBO,IAAI,CAACC,SAAL,CAAeb,IAAI,CAAC3F,WAAL,CAAiByG,IAAhC,CAAsC,EAHxD,CAAN;IAKD;;IAED,KAAKrG,WAAL,CAAiBsG,IAAjB,CAAsBf,IAAtB;;IAEA,MAAMgB,QAAQ,GAAG,KAAKnG,UAAtB;IACA,KAAKA,UAAL,GAAkBmF,IAAI,CAAC1B,GAAL,IAAYoC,SAA9B;;IACA,KAAK5E,mBAAL,CAAyB,KAAKjB,UAAL,IAAmB,CAACmG,QAA7C;;IAEA,IAAIC,iBAAJ;;IACA,IACE3G,MAAM,CAAC4G,oBAAP,IACAb,QAAQ,KAAK,oBADb,IAEAL,IAAI,CAACmB,KAFL,IAGAnB,IAAI,CAACmB,KAAL,CAAWC,aAJb,EAKE;MACAH,iBAAiB,GAAG,IAApB;IACD,CAPD,MAOO;MACLA,iBAAiB,GAAGjH,WAAW,CAACgG,IAAD,EAAOC,MAAP,EAAe,KAAKxF,WAApB,CAA/B;IACD;;IACD,IAAIwG,iBAAJ,EAAuB,KAAKvE,SAAL;;IAEvB,KAAK2E,qBAAL,CAA2BrB,IAA3B;;IAEA,MAAM1B,GAAG,GAAG+B,QAAQ,KAAK,SAAb,IAA0BA,QAAQ,KAAK,MAAvC,GAAgD,IAAhD,GAAuDL,IAAI,CAAC1B,GAAxE;IAEA,KAAKK,UAAL,CAAgB,OAAhB,EAAyBL,GAAzB,EAA8BmC,WAAW,CAACa,IAAZ,CAAiB,IAAjB,EAAuBtB,IAAvB,EAA6BC,MAA7B,CAA9B;;IAEA,IAAIG,gBAAgB,IAAI,CAAC,KAAKrF,iBAA9B,EAAiD;MAC/C,KAAKA,iBAAL,GAAyB,IAAzB;;MACA,KAAKwG,sBAAL,CAA4BvB,IAA5B;;MACA,KAAKjF,iBAAL,GAAyB,KAAzB;IACD,CAJD,MAIO;MACL,KAAKwG,sBAAL,CAA4BvB,IAA5B;IACD;;IAED,IAAIiB,iBAAJ,EAAuB,KAAKvE,SAAL;;IAGvB,KAAKjC,WAAL,CAAiB+G,GAAjB;;IAEAlH,MAAM,CAAC2B,OAAP,GAAiBsE,UAAjB;IACA,KAAK1F,UAAL,GAAkBmG,QAAlB;EACD;;EAEDlF,mBAAmB,CAAC2F,uBAAD,EAAoC;IACrD,IAAIA,uBAAJ,EAA6B,KAAKC,sBAAL;IAC7B,IAAI,CAAC,KAAK7G,UAAV,EAAsB,KAAK8G,qBAAL;EACvB;;EAEDD,sBAAsB,GAAG;IACvB,IAAI,KAAK1G,4BAAT,EAAuC;IACvC,KAAKA,4BAAL,GAAoC,IAApC;IAEA,MAAM4G,OAAO,GAAG,KAAKtH,MAAL,CAAYuH,sBAA5B;;IACA,IAAID,OAAJ,EAAa;MACX,KAAKE,aAAL,CAAmB;QACjBxB,IAAI,EAAE,cADW;QAEjByB,KAAK,EAAEH;MAFU,CAAnB;IAID;EACF;;EAEDD,qBAAqB,GAAG;IACtB,IAAI,CAAC,KAAK3G,4BAAV,EAAwC;IACxC,KAAKA,4BAAL,GAAoC,KAApC;IAEA,MAAM4G,OAAO,GAAG,KAAKtH,MAAL,CAAY0H,qBAA5B;;IACA,IAAIJ,OAAJ,EAAa;MACX,KAAKE,aAAL,CAAmB;QACjBxB,IAAI,EAAE,cADW;QAEjByB,KAAK,EAAEH;MAFU,CAAnB;IAID;EACF;;EAEDK,cAAc,CACZjC,IADY,EAQQ;IACpB,MAAMmB,KAAK,GAAGnB,IAAI,CAACmB,KAAnB;;IACA,IACEA,KAAK,IACLA,KAAK,CAACe,GAAN,IAAa,IADb,IAEAf,KAAK,CAACgB,QAAN,IAAkB,IAFlB,IAGAnC,IAAI,CAAC+B,KAAL,KAAeZ,KAAK,CAACgB,QAJvB,EAKE;MAEA,OAAOhB,KAAK,CAACe,GAAb;IACD;EACF;;EAEDE,SAAS,CACPC,KADO,EAEPpC,MAFO,EAGPqC,IAAsB,GAAG,EAHlB,EAIP;IACA,IAAI,EAACD,KAAD,YAACA,KAAK,CAAE3G,MAAR,CAAJ,EAAoB;IAEpB,IAAI4G,IAAI,CAAC/G,MAAT,EAAiB,KAAKA,MAAL;IAEjB,MAAMgH,WAA+B,GAAG;MACtCC,WAAW,EAAEF,IAAI,CAACE;IADoB,CAAxC;IAIA,MAAMjD,GAAG,GAAG8C,KAAK,CAAC3G,MAAlB;;IACA,KAAK,IAAIoC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyB,GAApB,EAAyBzB,CAAC,EAA1B,EAA8B;MAC5B,MAAMkC,IAAI,GAAGqC,KAAK,CAACvE,CAAD,CAAlB;MACA,IAAI,CAACkC,IAAL,EAAW;MAEX,IAAIsC,IAAI,CAACG,SAAT,EAAoB,KAAKC,aAAL,CAAmB,IAAnB,EAAyB1C,IAAzB,EAA+BC,MAA/B,EAAuCsC,WAAvC;MAEpB,KAAK1G,KAAL,CAAWmE,IAAX,EAAiBC,MAAjB;;MAEA,IAAIqC,IAAI,CAACK,QAAT,EAAmB;QACjBL,IAAI,CAACK,QAAL,CAAc3C,IAAd,EAAoBlC,CAApB;MACD;;MAED,IAAIwE,IAAI,CAACM,SAAL,IAAkB9E,CAAC,GAAGyB,GAAG,GAAG,CAAhC,EAAmC;QACjC+C,IAAI,CAACM,SAAL,CAAeC,IAAf,CAAoB,IAApB;MACD;;MAED,IAAIP,IAAI,CAACG,SAAT,EAAoB,KAAKC,aAAL,CAAmB,KAAnB,EAA0B1C,IAA1B,EAAgCC,MAAhC,EAAwCsC,WAAxC;IACrB;;IAED,IAAID,IAAI,CAAC/G,MAAT,EAAiB,KAAKW,MAAL;EAClB;;EAED4G,wBAAwB,CAAC9C,IAAD,EAAeC,MAAf,EAA+B;IACrD,MAAM1E,MAAM,GAAGyE,IAAI,CAAC+C,eAAL,IAAwB/C,IAAI,CAAC+C,eAAL,CAAqBrH,MAArB,GAA8B,CAArE;IACA,IAAIH,MAAJ,EAAY,KAAKA,MAAL;IACZ,KAAKM,KAAL,CAAWmE,IAAX,EAAiBC,MAAjB;IACA,IAAI1E,MAAJ,EAAY,KAAKW,MAAL;EACb;;EAED8G,UAAU,CAAC/C,MAAD,EAAiD;IACzD,MAAMD,IAAI,GAAGC,MAAM,CAACgD,IAApB;;IAEA,IAAIjD,IAAI,CAACM,IAAL,KAAc,gBAAlB,EAAoC;MAClC,KAAK3D,KAAL;IACD;;IAED,KAAKd,KAAL,CAAWmE,IAAX,EAAiBC,MAAjB;EACD;;EAEDsB,sBAAsB,CAACvB,IAAD,EAAe;IACnC,KAAKkD,cAAL,CAAoB,KAAKC,YAAL,CAAkB,KAAlB,EAAyBnD,IAAzB,CAApB;EACD;;EAEDqB,qBAAqB,CAACrB,IAAD,EAAe;IAClC,KAAKkD,cAAL,CACE,KAAKC,YAAL,CAAkB,IAAlB,EAAwBnD,IAAxB,CADF,EAGE,IAHF;EAKD;;EAEDoD,kBAAkB,CAACpD,IAAD,EAAezE,MAAM,GAAG,IAAxB,EAA8B;IAAA;;IAC9C,IAAI,yBAACyE,IAAI,CAACqD,aAAN,aAAC,oBAAoB3H,MAArB,CAAJ,EAAiC;IACjC,IAAIH,MAAJ,EAAY,KAAKA,MAAL;;IACZ,KAAK2H,cAAL,CAAoBlD,IAAI,CAACqD,aAAzB;;IACA,IAAI9H,MAAJ,EAAY,KAAKW,MAAL;EACb;;EAEDoH,aAAa,CACXjB,KADW,EAEXpC,MAFW,EAGXqC,IAA0B,GAAG,EAHlB,EAIX;IACAA,IAAI,CAACG,SAAL,GAAiB,IAAjB;IACA,OAAO,KAAKL,SAAL,CAAeC,KAAf,EAAsBpC,MAAtB,EAA8BqC,IAA9B,CAAP;EACD;;EAEDiB,SAAS,CAACC,KAAD,EAAkBvD,MAAlB,EAAkCqC,IAAsB,GAAG,EAA3D,EAA+D;IACtE,IAAIA,IAAI,CAACM,SAAL,IAAkB,IAAtB,EAA4B;MAC1BN,IAAI,CAACM,SAAL,GAAiBa,cAAjB;IACD;;IAED,OAAO,KAAKrB,SAAL,CAAeoB,KAAf,EAAsBvD,MAAtB,EAA8BqC,IAA9B,CAAP;EACD;;EAEDI,aAAa,CACXgB,OADW,EAEX1D,IAFW,EAGXC,MAHW,EAIXqC,IAJW,EAKX;IAEA,IAAI,KAAKhI,MAAL,CAAYyD,WAAZ,IAA2B,KAAKzD,MAAL,CAAY0B,OAA3C,EAAoD;;IAIpD,IAAI,KAAK1B,MAAL,CAAY2B,OAAhB,EAAyB;MACvB,KAAKU,KAAL;MACA;IACD;;IAED,IAAIgH,KAAK,GAAG,CAAZ;;IAEA,IAAI,KAAKtI,IAAL,CAAUwB,UAAV,EAAJ,EAA4B;MAC1B,IAAI,CAAC6G,OAAL,EAAcC,KAAK;MACnB,IAAIrB,IAAI,CAACE,WAAT,EAAsBmB,KAAK,IAAIrB,IAAI,CAACE,WAAL,CAAiBkB,OAAjB,EAA0B1D,IAA1B,KAAmC,CAA5C;MAEtB,MAAM4D,KAAK,GAAGF,OAAO,GAAGxJ,qBAAH,GAA2BD,oBAAhD;MACA,IAAI2J,KAAK,CAAC5D,IAAD,EAAOC,MAAP,CAAT,EAAyB0D,KAAK;IAC/B;;IAED,KAAK9F,OAAL,CAAagG,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYH,KAAZ,CAAb;EACD;;EAEDR,YAAY,CAACO,OAAD,EAAmB1D,IAAnB,EAAiC;IAG3C,OACGA,IAAI,KAAK0D,OAAO,GAAG1D,IAAI,CAAC+C,eAAR,GAA0B/C,IAAI,CAAC+D,gBAA3C,CAAL,IAAsE,IADxE;EAGD;;EAEDjC,aAAa,CAACF,OAAD,EAAqBoC,YAArB,EAA6C;IAGxD,IAAIpC,OAAO,CAACqC,MAAZ,EAAoB;IAEpB,IAAI,KAAKhJ,gBAAL,CAAsBiJ,GAAtB,CAA0BtC,OAA1B,CAAJ,EAAwC;IAExC,IAAI,CAAC,KAAKtH,MAAL,CAAY6J,kBAAZ,CAA+BvC,OAAO,CAACG,KAAvC,CAAL,EAAoD;;IAEpD,KAAK9G,gBAAL,CAAsBmJ,GAAtB,CAA0BxC,OAA1B;;IAEA,MAAMyC,cAAc,GAAGzC,OAAO,CAACtB,IAAR,KAAiB,cAAxC;IAIA,MAAMgE,aAAa,GACjBD,cAAc,IAAI,CAACL,YAAnB,IAAmC,CAAC,KAAKjJ,iBAD3C;IAGA,IAAIuJ,aAAa,IAAI,KAAKjJ,IAAL,CAAUwB,UAAV,EAArB,EAA6C,KAAKgB,OAAL,CAAa,CAAb;IAE7C,MAAM0G,YAAY,GAAG,KAAKxH,WAAL,EAArB;;IACA,IACEwH,YAAY,OAAZ,IACAA,YAAY,QAFd,EAGE;MACA,KAAK5H,KAAL;IACD;;IAED,IAAI6H,GAAJ;IACA,IAAIhH,YAAY,GAAG,KAAnB;;IACA,IAAI6G,cAAJ,EAAoB;MAClBG,GAAG,GAAI,KAAI5C,OAAO,CAACG,KAAM,IAAzB;;MACA,IAAI,KAAKzH,MAAL,CAAYiB,MAAZ,CAAmBkJ,sBAAvB,EAA+C;QAAA;;QAC7C,MAAMC,MAAM,mBAAG9C,OAAO,CAACtD,GAAX,qBAAG,aAAaqG,KAAb,CAAmBC,MAAlC;;QACA,IAAIF,MAAJ,EAAY;UACV,MAAMG,YAAY,GAAG,IAAIC,MAAJ,CAAW,cAAcJ,MAAd,GAAuB,GAAlC,EAAuC,GAAvC,CAArB;UACAF,GAAG,GAAGA,GAAG,CAACO,OAAJ,CAAYF,YAAZ,EAA0B,IAA1B,CAAN;QACD;;QAED,MAAMG,UAAU,GAAGnB,IAAI,CAACoB,GAAL,CACjB,KAAK7F,UAAL,EADiB,EAEjB,KAAK9E,MAAL,CAAYyD,WAAZ,GAA0B,CAA1B,GAA8B,KAAK1C,IAAL,CAAU6J,gBAAV,EAFb,CAAnB;QAIAV,GAAG,GAAGA,GAAG,CAACO,OAAJ,CAAY,UAAZ,EAAyB,KAAI,IAAII,MAAJ,CAAWH,UAAX,CAAuB,EAApD,CAAN;QAEAxH,YAAY,GAAG,IAAf;MACD;IACF,CAjBD,MAiBO,IAAI,CAAC,KAAKzC,iBAAV,EAA6B;MAClCyJ,GAAG,GAAI,KAAI5C,OAAO,CAACG,KAAM,IAAzB;MACAvE,YAAY,GAAG,IAAf;IACD,CAHM,MAGA;MACLgH,GAAG,GAAI,KAAI5C,OAAO,CAACG,KAAM,IAAzB;IACD;;IAGD,IAAI,KAAK7E,QAAL,IAAJ,EAAoC,KAAKN,MAAL;IAEpC,KAAK+B,UAAL,CACE,OADF,EAEEiD,OAAO,CAACtD,GAFV,EAGE,KAAKnB,OAAL,CAAamE,IAAb,CAAkB,IAAlB,EAAwBkD,GAAxB,EAA6BhH,YAA7B,CAHF;IAMA,IAAI8G,aAAJ,EAAmB,KAAKzG,OAAL,CAAa,CAAb;EACpB;;EAEDqF,cAAc,CACZkC,QADY,EAEZC,oBAFY,EAGZ;IACA,IAAI,EAACD,QAAD,YAACA,QAAQ,CAAE1J,MAAX,CAAJ,EAAuB;;IAEvB,IACE2J,oBAAoB,IACpBD,QAAQ,CAAC1J,MAAT,KAAoB,CADpB,IAEA3B,kBAAkB,CAACwD,IAAnB,CAAwB6H,QAAQ,CAAC,CAAD,CAAR,CAAYrD,KAApC,CAHF,EAIE;MACA,KAAKD,aAAL,CACEsD,QAAQ,CAAC,CAAD,CADV,EAGE,KAAK/J,IAAL,CAAUwB,UAAV,MAA0B,CAAC,KAAKK,QAAL,IAH7B;IAKD,CAVD,MAUO;MACL,KAAK,MAAM0E,OAAX,IAAsBwD,QAAtB,EAAgC;QAC9B,KAAKtD,aAAL,CAAmBF,OAAnB;MACD;IACF;EACF;;EAED0D,eAAe,CAACtF,IAAD,EAA8D;IAAA;;IAC3E,wBAAIA,IAAI,CAACuF,UAAT,aAAI,iBAAiB7J,MAArB,EAA6B;MAC3B,KAAKiB,KAAL;MACA,KAAKK,IAAL,CAAU,QAAV;MACA,KAAKL,KAAL;MACA,KAAKD,SAAL;MACA,KAAKC,KAAL;MACA,KAAK4G,SAAL,CAAevD,IAAI,CAACuF,UAApB,EAAgCvF,IAAhC;MACA,KAAKrD,KAAL;MACA,KAAKD,SAAL;IACD;EACF;;AApxBW;;AAwxBd8I,MAAM,CAACC,MAAP,CAAcrL,OAAO,CAACsL,SAAtB,EAAiCC,kBAAjC;AAEmC;EAEjCvL,OAAO,CAACsL,SAAR,CAAkBE,IAAlB,GAAyB,SAASA,IAAT,GAA6B,CAAE,CAAxD;AACD;eAIcxL,O;;;AAEf,SAASqJ,cAAT,GAAuC;EACrC,KAAK/G,SAAL;EACA,KAAKC,KAAL;AACD"}