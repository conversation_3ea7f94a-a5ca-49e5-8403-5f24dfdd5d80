import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Files, 
  Search, 
  GitBranch, 
  FolderOpen,
  FileText,
  Plus,
  MoreHorizontal
} from 'lucide-react';
import Sidebar from '../Sidebar'; // Import existing sidebar
import AIAgent from '../ai/AIAgent';

interface EnhancedSidebarProps {
  activeView: string;
  onCodeInsert?: (code: string, language: string) => void;
  currentFile?: string;
  selectedCode?: string;
}

const EnhancedSidebar: React.FC<EnhancedSidebarProps> = ({ 
  activeView, 
  onCodeInsert,
  currentFile,
  selectedCode 
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const renderExplorer = () => (
    <div className="h-full">
      <div className="p-3 border-b border-surface-tertiary">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Explorer</h3>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <Plus size={12} />
            </Button>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreHorizontal size={12} />
            </Button>
          </div>
        </div>
      </div>
      <div className="flex-1">
        <Sidebar />
      </div>
    </div>
  );

  const renderSearch = () => (
    <div className="h-full">
      <div className="p-3 border-b border-surface-tertiary">
        <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide mb-2">Search</h3>
        <Input
          placeholder="Search files..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="neon-input"
        />
      </div>
      <ScrollArea className="flex-1 p-3">
        <div className="text-sm text-gray-400">
          {searchQuery ? (
            <div>
              <p className="mb-2">Searching for: "{searchQuery}"</p>
              <div className="space-y-2">
                <div className="p-2 bg-surface-secondary rounded">
                  <div className="flex items-center space-x-2">
                    <FileText size={14} />
                    <span>example.ts</span>
                    <Badge variant="outline" className="text-xs">3 matches</Badge>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p>Enter search terms to find files and content</p>
          )}
        </div>
      </ScrollArea>
    </div>
  );

  const renderGit = () => (
    <div className="h-full">
      <div className="p-3 border-b border-surface-tertiary">
        <div className="flex items-center space-x-2 mb-2">
          <GitBranch size={16} className="text-neon-blue" />
          <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Source Control</h3>
        </div>
      </div>
      <ScrollArea className="flex-1 p-3">
        <div className="space-y-3">
          <div>
            <h4 className="text-xs font-medium text-gray-400 mb-2">CHANGES</h4>
            <div className="space-y-1">
              <div className="flex items-center space-x-2 p-1 hover:bg-surface-secondary rounded">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <FileText size={14} />
                <span className="text-sm">App.tsx</span>
                <Badge variant="outline" className="text-xs ml-auto">M</Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-xs font-medium text-gray-400 mb-2">STAGED CHANGES</h4>
            <p className="text-xs text-gray-500">No staged changes</p>
          </div>
        </div>
      </ScrollArea>
    </div>
  );

  const renderAI = () => (
    <AIAgent 
      onCodeInsert={onCodeInsert}
      currentFile={currentFile}
      selectedCode={selectedCode}
    />
  );

  const renderTerminal = () => (
    <div className="h-full">
      <div className="p-3 border-b border-surface-tertiary">
        <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Terminal</h3>
      </div>
      <div className="flex-1 p-3">
        <p className="text-sm text-gray-400">Terminal view - switch to bottom panel for full terminal</p>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="h-full">
      <div className="p-3 border-b border-surface-tertiary">
        <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Settings</h3>
      </div>
      <ScrollArea className="flex-1 p-3">
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-300 mb-2">Theme</h4>
            <div className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                Cyberpunk Dark (Current)
              </Button>
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-300 mb-2">Editor</h4>
            <div className="space-y-2">
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" defaultChecked className="rounded" />
                <span>Show minimap</span>
              </label>
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" defaultChecked className="rounded" />
                <span>Word wrap</span>
              </label>
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );

  const renderContent = () => {
    switch (activeView) {
      case 'explorer':
        return renderExplorer();
      case 'search':
        return renderSearch();
      case 'git':
        return renderGit();
      case 'ai':
        return renderAI();
      case 'terminal':
        return renderTerminal();
      case 'settings':
        return renderSettings();
      default:
        return renderExplorer();
    }
  };

  return (
    <div className="enhanced-sidebar w-80 h-full bg-surface-primary border-r border-surface-tertiary flex flex-col">
      {renderContent()}
    </div>
  );
};

export default EnhancedSidebar;
