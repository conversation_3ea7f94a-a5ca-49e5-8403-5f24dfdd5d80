import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';

const defaultColor = "#000000";
const SiSwisscows = React.forwardRef(function SiSwisscows2({ title = "Swisscows", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsx("title", { children: title }),
        /* @__PURE__ */ jsx("path", { d: "m6.591 24 .282-1.45c-.065-1.365-.279-2.682-.803-3.86.393-.62.534-1.683.647-2.793l-1.646-2.71c-.048.01-.29-.232-.414-.402l-.178.014c-2.661.191-2.711-2.508-2.336-4.563.366-1.395-1.782-.603-1.604-.69C.384 7.11.912 6.401 1.565 6.16c.243.205 1.32.383 1.023 2.016-.547.956-.069 5.358 2.807 3.28l.427-.623c1.247-.748 2.092-1.235 3.698-1.872.49-.624 1.552-1.964 1.804-2.208.06-.06.375-.321.818-.61.696-.909 1.407-2.59 1.34-3.29-.315.275-.648.576-1.059-.077-.324-.517.447-1.06 1.186-.969l.038-.044C13.133.984 14.74-.334 14.757.078c-.376.322-.737.69-.455 1.283.438-.34.823-.367 1.207-.223l.393.148c1.299.275 2.79-.919 2.818.023l.01.331c-.205.174-.46.332-.75.479.241.018.528-.06.76-.16l.008.302-.502.46c-.442.67-.985 1.327-1.641 1.783.018.101.015.291.013.527a5.747 5.747 0 0 0 2.243-.244l-.013-.022c-.212-.346.047-1.057.378-1.418.098-.108.298-.144.373-.177l1.038.95c-.024.145-.04.26-.115.343-.445.488-1.154.735-1.531.46-.635.74-1.36 1.253-2.253 1.477.016.062.035.121.056.179.125.151.225.318.292.504.092.185.166.359.227.526.632-.047 1.195-.021 2.117-.138l2.343.74 1.715.52-.994.54-.937-.46c-.73-.147-1.398.411-2.313-.13-.185.081-.397.198-.63.338.397.129.832.286 1.433.444l1.462 2.43.598 1.389-1.047-.258-.143-.904c-.685-1.147-1.351-1.054-1.98-1.493-.51.12-1.168.345-1.862.557-.367.685-.838 1.46-1.548 2.084l-2.51 1.248a1.815 1.815 0 0 1 .08.291c.352.16.722.454.395.624a.786.786 0 0 1-.415.065 2.015 2.015 0 0 1-.022.085c.265.232.465.535.146.617-.137.036-.29.01-.444-.039-.03.039-.062.076-.095.111.143.32.196.67-.125.614-.145-.025-.28-.118-.404-.233a2.155 2.155 0 0 1-.042.019c.046.371-.008.789-.313.616-.164-.093-.276-.283-.363-.481-.04 0-.079-.002-.118-.005-.27.67-.628 1.303-1.042 1.793l2.073.469.742.73-1.092.54-.396-.846c-.724.027-2.172.082-2.207.02a59.342 59.342 0 0 1-.496-1.435c-.285.242-.603.49-.958.744-.028 1.49-.382 2.68-.473 3.688.203.412.197.845.262 1.262zM17.883 5.432c-.542.068-.944.05-1.263-.02.005.227.02.47.058.706a5.206 5.206 0 0 0 1.205-.686z" })
      ]
    }
  );
});

export { SiSwisscows as default, defaultColor };
