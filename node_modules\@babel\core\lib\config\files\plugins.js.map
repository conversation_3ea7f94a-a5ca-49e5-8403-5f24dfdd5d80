{"version": 3, "names": ["debug", "buildDebug", "EXACT_RE", "BABEL_PLUGIN_PREFIX_RE", "BABEL_PRESET_PREFIX_RE", "BABEL_PLUGIN_ORG_RE", "BABEL_PRESET_ORG_RE", "OTHER_PLUGIN_ORG_RE", "OTHER_PRESET_ORG_RE", "OTHER_ORG_DEFAULT_RE", "resolvePlugin", "name", "dirname", "resolveStandardizedName", "resolvePreset", "loadPlugin", "filepath", "value", "requireModule", "loadPreset", "standardizeName", "type", "path", "isAbsolute", "isPreset", "replace", "resolveAlternativesHelper", "standardizedName", "error", "code", "message", "oppositeType", "tryRequireResolve", "id", "paths", "tryImportMetaResolve", "options", "importMetaResolve", "resolveStandardizedNameForRequire", "it", "res", "next", "done", "resolveStandardizedNameForImport", "parentUrl", "pathToFileURL", "join", "href", "fileURLToPath", "gens<PERSON>", "sync", "process", "cwd", "async", "supportsESM", "e", "e2", "LOADING_MODULES", "Set", "isAsync", "has", "Error", "add", "loadCjsOrMjsDefault", "err", "delete"], "sources": ["../../../src/config/files/plugins.ts"], "sourcesContent": ["/**\n * This file handles all logic for converting string-based configuration references into loaded objects.\n */\n\nimport buildDebug from \"debug\";\nimport path from \"path\";\nimport gensync, { type Handler } from \"gensync\";\nimport { isAsync } from \"../../gensync-utils/async\";\nimport loadCjsOrMjsDefault, { supportsESM } from \"./module-types\";\nimport { fileURLToPath, pathToFileURL } from \"url\";\n\nimport importMetaResolve from \"./import-meta-resolve\";\n\nimport { createRequire } from \"module\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:plugins\");\n\nconst EXACT_RE = /^module:/;\nconst BABEL_PLUGIN_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-plugin-)/;\nconst BABEL_PRESET_PREFIX_RE = /^(?!@|module:|[^/]+\\/|babel-preset-)/;\nconst BABEL_PLUGIN_ORG_RE = /^(@babel\\/)(?!plugin-|[^/]+\\/)/;\nconst BABEL_PRESET_ORG_RE = /^(@babel\\/)(?!preset-|[^/]+\\/)/;\nconst OTHER_PLUGIN_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-plugin(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_PRESET_ORG_RE =\n  /^(@(?!babel\\/)[^/]+\\/)(?![^/]*babel-preset(?:-|\\/|$)|[^/]+\\/)/;\nconst OTHER_ORG_DEFAULT_RE = /^(@(?!babel$)[^/]+)$/;\n\nexport function* resolvePlugin(name: string, dirname: string): Handler<string> {\n  return yield* resolveStandardizedName(\"plugin\", name, dirname);\n}\n\nexport function* resolvePreset(name: string, dirname: string): Handler<string> {\n  return yield* resolveStandardizedName(\"preset\", name, dirname);\n}\n\nexport function* loadPlugin(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const filepath = yield* resolvePlugin(name, dirname);\n\n  const value = yield* requireModule(\"plugin\", filepath);\n  debug(\"Loaded plugin %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nexport function* loadPreset(\n  name: string,\n  dirname: string,\n): Handler<{ filepath: string; value: unknown }> {\n  const filepath = yield* resolvePreset(name, dirname);\n\n  const value = yield* requireModule(\"preset\", filepath);\n\n  debug(\"Loaded preset %o from %o.\", name, dirname);\n\n  return { filepath, value };\n}\n\nfunction standardizeName(type: \"plugin\" | \"preset\", name: string) {\n  // Let absolute and relative paths through.\n  if (path.isAbsolute(name)) return name;\n\n  const isPreset = type === \"preset\";\n\n  return (\n    name\n      // foo -> babel-preset-foo\n      .replace(\n        isPreset ? BABEL_PRESET_PREFIX_RE : BABEL_PLUGIN_PREFIX_RE,\n        `babel-${type}-`,\n      )\n      // @babel/es2015 -> @babel/preset-es2015\n      .replace(\n        isPreset ? BABEL_PRESET_ORG_RE : BABEL_PLUGIN_ORG_RE,\n        `$1${type}-`,\n      )\n      // @foo/mypreset -> @foo/babel-preset-mypreset\n      .replace(\n        isPreset ? OTHER_PRESET_ORG_RE : OTHER_PLUGIN_ORG_RE,\n        `$1babel-${type}-`,\n      )\n      // @foo -> @foo/babel-preset\n      .replace(OTHER_ORG_DEFAULT_RE, `$1/babel-${type}`)\n      // module:mypreset -> mypreset\n      .replace(EXACT_RE, \"\")\n  );\n}\n\ntype Result<T> = { error: Error; value: null } | { error: null; value: T };\n\nfunction* resolveAlternativesHelper(\n  type: \"plugin\" | \"preset\",\n  name: string,\n): Iterator<string, string, Result<string>> {\n  const standardizedName = standardizeName(type, name);\n  const { error, value } = yield standardizedName;\n  if (!error) return value;\n\n  // @ts-expect-error code may not index error\n  if (error.code !== \"MODULE_NOT_FOUND\") throw error;\n\n  if (standardizedName !== name && !(yield name).error) {\n    error.message += `\\n- If you want to resolve \"${name}\", use \"module:${name}\"`;\n  }\n\n  if (!(yield standardizeName(type, \"@babel/\" + name)).error) {\n    error.message += `\\n- Did you mean \"@babel/${name}\"?`;\n  }\n\n  const oppositeType = type === \"preset\" ? \"plugin\" : \"preset\";\n  if (!(yield standardizeName(oppositeType, name)).error) {\n    error.message += `\\n- Did you accidentally pass a ${oppositeType} as a ${type}?`;\n  }\n\n  throw error;\n}\n\nfunction tryRequireResolve(\n  id: Parameters<RequireResolve>[0],\n  { paths: [dirname] }: Parameters<RequireResolve>[1],\n): Result<string> {\n  try {\n    return { error: null, value: require.resolve(id, { paths: [dirname] }) };\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nasync function tryImportMetaResolve(\n  id: Parameters<ImportMeta[\"resolve\"]>[0],\n  options: Parameters<ImportMeta[\"resolve\"]>[1],\n): Promise<Result<string>> {\n  try {\n    return { error: null, value: await importMetaResolve(id, options) };\n  } catch (error) {\n    return { error, value: null };\n  }\n}\n\nfunction resolveStandardizedNameForRequire(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(tryRequireResolve(res.value, { paths: [dirname] }));\n  }\n  return res.value;\n}\nasync function resolveStandardizedNameForImport(\n  type: \"plugin\" | \"preset\",\n  name: string,\n  dirname: string,\n) {\n  const parentUrl = pathToFileURL(\n    path.join(dirname, \"./babel-virtual-resolve-base.js\"),\n  ).href;\n\n  const it = resolveAlternativesHelper(type, name);\n  let res = it.next();\n  while (!res.done) {\n    res = it.next(await tryImportMetaResolve(res.value, parentUrl));\n  }\n  return fileURLToPath(res.value);\n}\n\nconst resolveStandardizedName = gensync<\n  [type: \"plugin\" | \"preset\", name: string, dirname?: string],\n  string\n>({\n  sync(type, name, dirname = process.cwd()) {\n    return resolveStandardizedNameForRequire(type, name, dirname);\n  },\n  async async(type, name, dirname = process.cwd()) {\n    if (!supportsESM) {\n      return resolveStandardizedNameForRequire(type, name, dirname);\n    }\n\n    try {\n      return await resolveStandardizedNameForImport(type, name, dirname);\n    } catch (e) {\n      try {\n        return resolveStandardizedNameForRequire(type, name, dirname);\n      } catch (e2) {\n        if (e.type === \"MODULE_NOT_FOUND\") throw e;\n        if (e2.type === \"MODULE_NOT_FOUND\") throw e2;\n        throw e;\n      }\n    }\n  },\n});\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var LOADING_MODULES = new Set();\n}\nfunction* requireModule(type: string, name: string): Handler<unknown> {\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!(yield* isAsync()) && LOADING_MODULES.has(name)) {\n      throw new Error(\n        `Reentrant ${type} detected trying to load \"${name}\". This module is not ignored ` +\n          \"and is trying to load itself while compiling itself, leading to a dependency cycle. \" +\n          'We recommend adding it to your \"ignore\" list in your babelrc, or to a .babelignore.',\n      );\n    }\n  }\n\n  try {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.add(name);\n    }\n    return yield* loadCjsOrMjsDefault(\n      name,\n      `You appear to be using a native ECMAScript module ${type}, ` +\n        \"which is only supported when running Babel asynchronously.\",\n      // For backward compatibility, we need to support malformed presets\n      // defined as separate named exports rather than a single default\n      // export.\n      // See packages/babel-core/test/fixtures/option-manager/presets/es2015_named.js\n      true,\n    );\n  } catch (err) {\n    err.message = `[BABEL]: ${err.message} (While processing: ${name})`;\n    throw err;\n  } finally {\n    if (!process.env.BABEL_8_BREAKING) {\n      LOADING_MODULES.delete(name);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAIA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AACA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;;;;;AAGA,MAAMA,KAAK,GAAGC,QAAA,CAAW,oCAAX,CAAd;;AAEA,MAAMC,QAAQ,GAAG,UAAjB;AACA,MAAMC,sBAAsB,GAAG,sCAA/B;AACA,MAAMC,sBAAsB,GAAG,sCAA/B;AACA,MAAMC,mBAAmB,GAAG,gCAA5B;AACA,MAAMC,mBAAmB,GAAG,gCAA5B;AACA,MAAMC,mBAAmB,GACvB,+DADF;AAEA,MAAMC,mBAAmB,GACvB,+DADF;AAEA,MAAMC,oBAAoB,GAAG,sBAA7B;;AAEO,UAAUC,aAAV,CAAwBC,IAAxB,EAAsCC,OAAtC,EAAwE;EAC7E,OAAO,OAAOC,uBAAuB,CAAC,QAAD,EAAWF,IAAX,EAAiBC,OAAjB,CAArC;AACD;;AAEM,UAAUE,aAAV,CAAwBH,IAAxB,EAAsCC,OAAtC,EAAwE;EAC7E,OAAO,OAAOC,uBAAuB,CAAC,QAAD,EAAWF,IAAX,EAAiBC,OAAjB,CAArC;AACD;;AAEM,UAAUG,UAAV,CACLJ,IADK,EAELC,OAFK,EAG0C;EAC/C,MAAMI,QAAQ,GAAG,OAAON,aAAa,CAACC,IAAD,EAAOC,OAAP,CAArC;EAEA,MAAMK,KAAK,GAAG,OAAOC,aAAa,CAAC,QAAD,EAAWF,QAAX,CAAlC;EACAhB,KAAK,CAAC,2BAAD,EAA8BW,IAA9B,EAAoCC,OAApC,CAAL;EAEA,OAAO;IAAEI,QAAF;IAAYC;EAAZ,CAAP;AACD;;AAEM,UAAUE,UAAV,CACLR,IADK,EAELC,OAFK,EAG0C;EAC/C,MAAMI,QAAQ,GAAG,OAAOF,aAAa,CAACH,IAAD,EAAOC,OAAP,CAArC;EAEA,MAAMK,KAAK,GAAG,OAAOC,aAAa,CAAC,QAAD,EAAWF,QAAX,CAAlC;EAEAhB,KAAK,CAAC,2BAAD,EAA8BW,IAA9B,EAAoCC,OAApC,CAAL;EAEA,OAAO;IAAEI,QAAF;IAAYC;EAAZ,CAAP;AACD;;AAED,SAASG,eAAT,CAAyBC,IAAzB,EAAoDV,IAApD,EAAkE;EAEhE,IAAIW,OAAA,CAAKC,UAAL,CAAgBZ,IAAhB,CAAJ,EAA2B,OAAOA,IAAP;EAE3B,MAAMa,QAAQ,GAAGH,IAAI,KAAK,QAA1B;EAEA,OACEV,IAAI,CAEDc,OAFH,CAGID,QAAQ,GAAGpB,sBAAH,GAA4BD,sBAHxC,EAIK,SAAQkB,IAAK,GAJlB,EAOGI,OAPH,CAQID,QAAQ,GAAGlB,mBAAH,GAAyBD,mBARrC,EASK,KAAIgB,IAAK,GATd,EAYGI,OAZH,CAaID,QAAQ,GAAGhB,mBAAH,GAAyBD,mBAbrC,EAcK,WAAUc,IAAK,GAdpB,EAiBGI,OAjBH,CAiBWhB,oBAjBX,EAiBkC,YAAWY,IAAK,EAjBlD,EAmBGI,OAnBH,CAmBWvB,QAnBX,EAmBqB,EAnBrB,CADF;AAsBD;;AAID,UAAUwB,yBAAV,CACEL,IADF,EAEEV,IAFF,EAG4C;EAC1C,MAAMgB,gBAAgB,GAAGP,eAAe,CAACC,IAAD,EAAOV,IAAP,CAAxC;EACA,MAAM;IAAEiB,KAAF;IAASX;EAAT,IAAmB,MAAMU,gBAA/B;EACA,IAAI,CAACC,KAAL,EAAY,OAAOX,KAAP;EAGZ,IAAIW,KAAK,CAACC,IAAN,KAAe,kBAAnB,EAAuC,MAAMD,KAAN;;EAEvC,IAAID,gBAAgB,KAAKhB,IAArB,IAA6B,CAAC,CAAC,MAAMA,IAAP,EAAaiB,KAA/C,EAAsD;IACpDA,KAAK,CAACE,OAAN,IAAkB,+BAA8BnB,IAAK,kBAAiBA,IAAK,GAA3E;EACD;;EAED,IAAI,CAAC,CAAC,MAAMS,eAAe,CAACC,IAAD,EAAO,YAAYV,IAAnB,CAAtB,EAAgDiB,KAArD,EAA4D;IAC1DA,KAAK,CAACE,OAAN,IAAkB,4BAA2BnB,IAAK,IAAlD;EACD;;EAED,MAAMoB,YAAY,GAAGV,IAAI,KAAK,QAAT,GAAoB,QAApB,GAA+B,QAApD;;EACA,IAAI,CAAC,CAAC,MAAMD,eAAe,CAACW,YAAD,EAAepB,IAAf,CAAtB,EAA4CiB,KAAjD,EAAwD;IACtDA,KAAK,CAACE,OAAN,IAAkB,mCAAkCC,YAAa,SAAQV,IAAK,GAA9E;EACD;;EAED,MAAMO,KAAN;AACD;;AAED,SAASI,iBAAT,CACEC,EADF,EAEE;EAAEC,KAAK,EAAE,CAACtB,OAAD;AAAT,CAFF,EAGkB;EAChB,IAAI;IACF,OAAO;MAAEgB,KAAK,EAAE,IAAT;MAAeX,KAAK,EAAE;QAAA;MAAA;QAAA;;QAAA;QAAA;QAAA;QAAA;MAAA,GAAgBgB,EAAhB,EAAoB;QAAEC,KAAK,EAAE,CAACtB,OAAD;MAAT,CAApB;IAAtB,CAAP;EACD,CAFD,CAEE,OAAOgB,KAAP,EAAc;IACd,OAAO;MAAEA,KAAF;MAASX,KAAK,EAAE;IAAhB,CAAP;EACD;AACF;;SAEckB,oB;;;;;4CAAf,WACEF,EADF,EAEEG,OAFF,EAG2B;IACzB,IAAI;MACF,OAAO;QAAER,KAAK,EAAE,IAAT;QAAeX,KAAK,QAAQ,IAAAoB,0BAAA,EAAkBJ,EAAlB,EAAsBG,OAAtB;MAA5B,CAAP;IACD,CAFD,CAEE,OAAOR,KAAP,EAAc;MACd,OAAO;QAAEA,KAAF;QAASX,KAAK,EAAE;MAAhB,CAAP;IACD;EACF,C;;;;AAED,SAASqB,iCAAT,CACEjB,IADF,EAEEV,IAFF,EAGEC,OAHF,EAIE;EACA,MAAM2B,EAAE,GAAGb,yBAAyB,CAACL,IAAD,EAAOV,IAAP,CAApC;EACA,IAAI6B,GAAG,GAAGD,EAAE,CAACE,IAAH,EAAV;;EACA,OAAO,CAACD,GAAG,CAACE,IAAZ,EAAkB;IAChBF,GAAG,GAAGD,EAAE,CAACE,IAAH,CAAQT,iBAAiB,CAACQ,GAAG,CAACvB,KAAL,EAAY;MAAEiB,KAAK,EAAE,CAACtB,OAAD;IAAT,CAAZ,CAAzB,CAAN;EACD;;EACD,OAAO4B,GAAG,CAACvB,KAAX;AACD;;SACc0B,gC;;;;;wDAAf,WACEtB,IADF,EAEEV,IAFF,EAGEC,OAHF,EAIE;IACA,MAAMgC,SAAS,GAAG,IAAAC,oBAAA,EAChBvB,OAAA,CAAKwB,IAAL,CAAUlC,OAAV,EAAmB,iCAAnB,CADgB,EAEhBmC,IAFF;IAIA,MAAMR,EAAE,GAAGb,yBAAyB,CAACL,IAAD,EAAOV,IAAP,CAApC;IACA,IAAI6B,GAAG,GAAGD,EAAE,CAACE,IAAH,EAAV;;IACA,OAAO,CAACD,GAAG,CAACE,IAAZ,EAAkB;MAChBF,GAAG,GAAGD,EAAE,CAACE,IAAH,OAAcN,oBAAoB,CAACK,GAAG,CAACvB,KAAL,EAAY2B,SAAZ,CAAlC,CAAN;IACD;;IACD,OAAO,IAAAI,oBAAA,EAAcR,GAAG,CAACvB,KAAlB,CAAP;EACD,C;;;;AAED,MAAMJ,uBAAuB,GAAGoC,UAAA,CAG9B;EACAC,IAAI,CAAC7B,IAAD,EAAOV,IAAP,EAAaC,OAAO,GAAGuC,OAAO,CAACC,GAAR,EAAvB,EAAsC;IACxC,OAAOd,iCAAiC,CAACjB,IAAD,EAAOV,IAAP,EAAaC,OAAb,CAAxC;EACD,CAHD;;EAIMyC,KAAN,CAAYhC,IAAZ,EAAkBV,IAAlB,EAAwBC,OAAO,GAAGuC,OAAO,CAACC,GAAR,EAAlC,EAAiD;IAAA;MAC/C,IAAI,CAACE,wBAAL,EAAkB;QAChB,OAAOhB,iCAAiC,CAACjB,IAAD,EAAOV,IAAP,EAAaC,OAAb,CAAxC;MACD;;MAED,IAAI;QACF,aAAa+B,gCAAgC,CAACtB,IAAD,EAAOV,IAAP,EAAaC,OAAb,CAA7C;MACD,CAFD,CAEE,OAAO2C,CAAP,EAAU;QACV,IAAI;UACF,OAAOjB,iCAAiC,CAACjB,IAAD,EAAOV,IAAP,EAAaC,OAAb,CAAxC;QACD,CAFD,CAEE,OAAO4C,EAAP,EAAW;UACX,IAAID,CAAC,CAAClC,IAAF,KAAW,kBAAf,EAAmC,MAAMkC,CAAN;UACnC,IAAIC,EAAE,CAACnC,IAAH,KAAY,kBAAhB,EAAoC,MAAMmC,EAAN;UACpC,MAAMD,CAAN;QACD;MACF;IAf8C;EAgBhD;;AApBD,CAH8B,CAAhC;;AA0BmC;EAEjC,IAAIE,eAAe,GAAG,IAAIC,GAAJ,EAAtB;AACD;;AACD,UAAUxC,aAAV,CAAwBG,IAAxB,EAAsCV,IAAtC,EAAsE;EACjC;IACjC,IAAI,EAAE,OAAO,IAAAgD,cAAA,GAAT,KAAuBF,eAAe,CAACG,GAAhB,CAAoBjD,IAApB,CAA3B,EAAsD;MACpD,MAAM,IAAIkD,KAAJ,CACH,aAAYxC,IAAK,6BAA4BV,IAAK,gCAAnD,GACE,sFADF,GAEE,qFAHE,CAAN;IAKD;EACF;;EAED,IAAI;IACiC;MACjC8C,eAAe,CAACK,GAAhB,CAAoBnD,IAApB;IACD;IACD,OAAO,OAAO,IAAAoD,oBAAA,EACZpD,IADY,EAEX,qDAAoDU,IAAK,IAA1D,GACE,4DAHU,EAQZ,IARY,CAAd;EAUD,CAdD,CAcE,OAAO2C,GAAP,EAAY;IACZA,GAAG,CAAClC,OAAJ,GAAe,YAAWkC,GAAG,CAAClC,OAAQ,uBAAsBnB,IAAK,GAAjE;IACA,MAAMqD,GAAN;EACD,CAjBD,SAiBU;IAC2B;MACjCP,eAAe,CAACQ,MAAhB,CAAuBtD,IAAvB;IACD;EACF;AACF"}