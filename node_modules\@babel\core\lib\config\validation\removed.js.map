{"version": 3, "names": ["auxiliaryComment", "message", "blacklist", "breakConfig", "experimental", "externalHelpers", "extra", "jsxPragma", "loose", "metadataUsedHelpers", "modules", "nonStandard", "optional", "sourceMapName", "stage", "whitelist", "resolveModuleSource", "version", "metadata", "sourceMapTarget"], "sources": ["../../../src/config/validation/removed.ts"], "sourcesContent": ["export default {\n  auxiliaryComment: {\n    message: \"Use `auxiliaryCommentBefore` or `auxiliaryCommentAfter`\",\n  },\n  blacklist: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  breakConfig: {\n    message: \"This is not a necessary option in Babel 6\",\n  },\n  experimental: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  externalHelpers: {\n    message:\n      \"Use the `external-helpers` plugin instead. \" +\n      \"Check out http://babeljs.io/docs/plugins/external-helpers/\",\n  },\n  extra: {\n    message: \"\",\n  },\n  jsxPragma: {\n    message:\n      \"use the `pragma` option in the `react-jsx` plugin. \" +\n      \"Check out http://babeljs.io/docs/plugins/transform-react-jsx/\",\n  },\n  loose: {\n    message:\n      \"Specify the `loose` option for the relevant plugin you are using \" +\n      \"or use a preset that sets the option.\",\n  },\n  metadataUsedHelpers: {\n    message: \"Not required anymore as this is enabled by default\",\n  },\n  modules: {\n    message:\n      \"Use the corresponding module transform plugin in the `plugins` option. \" +\n      \"Check out http://babeljs.io/docs/plugins/#modules\",\n  },\n  nonStandard: {\n    message:\n      \"Use the `react-jsx` and `flow-strip-types` plugins to support JSX and Flow. \" +\n      \"Also check out the react preset http://babeljs.io/docs/plugins/preset-react/\",\n  },\n  optional: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n  sourceMapName: {\n    message:\n      \"The `sourceMapName` option has been removed because it makes more sense for the \" +\n      \"tooling that calls Babel to assign `map.file` themselves.\",\n  },\n  stage: {\n    message:\n      \"Check out the corresponding stage-x presets http://babeljs.io/docs/plugins/#presets\",\n  },\n  whitelist: {\n    message: \"Put the specific transforms you want in the `plugins` option\",\n  },\n\n  resolveModuleSource: {\n    version: 6,\n    message: \"Use `babel-plugin-module-resolver@3`'s 'resolvePath' options\",\n  },\n  metadata: {\n    version: 6,\n    message:\n      \"Generated plugin metadata is always included in the output result\",\n  },\n  sourceMapTarget: {\n    version: 6,\n    message:\n      \"The `sourceMapTarget` option has been removed because it makes more sense for the tooling \" +\n      \"that calls Babel to assign `map.file` themselves.\",\n  },\n} as { [name: string]: { version?: number; message: string } };\n"], "mappings": ";;;;;;eAAe;EACbA,gBAAgB,EAAE;IAChBC,OAAO,EAAE;EADO,CADL;EAIbC,SAAS,EAAE;IACTD,OAAO,EAAE;EADA,CAJE;EAObE,WAAW,EAAE;IACXF,OAAO,EAAE;EADE,CAPA;EAUbG,YAAY,EAAE;IACZH,OAAO,EAAE;EADG,CAVD;EAabI,eAAe,EAAE;IACfJ,OAAO,EACL,gDACA;EAHa,CAbJ;EAkBbK,KAAK,EAAE;IACLL,OAAO,EAAE;EADJ,CAlBM;EAqBbM,SAAS,EAAE;IACTN,OAAO,EACL,wDACA;EAHO,CArBE;EA0BbO,KAAK,EAAE;IACLP,OAAO,EACL,sEACA;EAHG,CA1BM;EA+BbQ,mBAAmB,EAAE;IACnBR,OAAO,EAAE;EADU,CA/BR;EAkCbS,OAAO,EAAE;IACPT,OAAO,EACL,4EACA;EAHK,CAlCI;EAuCbU,WAAW,EAAE;IACXV,OAAO,EACL,iFACA;EAHS,CAvCA;EA4CbW,QAAQ,EAAE;IACRX,OAAO,EAAE;EADD,CA5CG;EA+CbY,aAAa,EAAE;IACbZ,OAAO,EACL,qFACA;EAHW,CA/CF;EAoDba,KAAK,EAAE;IACLb,OAAO,EACL;EAFG,CApDM;EAwDbc,SAAS,EAAE;IACTd,OAAO,EAAE;EADA,CAxDE;EA4Dbe,mBAAmB,EAAE;IACnBC,OAAO,EAAE,CADU;IAEnBhB,OAAO,EAAE;EAFU,CA5DR;EAgEbiB,QAAQ,EAAE;IACRD,OAAO,EAAE,CADD;IAERhB,OAAO,EACL;EAHM,CAhEG;EAqEbkB,eAAe,EAAE;IACfF,OAAO,EAAE,CADM;IAEfhB,OAAO,EACL,+FACA;EAJa;AArEJ,C"}