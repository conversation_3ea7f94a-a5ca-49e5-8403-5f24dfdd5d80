{"version": 3, "names": ["getEnv", "defaultValue", "process", "env", "BABEL_ENV", "NODE_ENV"], "sources": ["../../../src/config/helpers/environment.ts"], "sourcesContent": ["export function getEnv(defaultValue: string = \"development\"): string {\n  return process.env.BABEL_ENV || process.env.NODE_ENV || defaultValue;\n}\n"], "mappings": ";;;;;;;AAAO,SAASA,MAAT,CAAgBC,YAAoB,GAAG,aAAvC,EAA8D;EACnE,OAAOC,OAAO,CAACC,GAAR,CAAYC,SAAZ,IAAyBF,OAAO,CAACC,GAAR,CAAYE,QAArC,IAAiDJ,YAAxD;AACD"}