{"version": 3, "names": ["PACKAGE_FILENAME", "findPackageData", "filepath", "pkg", "directories", "isPackage", "dirname", "path", "basename", "push", "readConfigPackage", "join", "nextLoc", "makeStaticFileCache", "content", "options", "JSON", "parse", "err", "ConfigError", "message", "Error", "Array", "isArray"], "sources": ["../../../src/config/files/package.ts"], "sourcesContent": ["import path from \"path\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { makeStaticFileCache } from \"./utils\";\n\nimport type { ConfigFile, FilePackageData } from \"./types\";\n\nimport ConfigError from \"../../errors/config-error\";\n\nconst PACKAGE_FILENAME = \"package.json\";\n\n/**\n * Find metadata about the package that this file is inside of. Resolution\n * of Babel's config requires general package information to decide when to\n * search for .babelrc files\n */\nexport function* findPackageData(filepath: string): Handler<FilePackageData> {\n  let pkg = null;\n  const directories = [];\n  let isPackage = true;\n\n  let dirname = path.dirname(filepath);\n  while (!pkg && path.basename(dirname) !== \"node_modules\") {\n    directories.push(dirname);\n\n    pkg = yield* readConfigPackage(path.join(dirname, PACKAGE_FILENAME));\n\n    const nextLoc = path.dirname(dirname);\n    if (dirname === nextLoc) {\n      isPackage = false;\n      break;\n    }\n    dirname = nextLoc;\n  }\n\n  return { filepath, directories, pkg, isPackage };\n}\n\nconst readConfigPackage = makeStaticFileCache(\n  (filepath, content): ConfigFile => {\n    let options;\n    try {\n      options = JSON.parse(content) as unknown;\n    } catch (err) {\n      throw new ConfigError(\n        `Error while parsing JSON - ${err.message}`,\n        filepath,\n      );\n    }\n\n    if (!options) throw new Error(`${filepath}: No config detected`);\n\n    if (typeof options !== \"object\") {\n      throw new ConfigError(\n        `Config returned typeof ${typeof options}`,\n        filepath,\n      );\n    }\n    if (Array.isArray(options)) {\n      throw new ConfigError(`Expected config object but found array`, filepath);\n    }\n\n    return {\n      filepath,\n      dirname: path.dirname(filepath),\n      options,\n    };\n  },\n);\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAIA;;AAEA,MAAMA,gBAAgB,GAAG,cAAzB;;AAOO,UAAUC,eAAV,CAA0BC,QAA1B,EAAsE;EAC3E,IAAIC,GAAG,GAAG,IAAV;EACA,MAAMC,WAAW,GAAG,EAApB;EACA,IAAIC,SAAS,GAAG,IAAhB;;EAEA,IAAIC,OAAO,GAAGC,OAAA,CAAKD,OAAL,CAAaJ,QAAb,CAAd;;EACA,OAAO,CAACC,GAAD,IAAQI,OAAA,CAAKC,QAAL,CAAcF,OAAd,MAA2B,cAA1C,EAA0D;IACxDF,WAAW,CAACK,IAAZ,CAAiBH,OAAjB;IAEAH,GAAG,GAAG,OAAOO,iBAAiB,CAACH,OAAA,CAAKI,IAAL,CAAUL,OAAV,EAAmBN,gBAAnB,CAAD,CAA9B;;IAEA,MAAMY,OAAO,GAAGL,OAAA,CAAKD,OAAL,CAAaA,OAAb,CAAhB;;IACA,IAAIA,OAAO,KAAKM,OAAhB,EAAyB;MACvBP,SAAS,GAAG,KAAZ;MACA;IACD;;IACDC,OAAO,GAAGM,OAAV;EACD;;EAED,OAAO;IAAEV,QAAF;IAAYE,WAAZ;IAAyBD,GAAzB;IAA8BE;EAA9B,CAAP;AACD;;AAED,MAAMK,iBAAiB,GAAG,IAAAG,0BAAA,EACxB,CAACX,QAAD,EAAWY,OAAX,KAAmC;EACjC,IAAIC,OAAJ;;EACA,IAAI;IACFA,OAAO,GAAGC,IAAI,CAACC,KAAL,CAAWH,OAAX,CAAV;EACD,CAFD,CAEE,OAAOI,GAAP,EAAY;IACZ,MAAM,IAAIC,oBAAJ,CACH,8BAA6BD,GAAG,CAACE,OAAQ,EADtC,EAEJlB,QAFI,CAAN;EAID;;EAED,IAAI,CAACa,OAAL,EAAc,MAAM,IAAIM,KAAJ,CAAW,GAAEnB,QAAS,sBAAtB,CAAN;;EAEd,IAAI,OAAOa,OAAP,KAAmB,QAAvB,EAAiC;IAC/B,MAAM,IAAII,oBAAJ,CACH,0BAAyB,OAAOJ,OAAQ,EADrC,EAEJb,QAFI,CAAN;EAID;;EACD,IAAIoB,KAAK,CAACC,OAAN,CAAcR,OAAd,CAAJ,EAA4B;IAC1B,MAAM,IAAII,oBAAJ,CAAiB,wCAAjB,EAA0DjB,QAA1D,CAAN;EACD;;EAED,OAAO;IACLA,QADK;IAELI,OAAO,EAAEC,OAAA,CAAKD,OAAL,CAAaJ,QAAb,CAFJ;IAGLa;EAHK,CAAP;AAKD,CA7BuB,CAA1B"}