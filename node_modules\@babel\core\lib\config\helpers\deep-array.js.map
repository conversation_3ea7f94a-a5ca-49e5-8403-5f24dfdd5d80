{"version": 3, "names": ["finalize", "deepArr", "Object", "freeze", "flattenToSet", "arr", "result", "Set", "stack", "length", "el", "pop", "Array", "isArray", "push", "add"], "sources": ["../../../src/config/helpers/deep-array.ts"], "sourcesContent": ["export type DeepArray<T> = Array<T | ReadonlyDeepArray<T>>;\n\n// Just to make sure that DeepArray<T> is not assignable to ReadonlyDeepArray<T>\ndeclare const __marker: unique symbol;\nexport type ReadonlyDeepArray<T> = ReadonlyArray<T | ReadonlyDeepArray<T>> & {\n  [__marker]: true;\n};\n\nexport function finalize<T>(deepArr: DeepArray<T>): ReadonlyDeepArray<T> {\n  return Object.freeze(deepArr) as ReadonlyDeepArray<T>;\n}\n\nexport function flattenToSet<T extends string>(\n  arr: ReadonlyDeepArray<T>,\n): Set<T> {\n  const result = new Set<T>();\n  const stack = [arr];\n  while (stack.length > 0) {\n    for (const el of stack.pop()) {\n      if (Array.isArray(el)) stack.push(el as ReadonlyDeepArray<T>);\n      else result.add(el as T);\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;AAQO,SAASA,QAAT,CAAqBC,OAArB,EAAkE;EACvE,OAAOC,MAAM,CAACC,MAAP,CAAcF,OAAd,CAAP;AACD;;AAEM,SAASG,YAAT,CACLC,GADK,EAEG;EACR,MAAMC,MAAM,GAAG,IAAIC,GAAJ,EAAf;EACA,MAAMC,KAAK,GAAG,CAACH,GAAD,CAAd;;EACA,OAAOG,KAAK,CAACC,MAAN,GAAe,CAAtB,EAAyB;IACvB,KAAK,MAAMC,EAAX,IAAiBF,KAAK,CAACG,GAAN,EAAjB,EAA8B;MAC5B,IAAIC,KAAK,CAACC,OAAN,CAAcH,EAAd,CAAJ,EAAuBF,KAAK,CAACM,IAAN,CAAWJ,EAAX,EAAvB,KACKJ,MAAM,CAACS,GAAP,CAAWL,EAAX;IACN;EACF;;EACD,OAAOJ,<PERSON><PERSON>;AACD"}