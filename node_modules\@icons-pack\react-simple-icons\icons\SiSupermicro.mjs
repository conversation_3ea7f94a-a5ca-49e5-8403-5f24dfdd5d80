import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';

const defaultColor = "#151F6D";
const SiSupermicro = React.forwardRef(function SiSupermicro2({ title = "Supermicro", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsx("title", { children: title }),
        /* @__PURE__ */ jsx("path", { d: "M13.937 5.842c-.489-.007-.988.002-1.494.03-3.99.218-7.831 1.51-10.236 3.443-.469.379-1.18 1.133-1.453 1.543-.539.8-.754 1.444-.754 2.252 0 1.55 1.026 2.85 3.006 3.798 1.218.586 2.67.97 4.392 1.164 1.203.133 3.198.114 4.568-.046 3.963-.465 7.517-1.921 9.52-3.893.242-.242.77-.851.789-.914 0-.004.105.031.234.074.554.188 1.148-.078 1.398-.629.133-.28.121-.732-.023-1.025a1.149 1.149 0 0 0-.621-.57l-.188-.067v-.199c0-.855-.422-1.727-1.203-2.484-1.568-1.52-4.513-2.421-7.935-2.477Zm-.156.395c.638-.008 1.257.013 1.836.064 2.276.2 4.213.782 5.587 1.684.504.332 1.176 1.002 1.418 1.416.234.402.375.848.403 1.27l.02.331-.24.004a1.076 1.076 0 0 0-.823.41c-.196.25-.254.422-.25.778 0 .336.082.556.289.795l.129.144-.348.352c-.683.69-1.46 1.242-2.482 1.75-2.585 1.296-6.064 1.974-9.258 1.806-2.178-.113-3.916-.502-5.365-1.197-1.527-.73-2.478-1.723-2.72-2.82-.613-2.776 3.139-5.614 8.593-6.508a21.81 21.81 0 0 1 3.21-.28ZM23 7.784l-.01.002c-.03 0-.059 0-.088.01-.04.007-.079.026-.117.042a.44.44 0 0 0-.138.125c-.016.027-.035.052-.043.082a.35.35 0 0 0-.032.122.468.468 0 0 0 .016.152.436.436 0 0 0 .174.228.483.483 0 0 0 .187.067.456.456 0 0 0 .248-.047.408.408 0 0 0 .215-.281c.005-.035.013-.069.008-.104.001-.026-.006-.054-.01-.08a.435.435 0 0 0-.191-.26.485.485 0 0 0-.21-.058H23zm-.031.086h.06a.292.292 0 0 1 .112.033c.02.008.037.02.054.035.023.02.048.041.063.068a.3.3 0 0 1 .037.075c.012.027.013.057.02.086-.002.031.004.064-.004.095-.003.033-.018.062-.032.092-.006.019-.019.034-.029.05a.297.297 0 0 1-.076.075c-.017.015-.04.02-.059.03-.022.006-.044.016-.068.017-.006 0-.011 0-.018.002-.03 0-.061.004-.091-.002a.297.297 0 0 1-.154-.078.22.22 0 0 1-.044-.047c-.018-.02-.026-.045-.039-.068-.009-.02-.012-.042-.017-.063-.008-.027-.006-.056-.006-.084-.002-.039.01-.077.021-.113.014-.026.024-.054.043-.076.015-.024.039-.04.057-.059a.317.317 0 0 1 .066-.041c.023-.007.045-.018.069-.021a.141.141 0 0 1 .035-.006zm.047.12-.055.003h-.053l-.02.004-.019.002-.015.002-.016.001v.415h.094v-.17l.037.002.035.002.012.003.013.002.012.012.004.008.004.007.002.01.002.008.011.038.006.014.006.014h.104l-.006-.012-.008-.035-.01-.037-.006-.022-.006-.02-.007-.015-.008-.013-.01-.012-.01-.01-.015-.008-.014-.01v-.003l.012-.006.013-.006.012-.006.012-.008.01-.01.007-.013.006-.014.002-.02v-.019l-.002-.013-.002-.014-.008-.014-.008-.013-.01-.012-.01-.01-.015-.006-.015-.008-.018-.004-.018-.005-.029-.002zm-.055.07h.053l.017.005.016.006.014.012.01.01.001.007.004.01v.021l-.006.014-.005.01-.01.006-.01.01-.018.003-.013.008-.04.002h-.04v-.121l.013-.002zM4.203 10.318c-.071.004-.104.024-.104.054 0 .098-.058.102-.285.024a1.043 1.043 0 0 0-.703-.024.83.83 0 0 0-.574.825c0 .406.176.605.887 1.011.175.102.375.245.437.319.192.214.149.445-.094.547-.417.171-.863-.11-.94-.602-.024-.156-.032-.162-.157-.162h-.133v1.17h.133c.097 0 .137-.02.152-.074.02-.075.024-.075.262 0 .718.222 1.32-.048 1.445-.649.102-.48-.117-.775-.875-1.19-.57-.312-.73-.534-.52-.734a.436.436 0 0 1 .454-.117c.261.067.511.41.511.703 0 .055.032.07.137.07h.137V10.32h-.17zm15.046.584-.039.002c-.078 0-.12.023-.136.078-.024.074-.032.074-.149.012-.172-.086-.625-.106-.851-.032-.246.078-.551.364-.684.633-.086.176-.113.29-.125.555-.02.41.078.709.312.963.512.554 1.465.449 1.813-.2l.074-.144-.094-.074-.093-.078-.18.203c-.27.312-.524.379-.852.234-.32-.148-.48-.58-.398-1.088.086-.554.367-.812.797-.734.18.035.387.242.457.46.05.145.074.169.18.18l.125.012-.02-.246c-.061-.704-.058-.739-.137-.736zm-8.394.033v.14c0 .137.002.141.135.141.113 0 .144.02.176.102.023.054.039.45.039.875s-.016.818-.04.873c-.03.082-.062.101-.175.101-.129 0-.135.008-.135.137v.137h1.17v-.133c0-.129-.008-.137-.145-.148l-.148-.012-.012-.403c-.004-.218.004-.396.02-.392.015 0 .203.244.414.545l.379.543h1.523v-.137c0-.125-.012-.137-.11-.137-.19 0-.202-.043-.202-.703l.004-.603.347.779c.465 1.046.43 1.042.863.059l.329-.74.011.552c.004.305 0 .578-.008.606-.011.027-.082.05-.16.05-.129 0-.136.008-.136.137v.137h2.224v-.137c0-.125-.008-.137-.117-.137-.074 0-.129-.027-.156-.074-.043-.082-.055-1.635-.016-1.79.02-.067.055-.087.156-.087.125 0 .133-.008.133-.136v-.137h-1.865l-.34.77c-.187.425-.355.786-.37.802-.017.016-.189-.322-.384-.76l-.355-.793-.395-.011-.39-.012v.14c0 .125.008.137.117.137.094 0 .129.024.156.102.024.054.04.45.04.875s-.016.818-.04.873c-.027.082-.062.101-.164.101a.565.565 0 0 1-.254-.078c-.117-.07-.414-.432-.57-.697-.063-.11-.063-.11.078-.203.195-.129.281-.258.309-.473a.642.642 0 0 0-.329-.648c-.18-.102-.203-.106-.894-.117zm8.55 0v.137c0 .132.008.14.149.152l.145.012v1.912l-.145.012c-.14.011-.148.02-.148.148v.133h1.169v-.133c0-.129-.007-.137-.144-.148l-.149-.012-.011-.41c-.012-.488-.04-.5.453.195l.344.488.378.012.38.012v-.133c0-.125-.012-.137-.149-.156-.172-.032-.293-.149-.594-.575-.187-.265-.199-.29-.136-.33.039-.02.128-.09.199-.148.324-.277.238-.844-.157-1.055-.144-.074-.23-.086-.87-.097zm-14.876.008v.136c0 .13.008.137.133.137.086 0 .14.024.156.063.012.035.024.383.024.777 0 .937.058 1.111.449 1.318.125.067.227.082.508.082.48 0 .677-.101.86-.445.063-.117.079-.252.095-.955l.02-.82.237-.012c.223-.012.243-.004.266.078.04.149.027 1.658-.016 1.764-.027.078-.062.101-.156.101-.11 0-.117.012-.117.137v.137h1.133v-.133c0-.129-.008-.137-.145-.149l-.148-.011-.012-.38-.012-.376.23.002c.403.004.638-.127.766-.443.168-.403-.066-.844-.519-.97-.078-.019-.602-.038-1.164-.038H6.092v.133c0 .128.007.136.148.148l.145.012.011.734c.012.86-.012.947-.281 1.08-.27.129-.543.082-.676-.11-.062-.093-.07-.193-.07-.903v-.801l.148-.012c.137-.012.145-.02.145-.148v-.133h-.566Zm4.295 0v.133c0 .128.008.136.148.148l.145.012.012.933c.011 1.008.011.998-.196.998-.097 0-.11.016-.11.137v.137h1.9l.12-.39.118-.391h-.13c-.1 0-.14.027-.21.136-.156.254-.243.293-.63.293h-.347v-.78h.293c.203 0 .3.017.309.048.011.027.027.096.039.154.02.09.047.11.14.11h.118v-.975h.12c.094 0 .118-.012.098-.063a1.93 1.93 0 0 1-.023-.351v-.29H9.78Zm7.173.275c.035 0 .078.003.135.006l.246.012v1.912h-.254c-.234 0-.253-.008-.277-.098-.043-.152-.039-1.623.004-1.736.026-.073.043-.095.146-.096zm-6.353.03.352.015c.246.008.367.027.406.07a.472.472 0 0 1 .082.184c.023.117.02.129-.074.129-.078 0-.106.023-.125.105a.887.887 0 0 1-.04.157c-.007.035-.105.05-.308.05h-.293v-.355zm2.213.011a.35.35 0 0 1 .148.043c.18.082.27.363.176.543-.05.094-.238.191-.37.191h-.099v-.367c0-.367.012-.402.145-.41zm8.55 0a.35.35 0 0 1 .149.043c.113.05.215.215.215.344 0 .21-.2.39-.434.39-.066 0-.074-.035-.074-.367 0-.367.012-.402.144-.41zm-12.599.035h.133c.102 0 .16.032.238.125.09.11.106.153.09.313-.023.242-.117.344-.312.344h-.149v-.391z" })
      ]
    }
  );
});

export { SiSupermicro as default, defaultColor };
