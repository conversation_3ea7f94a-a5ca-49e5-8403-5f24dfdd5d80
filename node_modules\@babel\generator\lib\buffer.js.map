{"version": 3, "names": ["SourcePos", "identifierName", "undefined", "line", "column", "filename", "<PERSON><PERSON><PERSON>", "constructor", "map", "_map", "_buf", "_str", "_appendCount", "_last", "_queue", "_queueCursor", "_position", "_sourcePosition", "_disallowedPop", "objectReusable", "_allocQueue", "queue", "i", "push", "char", "repeat", "_pushQueue", "cursor", "length", "item", "_popQueue", "Error", "get", "_flush", "result", "code", "trimRight", "decodedMap", "getDecoded", "resultMap", "value", "Object", "defineProperty", "writable", "rawMappings", "mappings", "getRawMappings", "append", "str", "maybeNewline", "_append", "appendChar", "_appendChar", "sourcePosition", "queueIndentation", "queueCursor", "sourcePos", "String", "fromCharCode", "_mark", "len", "charCodeAt", "indexOf", "last", "mark", "removeTrailingNewline", "removeLastSemicolon", "getLastChar", "endsWithCharAndNewline", "lastCp", "<PERSON><PERSON><PERSON><PERSON>", "exactSource", "loc", "cb", "source", "_disallowPop", "prop", "_normalizePosition", "withSource", "originalLine", "originalColumn", "originalFilename", "originalIdentifierName", "disallowedPop", "targetObj", "pos", "getCurrentColumn", "lastIndex", "getCurrentLine", "count"], "sources": ["../src/buffer.ts"], "sourcesContent": ["import type SourceMap from \"./source-map\";\nimport * as charcodes from \"charcodes\";\n\nexport type Pos = {\n  line: number;\n  column: number;\n};\nexport type Loc = {\n  start?: Pos;\n  end?: Pos;\n  identifierName?: string;\n  filename?: string;\n};\ntype SourcePos = {\n  identifierName: string | undefined;\n  line: number | undefined;\n  column: number | undefined;\n  filename: string | undefined;\n};\n\ntype QueueItem = {\n  char: number;\n  repeat: number;\n  line: number | undefined;\n  column: number | undefined;\n  identifierName: string | undefined;\n  filename: string | undefined;\n};\n\nfunction SourcePos(): SourcePos {\n  return {\n    identifierName: undefined,\n    line: undefined,\n    column: undefined,\n    filename: undefined,\n  };\n}\n\nexport default class Buffer {\n  constructor(map?: SourceMap | null) {\n    this._map = map;\n\n    this._allocQueue();\n  }\n\n  _map: SourceMap = null;\n  _buf = \"\";\n  _str = \"\";\n  _appendCount = 0;\n  _last = 0;\n  _queue: QueueItem[] = [];\n  _queueCursor = 0;\n\n  _position = {\n    line: 1,\n    column: 0,\n  };\n  _sourcePosition = SourcePos();\n  _disallowedPop: SourcePos & { objectReusable: boolean } = {\n    identifierName: undefined,\n    line: undefined,\n    column: undefined,\n    filename: undefined,\n    objectReusable: true, // To avoid deleting and re-creating objects, we reuse existing objects when they are not needed anymore.\n  };\n\n  _allocQueue() {\n    const queue = this._queue;\n\n    for (let i = 0; i < 16; i++) {\n      queue.push({\n        char: 0,\n        repeat: 1,\n        line: undefined,\n        column: undefined,\n        identifierName: undefined,\n        filename: \"\",\n      });\n    }\n  }\n\n  _pushQueue(\n    char: number,\n    repeat: number,\n    line: number | undefined,\n    column: number | undefined,\n    identifierName: string | undefined,\n    filename: string | undefined,\n  ) {\n    const cursor = this._queueCursor;\n    if (cursor === this._queue.length) {\n      this._allocQueue();\n    }\n    const item = this._queue[cursor];\n    item.char = char;\n    item.repeat = repeat;\n    item.line = line;\n    item.column = column;\n    item.identifierName = identifierName;\n    item.filename = filename;\n\n    this._queueCursor++;\n  }\n\n  _popQueue(): QueueItem {\n    if (this._queueCursor === 0) {\n      throw new Error(\"Cannot pop from empty queue\");\n    }\n    return this._queue[--this._queueCursor];\n  }\n\n  /**\n   * Get the final string output from the buffer, along with the sourcemap if one exists.\n   */\n\n  get() {\n    this._flush();\n\n    const map = this._map;\n    const result = {\n      // Whatever trim is used here should not execute a regex against the\n      // source string since it may be arbitrarily large after all transformations\n      code: (this._buf + this._str).trimRight(),\n      // Decoded sourcemap is free to generate.\n      decodedMap: map?.getDecoded(),\n\n      // Encoding the sourcemap is moderately CPU expensive.\n      get map() {\n        const resultMap = map ? map.get() : null;\n        result.map = resultMap;\n        return resultMap;\n      },\n      set map(value) {\n        Object.defineProperty(result, \"map\", { value, writable: true });\n      },\n      // Retrieving the raw mappings is very memory intensive.\n      get rawMappings() {\n        const mappings = map?.getRawMappings();\n        result.rawMappings = mappings;\n        return mappings;\n      },\n      set rawMappings(value) {\n        Object.defineProperty(result, \"rawMappings\", { value, writable: true });\n      },\n    };\n\n    return result;\n  }\n\n  /**\n   * Add a string to the buffer that cannot be reverted.\n   */\n\n  append(str: string, maybeNewline: boolean): void {\n    this._flush();\n\n    this._append(str, this._sourcePosition, maybeNewline);\n  }\n\n  appendChar(char: number): void {\n    this._flush();\n    this._appendChar(char, 1, this._sourcePosition);\n  }\n\n  /**\n   * Add a string to the buffer than can be reverted.\n   */\n  queue(char: number): void {\n    // Drop trailing spaces when a newline is inserted.\n    if (char === charcodes.lineFeed) {\n      while (this._queueCursor !== 0) {\n        const char = this._queue[this._queueCursor - 1].char;\n        if (char !== charcodes.space && char !== charcodes.tab) {\n          break;\n        }\n\n        this._queueCursor--;\n      }\n    }\n\n    const sourcePosition = this._sourcePosition;\n    this._pushQueue(\n      char,\n      1,\n      sourcePosition.line,\n      sourcePosition.column,\n      sourcePosition.identifierName,\n      sourcePosition.filename,\n    );\n  }\n\n  /**\n   * Same as queue, but this indentation will never have a sourcmap marker.\n   */\n  queueIndentation(char: number, repeat: number): void {\n    this._pushQueue(char, repeat, undefined, undefined, undefined, undefined);\n  }\n\n  _flush(): void {\n    const queueCursor = this._queueCursor;\n    const queue = this._queue;\n    for (let i = 0; i < queueCursor; i++) {\n      const item: QueueItem = queue[i];\n      this._appendChar(item.char, item.repeat, item);\n    }\n    this._queueCursor = 0;\n  }\n\n  _appendChar(char: number, repeat: number, sourcePos: SourcePos): void {\n    this._last = char;\n\n    this._str +=\n      repeat > 1\n        ? String.fromCharCode(char).repeat(repeat)\n        : String.fromCharCode(char);\n\n    if (char !== charcodes.lineFeed) {\n      this._mark(\n        sourcePos.line,\n        sourcePos.column,\n        sourcePos.identifierName,\n        sourcePos.filename,\n      );\n      this._position.column += repeat;\n    } else {\n      this._position.line++;\n      this._position.column = 0;\n    }\n  }\n\n  _append(str: string, sourcePos: SourcePos, maybeNewline: boolean): void {\n    const len = str.length;\n\n    this._last = str.charCodeAt(len - 1);\n\n    if (++this._appendCount > 4096) {\n      +this._str; // Unexplainable huge performance boost. Ref: https://github.com/davidmarkclements/flatstr License: MIT\n      this._buf += this._str;\n      this._str = str;\n      this._appendCount = 0;\n    } else {\n      this._str += str;\n    }\n\n    if (!maybeNewline && !this._map) {\n      this._position.column += len;\n      return;\n    }\n\n    const { column, identifierName, filename } = sourcePos;\n    let line = sourcePos.line;\n\n    // Search for newline chars. We search only for `\\n`, since both `\\r` and\n    // `\\r\\n` are normalized to `\\n` during parse. We exclude `\\u2028` and\n    // `\\u2029` for performance reasons, they're so uncommon that it's probably\n    // ok. It's also unclear how other sourcemap utilities handle them...\n    let i = str.indexOf(\"\\n\");\n    let last = 0;\n\n    // If the string starts with a newline char, then adding a mark is redundant.\n    // This catches both \"no newlines\" and \"newline after several chars\".\n    if (i !== 0) {\n      this._mark(line, column, identifierName, filename);\n    }\n\n    // Now, find each reamining newline char in the string.\n    while (i !== -1) {\n      this._position.line++;\n      this._position.column = 0;\n      last = i + 1;\n\n      // We mark the start of each line, which happens directly after this newline char\n      // unless this is the last char.\n      if (last < str.length) {\n        this._mark(++line, 0, identifierName, filename);\n      }\n      i = str.indexOf(\"\\n\", last);\n    }\n    this._position.column += str.length - last;\n  }\n\n  _mark(\n    line: number | undefined,\n    column: number | undefined,\n    identifierName: string | undefined,\n    filename: string | undefined,\n  ): void {\n    this._map?.mark(this._position, line, column, identifierName, filename);\n  }\n\n  removeTrailingNewline(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.lineFeed\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  removeLastSemicolon(): void {\n    const queueCursor = this._queueCursor;\n    if (\n      queueCursor !== 0 &&\n      this._queue[queueCursor - 1].char === charcodes.semicolon\n    ) {\n      this._queueCursor--;\n    }\n  }\n\n  getLastChar(): number {\n    const queueCursor = this._queueCursor;\n    return queueCursor !== 0 ? this._queue[queueCursor - 1].char : this._last;\n  }\n\n  /**\n   * check if current _last + queue ends with newline, return the character before newline\n   *\n   * @param {*} ch\n   * @memberof Buffer\n   */\n  endsWithCharAndNewline(): number {\n    const queue = this._queue;\n    const queueCursor = this._queueCursor;\n    if (queueCursor !== 0) {\n      // every element in queue is one-length whitespace string\n      const lastCp = queue[queueCursor - 1].char;\n      if (lastCp !== charcodes.lineFeed) return;\n      if (queueCursor > 1) {\n        return queue[queueCursor - 2].char;\n      } else {\n        return this._last;\n      }\n    }\n    // We assume that everything being matched is at most a single token plus some whitespace,\n    // which everything currently is, but otherwise we'd have to expand _last or check _buf.\n  }\n\n  hasContent(): boolean {\n    return this._queueCursor !== 0 || !!this._last;\n  }\n\n  /**\n   * Certain sourcemap usecases expect mappings to be more accurate than\n   * Babel's generic sourcemap handling allows. For now, we special-case\n   * identifiers to allow for the primary cases to work.\n   * The goal of this line is to ensure that the map output from Babel will\n   * have an exact range on identifiers in the output code. Without this\n   * line, Babel would potentially include some number of trailing tokens\n   * that are printed after the identifier, but before another location has\n   * been assigned.\n   * This allows tooling like Rollup and Webpack to more accurately perform\n   * their own transformations. Most importantly, this allows the import/export\n   * transformations performed by those tools to loose less information when\n   * applying their own transformations on top of the code and map results\n   * generated by Babel itself.\n   *\n   * The primary example of this is the snippet:\n   *\n   *   import mod from \"mod\";\n   *   mod();\n   *\n   * With this line, there will be one mapping range over \"mod\" and another\n   * over \"();\", where previously it would have been a single mapping.\n   */\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!this._map) return cb();\n\n    this.source(\"start\", loc);\n\n    cb();\n\n    // In cases where tokens are printed after this item, we want to\n    // ensure that they get the location of the _end_ of the identifier.\n    // To accomplish this, we assign the location and explicitly disable\n    // the standard Buffer withSource previous-position \"reactivation\"\n    // logic. This means that if another item calls '.source()' to set\n    // the location after the identifier, it is fine, but the position won't\n    // be automatically replaced with the previous value.\n    this.source(\"end\", loc);\n    this._disallowPop(\"start\", loc);\n  }\n\n  /**\n   * Sets a given position as the current source location so generated code after this call\n   * will be given this position in the sourcemap.\n   */\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    // Since this is called extremely often, we re-use the same _sourcePosition\n    // object for the whole lifetime of the buffer.\n    this._normalizePosition(prop, loc, this._sourcePosition);\n  }\n\n  /**\n   * Call a callback with a specific source location and restore on completion.\n   */\n\n  withSource(prop: \"start\" | \"end\", loc: Loc, cb: () => void): void {\n    if (!this._map) return cb();\n\n    // Use the call stack to manage a stack of \"source location\" data because\n    // the _sourcePosition object is mutated over the course of code generation,\n    // and constantly copying it would be slower.\n    const originalLine = this._sourcePosition.line;\n    const originalColumn = this._sourcePosition.column;\n    const originalFilename = this._sourcePosition.filename;\n    const originalIdentifierName = this._sourcePosition.identifierName;\n\n    this.source(prop, loc);\n\n    cb();\n\n    if (\n      // Verify if reactivating this specific position has been disallowed.\n      this._disallowedPop.objectReusable ||\n      this._disallowedPop.line !== originalLine ||\n      this._disallowedPop.column !== originalColumn ||\n      this._disallowedPop.filename !== originalFilename\n    ) {\n      this._sourcePosition.line = originalLine;\n      this._sourcePosition.column = originalColumn;\n      this._sourcePosition.filename = originalFilename;\n      this._sourcePosition.identifierName = originalIdentifierName;\n      this._disallowedPop.objectReusable = true;\n    }\n  }\n\n  /**\n   * Allow printers to disable the default location-reset behavior of the\n   * sourcemap output, so that certain printers can be sure that the\n   * \"end\" location that they set is actually treated as the end position.\n   */\n  _disallowPop(prop: \"start\" | \"end\", loc: Loc) {\n    if (!loc) return;\n\n    const disallowedPop = this._disallowedPop;\n\n    this._normalizePosition(prop, loc, disallowedPop);\n\n    disallowedPop.objectReusable = false;\n  }\n\n  _normalizePosition(prop: \"start\" | \"end\", loc: Loc, targetObj: SourcePos) {\n    const pos = loc[prop];\n\n    targetObj.identifierName =\n      (prop === \"start\" && loc.identifierName) || undefined;\n    if (pos) {\n      targetObj.line = pos.line;\n      targetObj.column = pos.column;\n      targetObj.filename = loc.filename;\n    } else {\n      targetObj.line = null;\n      targetObj.column = null;\n      targetObj.filename = null;\n    }\n  }\n\n  getCurrentColumn(): number {\n    const queue = this._queue;\n\n    let lastIndex = -1;\n    let len = 0;\n    for (let i = 0; i < this._queueCursor; i++) {\n      const item = queue[i];\n      if (item.char === charcodes.lineFeed) {\n        lastIndex = i;\n        len += item.repeat;\n      }\n    }\n\n    return lastIndex === -1 ? this._position.column + len : len - 1 - lastIndex;\n  }\n\n  getCurrentLine(): number {\n    let count = 0;\n\n    const queue = this._queue;\n    for (let i = 0; i < this._queueCursor; i++) {\n      if (queue[i].char === charcodes.lineFeed) {\n        count++;\n      }\n    }\n\n    return this._position.line + count;\n  }\n}\n"], "mappings": ";;;;;;;AA6BA,SAASA,SAAT,GAAgC;EAC9B,OAAO;IACLC,cAAc,EAAEC,SADX;IAELC,IAAI,EAAED,SAFD;IAGLE,MAAM,EAAEF,SAHH;IAILG,QAAQ,EAAEH;EAJL,CAAP;AAMD;;AAEc,MAAMI,MAAN,CAAa;EAC1BC,WAAW,CAACC,GAAD,EAAyB;IAAA,KAMpCC,IANoC,GAMlB,IANkB;IAAA,KAOpCC,IAPoC,GAO7B,EAP6B;IAAA,KAQpCC,IARoC,GAQ7B,EAR6B;IAAA,KASpCC,YAToC,GASrB,CATqB;IAAA,KAUpCC,KAVoC,GAU5B,CAV4B;IAAA,KAWpCC,MAXoC,GAWd,EAXc;IAAA,KAYpCC,YAZoC,GAYrB,CAZqB;IAAA,KAcpCC,SAdoC,GAcxB;MACVb,IAAI,EAAE,CADI;MAEVC,MAAM,EAAE;IAFE,CAdwB;IAAA,KAkBpCa,eAlBoC,GAkBlBjB,SAAS,EAlBS;IAAA,KAmBpCkB,cAnBoC,GAmBsB;MACxDjB,cAAc,EAAEC,SADwC;MAExDC,IAAI,EAAED,SAFkD;MAGxDE,MAAM,EAAEF,SAHgD;MAIxDG,QAAQ,EAAEH,SAJ8C;MAKxDiB,cAAc,EAAE;IALwC,CAnBtB;IAClC,KAAKV,IAAL,GAAYD,GAAZ;;IAEA,KAAKY,WAAL;EACD;;EAuBDA,WAAW,GAAG;IACZ,MAAMC,KAAK,GAAG,KAAKP,MAAnB;;IAEA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;MAC3BD,KAAK,CAACE,IAAN,CAAW;QACTC,IAAI,EAAE,CADG;QAETC,MAAM,EAAE,CAFC;QAGTtB,IAAI,EAAED,SAHG;QAITE,MAAM,EAAEF,SAJC;QAKTD,cAAc,EAAEC,SALP;QAMTG,QAAQ,EAAE;MAND,CAAX;IAQD;EACF;;EAEDqB,UAAU,CACRF,IADQ,EAERC,MAFQ,EAGRtB,IAHQ,EAIRC,MAJQ,EAKRH,cALQ,EAMRI,QANQ,EAOR;IACA,MAAMsB,MAAM,GAAG,KAAKZ,YAApB;;IACA,IAAIY,MAAM,KAAK,KAAKb,MAAL,CAAYc,MAA3B,EAAmC;MACjC,KAAKR,WAAL;IACD;;IACD,MAAMS,IAAI,GAAG,KAAKf,MAAL,CAAYa,MAAZ,CAAb;IACAE,IAAI,CAACL,IAAL,GAAYA,IAAZ;IACAK,IAAI,CAACJ,MAAL,GAAcA,MAAd;IACAI,IAAI,CAAC1B,IAAL,GAAYA,IAAZ;IACA0B,IAAI,CAACzB,MAAL,GAAcA,MAAd;IACAyB,IAAI,CAAC5B,cAAL,GAAsBA,cAAtB;IACA4B,IAAI,CAACxB,QAAL,GAAgBA,QAAhB;IAEA,KAAKU,YAAL;EACD;;EAEDe,SAAS,GAAc;IACrB,IAAI,KAAKf,YAAL,KAAsB,CAA1B,EAA6B;MAC3B,MAAM,IAAIgB,KAAJ,CAAU,6BAAV,CAAN;IACD;;IACD,OAAO,KAAKjB,MAAL,CAAY,EAAE,KAAKC,YAAnB,CAAP;EACD;;EAMDiB,GAAG,GAAG;IACJ,KAAKC,MAAL;;IAEA,MAAMzB,GAAG,GAAG,KAAKC,IAAjB;IACA,MAAMyB,MAAM,GAAG;MAGbC,IAAI,EAAE,CAAC,KAAKzB,IAAL,GAAY,KAAKC,IAAlB,EAAwByB,SAAxB,EAHO;MAKbC,UAAU,EAAE7B,GAAF,oBAAEA,GAAG,CAAE8B,UAAL,EALC;;MAQb,IAAI9B,GAAJ,GAAU;QACR,MAAM+B,SAAS,GAAG/B,GAAG,GAAGA,GAAG,CAACwB,GAAJ,EAAH,GAAe,IAApC;QACAE,MAAM,CAAC1B,GAAP,GAAa+B,SAAb;QACA,OAAOA,SAAP;MACD,CAZY;;MAab,IAAI/B,GAAJ,CAAQgC,KAAR,EAAe;QACbC,MAAM,CAACC,cAAP,CAAsBR,MAAtB,EAA8B,KAA9B,EAAqC;UAAEM,KAAF;UAASG,QAAQ,EAAE;QAAnB,CAArC;MACD,CAfY;;MAiBb,IAAIC,WAAJ,GAAkB;QAChB,MAAMC,QAAQ,GAAGrC,GAAH,oBAAGA,GAAG,CAAEsC,cAAL,EAAjB;QACAZ,MAAM,CAACU,WAAP,GAAqBC,QAArB;QACA,OAAOA,QAAP;MACD,CArBY;;MAsBb,IAAID,WAAJ,CAAgBJ,KAAhB,EAAuB;QACrBC,MAAM,CAACC,cAAP,CAAsBR,MAAtB,EAA8B,aAA9B,EAA6C;UAAEM,KAAF;UAASG,QAAQ,EAAE;QAAnB,CAA7C;MACD;;IAxBY,CAAf;IA2BA,OAAOT,MAAP;EACD;;EAMDa,MAAM,CAACC,GAAD,EAAcC,YAAd,EAA2C;IAC/C,KAAKhB,MAAL;;IAEA,KAAKiB,OAAL,CAAaF,GAAb,EAAkB,KAAK/B,eAAvB,EAAwCgC,YAAxC;EACD;;EAEDE,UAAU,CAAC3B,IAAD,EAAqB;IAC7B,KAAKS,MAAL;;IACA,KAAKmB,WAAL,CAAiB5B,IAAjB,EAAuB,CAAvB,EAA0B,KAAKP,eAA/B;EACD;;EAKDI,KAAK,CAACG,IAAD,EAAqB;IAExB,IAAIA,IAAI,OAAR,EAAiC;MAC/B,OAAO,KAAKT,YAAL,KAAsB,CAA7B,EAAgC;QAC9B,MAAMS,IAAI,GAAG,KAAKV,MAAL,CAAY,KAAKC,YAAL,GAAoB,CAAhC,EAAmCS,IAAhD;;QACA,IAAIA,IAAI,OAAJ,IAA4BA,IAAI,MAApC,EAAwD;UACtD;QACD;;QAED,KAAKT,YAAL;MACD;IACF;;IAED,MAAMsC,cAAc,GAAG,KAAKpC,eAA5B;;IACA,KAAKS,UAAL,CACEF,IADF,EAEE,CAFF,EAGE6B,cAAc,CAAClD,IAHjB,EAIEkD,cAAc,CAACjD,MAJjB,EAKEiD,cAAc,CAACpD,cALjB,EAMEoD,cAAc,CAAChD,QANjB;EAQD;;EAKDiD,gBAAgB,CAAC9B,IAAD,EAAeC,MAAf,EAAqC;IACnD,KAAKC,UAAL,CAAgBF,IAAhB,EAAsBC,MAAtB,EAA8BvB,SAA9B,EAAyCA,SAAzC,EAAoDA,SAApD,EAA+DA,SAA/D;EACD;;EAED+B,MAAM,GAAS;IACb,MAAMsB,WAAW,GAAG,KAAKxC,YAAzB;IACA,MAAMM,KAAK,GAAG,KAAKP,MAAnB;;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiC,WAApB,EAAiCjC,CAAC,EAAlC,EAAsC;MACpC,MAAMO,IAAe,GAAGR,KAAK,CAACC,CAAD,CAA7B;;MACA,KAAK8B,WAAL,CAAiBvB,IAAI,CAACL,IAAtB,EAA4BK,IAAI,CAACJ,MAAjC,EAAyCI,IAAzC;IACD;;IACD,KAAKd,YAAL,GAAoB,CAApB;EACD;;EAEDqC,WAAW,CAAC5B,IAAD,EAAeC,MAAf,EAA+B+B,SAA/B,EAA2D;IACpE,KAAK3C,KAAL,GAAaW,IAAb;IAEA,KAAKb,IAAL,IACEc,MAAM,GAAG,CAAT,GACIgC,MAAM,CAACC,YAAP,CAAoBlC,IAApB,EAA0BC,MAA1B,CAAiCA,MAAjC,CADJ,GAEIgC,MAAM,CAACC,YAAP,CAAoBlC,IAApB,CAHN;;IAKA,IAAIA,IAAI,OAAR,EAAiC;MAC/B,KAAKmC,KAAL,CACEH,SAAS,CAACrD,IADZ,EAEEqD,SAAS,CAACpD,MAFZ,EAGEoD,SAAS,CAACvD,cAHZ,EAIEuD,SAAS,CAACnD,QAJZ;;MAMA,KAAKW,SAAL,CAAeZ,MAAf,IAAyBqB,MAAzB;IACD,CARD,MAQO;MACL,KAAKT,SAAL,CAAeb,IAAf;MACA,KAAKa,SAAL,CAAeZ,MAAf,GAAwB,CAAxB;IACD;EACF;;EAED8C,OAAO,CAACF,GAAD,EAAcQ,SAAd,EAAoCP,YAApC,EAAiE;IACtE,MAAMW,GAAG,GAAGZ,GAAG,CAACpB,MAAhB;IAEA,KAAKf,KAAL,GAAamC,GAAG,CAACa,UAAJ,CAAeD,GAAG,GAAG,CAArB,CAAb;;IAEA,IAAI,EAAE,KAAKhD,YAAP,GAAsB,IAA1B,EAAgC;MAC9B,CAAC,KAAKD,IAAN;MACA,KAAKD,IAAL,IAAa,KAAKC,IAAlB;MACA,KAAKA,IAAL,GAAYqC,GAAZ;MACA,KAAKpC,YAAL,GAAoB,CAApB;IACD,CALD,MAKO;MACL,KAAKD,IAAL,IAAaqC,GAAb;IACD;;IAED,IAAI,CAACC,YAAD,IAAiB,CAAC,KAAKxC,IAA3B,EAAiC;MAC/B,KAAKO,SAAL,CAAeZ,MAAf,IAAyBwD,GAAzB;MACA;IACD;;IAED,MAAM;MAAExD,MAAF;MAAUH,cAAV;MAA0BI;IAA1B,IAAuCmD,SAA7C;IACA,IAAIrD,IAAI,GAAGqD,SAAS,CAACrD,IAArB;IAMA,IAAImB,CAAC,GAAG0B,GAAG,CAACc,OAAJ,CAAY,IAAZ,CAAR;IACA,IAAIC,IAAI,GAAG,CAAX;;IAIA,IAAIzC,CAAC,KAAK,CAAV,EAAa;MACX,KAAKqC,KAAL,CAAWxD,IAAX,EAAiBC,MAAjB,EAAyBH,cAAzB,EAAyCI,QAAzC;IACD;;IAGD,OAAOiB,CAAC,KAAK,CAAC,CAAd,EAAiB;MACf,KAAKN,SAAL,CAAeb,IAAf;MACA,KAAKa,SAAL,CAAeZ,MAAf,GAAwB,CAAxB;MACA2D,IAAI,GAAGzC,CAAC,GAAG,CAAX;;MAIA,IAAIyC,IAAI,GAAGf,GAAG,CAACpB,MAAf,EAAuB;QACrB,KAAK+B,KAAL,CAAW,EAAExD,IAAb,EAAmB,CAAnB,EAAsBF,cAAtB,EAAsCI,QAAtC;MACD;;MACDiB,CAAC,GAAG0B,GAAG,CAACc,OAAJ,CAAY,IAAZ,EAAkBC,IAAlB,CAAJ;IACD;;IACD,KAAK/C,SAAL,CAAeZ,MAAf,IAAyB4C,GAAG,CAACpB,MAAJ,GAAamC,IAAtC;EACD;;EAEDJ,KAAK,CACHxD,IADG,EAEHC,MAFG,EAGHH,cAHG,EAIHI,QAJG,EAKG;IAAA;;IACN,mBAAKI,IAAL,gCAAWuD,IAAX,CAAgB,KAAKhD,SAArB,EAAgCb,IAAhC,EAAsCC,MAAtC,EAA8CH,cAA9C,EAA8DI,QAA9D;EACD;;EAED4D,qBAAqB,GAAS;IAC5B,MAAMV,WAAW,GAAG,KAAKxC,YAAzB;;IACA,IACEwC,WAAW,KAAK,CAAhB,IACA,KAAKzC,MAAL,CAAYyC,WAAW,GAAG,CAA1B,EAA6B/B,IAA7B,OAFF,EAGE;MACA,KAAKT,YAAL;IACD;EACF;;EAEDmD,mBAAmB,GAAS;IAC1B,MAAMX,WAAW,GAAG,KAAKxC,YAAzB;;IACA,IACEwC,WAAW,KAAK,CAAhB,IACA,KAAKzC,MAAL,CAAYyC,WAAW,GAAG,CAA1B,EAA6B/B,IAA7B,OAFF,EAGE;MACA,KAAKT,YAAL;IACD;EACF;;EAEDoD,WAAW,GAAW;IACpB,MAAMZ,WAAW,GAAG,KAAKxC,YAAzB;IACA,OAAOwC,WAAW,KAAK,CAAhB,GAAoB,KAAKzC,MAAL,CAAYyC,WAAW,GAAG,CAA1B,EAA6B/B,IAAjD,GAAwD,KAAKX,KAApE;EACD;;EAQDuD,sBAAsB,GAAW;IAC/B,MAAM/C,KAAK,GAAG,KAAKP,MAAnB;IACA,MAAMyC,WAAW,GAAG,KAAKxC,YAAzB;;IACA,IAAIwC,WAAW,KAAK,CAApB,EAAuB;MAErB,MAAMc,MAAM,GAAGhD,KAAK,CAACkC,WAAW,GAAG,CAAf,CAAL,CAAuB/B,IAAtC;MACA,IAAI6C,MAAM,OAAV,EAAmC;;MACnC,IAAId,WAAW,GAAG,CAAlB,EAAqB;QACnB,OAAOlC,KAAK,CAACkC,WAAW,GAAG,CAAf,CAAL,CAAuB/B,IAA9B;MACD,CAFD,MAEO;QACL,OAAO,KAAKX,KAAZ;MACD;IACF;EAGF;;EAEDyD,UAAU,GAAY;IACpB,OAAO,KAAKvD,YAAL,KAAsB,CAAtB,IAA2B,CAAC,CAAC,KAAKF,KAAzC;EACD;;EAyBD0D,WAAW,CAACC,GAAD,EAAuBC,EAAvB,EAAuC;IAChD,IAAI,CAAC,KAAKhE,IAAV,EAAgB,OAAOgE,EAAE,EAAT;IAEhB,KAAKC,MAAL,CAAY,OAAZ,EAAqBF,GAArB;IAEAC,EAAE;IASF,KAAKC,MAAL,CAAY,KAAZ,EAAmBF,GAAnB;;IACA,KAAKG,YAAL,CAAkB,OAAlB,EAA2BH,GAA3B;EACD;;EAODE,MAAM,CAACE,IAAD,EAAwBJ,GAAxB,EAAoD;IACxD,IAAI,CAACA,GAAL,EAAU;;IAIV,KAAKK,kBAAL,CAAwBD,IAAxB,EAA8BJ,GAA9B,EAAmC,KAAKvD,eAAxC;EACD;;EAMD6D,UAAU,CAACF,IAAD,EAAwBJ,GAAxB,EAAkCC,EAAlC,EAAwD;IAChE,IAAI,CAAC,KAAKhE,IAAV,EAAgB,OAAOgE,EAAE,EAAT;IAKhB,MAAMM,YAAY,GAAG,KAAK9D,eAAL,CAAqBd,IAA1C;IACA,MAAM6E,cAAc,GAAG,KAAK/D,eAAL,CAAqBb,MAA5C;IACA,MAAM6E,gBAAgB,GAAG,KAAKhE,eAAL,CAAqBZ,QAA9C;IACA,MAAM6E,sBAAsB,GAAG,KAAKjE,eAAL,CAAqBhB,cAApD;IAEA,KAAKyE,MAAL,CAAYE,IAAZ,EAAkBJ,GAAlB;IAEAC,EAAE;;IAEF,IAEE,KAAKvD,cAAL,CAAoBC,cAApB,IACA,KAAKD,cAAL,CAAoBf,IAApB,KAA6B4E,YAD7B,IAEA,KAAK7D,cAAL,CAAoBd,MAApB,KAA+B4E,cAF/B,IAGA,KAAK9D,cAAL,CAAoBb,QAApB,KAAiC4E,gBALnC,EAME;MACA,KAAKhE,eAAL,CAAqBd,IAArB,GAA4B4E,YAA5B;MACA,KAAK9D,eAAL,CAAqBb,MAArB,GAA8B4E,cAA9B;MACA,KAAK/D,eAAL,CAAqBZ,QAArB,GAAgC4E,gBAAhC;MACA,KAAKhE,eAAL,CAAqBhB,cAArB,GAAsCiF,sBAAtC;MACA,KAAKhE,cAAL,CAAoBC,cAApB,GAAqC,IAArC;IACD;EACF;;EAODwD,YAAY,CAACC,IAAD,EAAwBJ,GAAxB,EAAkC;IAC5C,IAAI,CAACA,GAAL,EAAU;IAEV,MAAMW,aAAa,GAAG,KAAKjE,cAA3B;;IAEA,KAAK2D,kBAAL,CAAwBD,IAAxB,EAA8BJ,GAA9B,EAAmCW,aAAnC;;IAEAA,aAAa,CAAChE,cAAd,GAA+B,KAA/B;EACD;;EAED0D,kBAAkB,CAACD,IAAD,EAAwBJ,GAAxB,EAAkCY,SAAlC,EAAwD;IACxE,MAAMC,GAAG,GAAGb,GAAG,CAACI,IAAD,CAAf;IAEAQ,SAAS,CAACnF,cAAV,GACG2E,IAAI,KAAK,OAAT,IAAoBJ,GAAG,CAACvE,cAAzB,IAA4CC,SAD9C;;IAEA,IAAImF,GAAJ,EAAS;MACPD,SAAS,CAACjF,IAAV,GAAiBkF,GAAG,CAAClF,IAArB;MACAiF,SAAS,CAAChF,MAAV,GAAmBiF,GAAG,CAACjF,MAAvB;MACAgF,SAAS,CAAC/E,QAAV,GAAqBmE,GAAG,CAACnE,QAAzB;IACD,CAJD,MAIO;MACL+E,SAAS,CAACjF,IAAV,GAAiB,IAAjB;MACAiF,SAAS,CAAChF,MAAV,GAAmB,IAAnB;MACAgF,SAAS,CAAC/E,QAAV,GAAqB,IAArB;IACD;EACF;;EAEDiF,gBAAgB,GAAW;IACzB,MAAMjE,KAAK,GAAG,KAAKP,MAAnB;IAEA,IAAIyE,SAAS,GAAG,CAAC,CAAjB;IACA,IAAI3B,GAAG,GAAG,CAAV;;IACA,KAAK,IAAItC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKP,YAAzB,EAAuCO,CAAC,EAAxC,EAA4C;MAC1C,MAAMO,IAAI,GAAGR,KAAK,CAACC,CAAD,CAAlB;;MACA,IAAIO,IAAI,CAACL,IAAL,OAAJ,EAAsC;QACpC+D,SAAS,GAAGjE,CAAZ;QACAsC,GAAG,IAAI/B,IAAI,CAACJ,MAAZ;MACD;IACF;;IAED,OAAO8D,SAAS,KAAK,CAAC,CAAf,GAAmB,KAAKvE,SAAL,CAAeZ,MAAf,GAAwBwD,GAA3C,GAAiDA,GAAG,GAAG,CAAN,GAAU2B,SAAlE;EACD;;EAEDC,cAAc,GAAW;IACvB,IAAIC,KAAK,GAAG,CAAZ;IAEA,MAAMpE,KAAK,GAAG,KAAKP,MAAnB;;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKP,YAAzB,EAAuCO,CAAC,EAAxC,EAA4C;MAC1C,IAAID,KAAK,CAACC,CAAD,CAAL,CAASE,IAAT,OAAJ,EAA0C;QACxCiE,KAAK;MACN;IACF;;IAED,OAAO,KAAKzE,SAAL,CAAeb,IAAf,GAAsBsF,KAA7B;EACD;;AAlcyB"}