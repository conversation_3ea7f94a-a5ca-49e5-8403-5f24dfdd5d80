{"version": 3, "names": ["version", "DEFAULT_EXTENSIONS", "Object", "freeze", "OptionManager", "init", "opts", "loadOptionsSync", "Plugin", "alias", "Error"], "sources": ["../src/index.ts"], "sourcesContent": ["declare const PACKAGE_JSON: { name: string; version: string };\nexport const version = PACKAGE_JSON.version;\n\nexport { default as File } from \"./transformation/file/file\";\nexport type { default as PluginPass } from \"./transformation/plugin-pass\";\nexport { default as buildExternalHelpers } from \"./tools/build-external-helpers\";\nexport { resolvePlugin, resolvePreset } from \"./config/files\";\n\nexport { getEnv } from \"./config/helpers/environment\";\n\n// NOTE: Lazy re-exports aren't detected by the Node.js CJS-ESM interop.\n// These are handled by pluginInjectNodeReexportsHints in our babel.config.js\n// so that they can work well.\nexport * as types from \"@babel/types\";\nexport { tokTypes } from \"@babel/parser\";\nexport { default as traverse } from \"@babel/traverse\";\nexport { default as template } from \"@babel/template\";\n\nexport {\n  createConfigItem,\n  createConfigItemSync,\n  createConfigItemAsync,\n} from \"./config\";\n\nexport {\n  loadPartialConfig,\n  loadPartialConfigSync,\n  loadPartialConfigAsync,\n  loadOptions,\n  loadOptionsSync,\n  loadOptionsAsync,\n} from \"./config\";\n\nexport type {\n  CallerMetadata,\n  InputOptions,\n  PluginAPI,\n  PluginObject,\n  PresetAPI,\n  PresetObject,\n} from \"./config\";\n\nexport {\n  transform,\n  transformSync,\n  transformAsync,\n  type FileResult,\n} from \"./transform\";\nexport {\n  transformFile,\n  transformFileSync,\n  transformFileAsync,\n} from \"./transform-file\";\nexport {\n  transformFromAst,\n  transformFromAstSync,\n  transformFromAstAsync,\n} from \"./transform-ast\";\nexport { parse, parseSync, parseAsync } from \"./parse\";\n\n/**\n * Recommended set of compilable extensions. Not used in @babel/core directly, but meant as\n * as an easy source for tooling making use of @babel/core.\n */\nexport const DEFAULT_EXTENSIONS = Object.freeze([\n  \".js\",\n  \".jsx\",\n  \".es6\",\n  \".es\",\n  \".mjs\",\n  \".cjs\",\n] as const);\n\n// For easier backward-compatibility, provide an API like the one we exposed in Babel 6.\nimport { loadOptionsSync } from \"./config\";\nexport class OptionManager {\n  init(opts: {}) {\n    return loadOptionsSync(opts);\n  }\n}\n\nexport function Plugin(alias: string) {\n  throw new Error(\n    `The (${alias}) Babel 5 plugin is being run with an unsupported Babel version.`,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;;AAEA;;AACA;;AAEA;;;;;;;;;;;;;;;;;;;AAMA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAwBA;;AAMA;;AAKA;;AAKA;;AAzDO,MAAMA,OAAO,WAAb;;AA+DA,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,MAAP,CAAc,CAC9C,KAD8C,EAE9C,MAF8C,EAG9C,MAH8C,EAI9C,KAJ8C,EAK9C,MAL8C,EAM9C,MAN8C,CAAd,CAA3B;;;AAWA,MAAMC,aAAN,CAAoB;EACzBC,IAAI,CAACC,IAAD,EAAW;IACb,OAAO,IAAAC,uBAAA,EAAgBD,IAAhB,CAAP;EACD;;AAHwB;;;;AAMpB,SAASE,MAAT,CAAgBC,KAAhB,EAA+B;EACpC,MAAM,IAAIC,KAAJ,CACH,QAAOD,KAAM,kEADV,CAAN;AAGD"}