module.exports={A:{A:{"2":"J D E F 4B","129":"A B"},B:{"1":"P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H","129":"C K","1025":"L G M N O"},C:{"2":"0 5B pB I q J D E F A B C K L G M N O r s t u v w x y z 6B 7B","513":"1 2 3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B","2":"I q"},E:{"1":"q J D E F A B C K L G BC CC DC EC vB mB nB wB FC GC xB yB zB 0B oB 1B HC","2":"I AC uB"},F:{"1":"0 1 2 3 4 5 6 7 8 9 F B C G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a IC JC KC LC mB 2B MC nB"},G:{"388":"E uB NC 3B OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B"},H:{"2":"hC"},I:{"2":"pB iC jC kC","388":"I H lC 3B mC nC"},J:{"2":"D","388":"A"},K:{"1":"A B C mB 2B nB","388":"c"},L:{"388":"H"},M:{"641":"b"},N:{"388":"A B"},O:{"388":"oC"},P:{"388":"I pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"388":"wB"},R:{"388":"1C"},S:{"513":"2C"}},B:1,C:"Number input type"};
