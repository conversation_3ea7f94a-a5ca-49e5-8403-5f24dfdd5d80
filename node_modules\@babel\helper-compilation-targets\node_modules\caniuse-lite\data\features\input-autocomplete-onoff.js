module.exports={A:{A:{"1":"J D E F A 4B","132":"B"},B:{"132":"C K L G M N O","260":"P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H"},C:{"1":"0 1 5B pB I q J D E F A B C K L G M N O r s t u v w x y z 6B 7B","516":"2 3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB"},D:{"1":"N O r s t u v w x y","2":"I q J D E F A B C K L G M","132":"0 1 2 3 4 5 6 7 8 9 z AB BB CB","260":"DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B"},E:{"1":"J BC CC","2":"I q AC uB","2052":"D E F A B C K L G DC EC vB mB nB wB FC GC xB yB zB 0B oB 1B HC"},F:{"1":"0 1 2 3 4 5 6 7 8 9 F B C G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a IC JC KC LC mB 2B MC nB"},G:{"2":"uB NC 3B","1025":"E OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B"},H:{"1025":"hC"},I:{"1":"pB I H iC jC kC lC 3B mC nC"},J:{"1":"D A"},K:{"1":"A B C c mB 2B nB"},L:{"1":"H"},M:{"1":"b"},N:{"2052":"A B"},O:{"1025":"oC"},P:{"1":"I pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"260":"wB"},R:{"1":"1C"},S:{"516":"2C"}},B:1,C:"autocomplete attribute: on & off values"};
