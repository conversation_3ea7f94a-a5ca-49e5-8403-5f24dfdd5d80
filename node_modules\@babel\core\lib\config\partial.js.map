{"version": 3, "names": ["resolveRootMode", "rootDir", "rootMode", "upwardRootDir", "findConfigUpwards", "Object", "assign", "Error", "ROOT_CONFIG_FILENAMES", "join", "code", "dirname", "loadPrivatePartialConfig", "inputOpts", "Array", "isArray", "args", "validate", "envName", "getEnv", "cwd", "root", "caller", "cloneInputAst", "absoluteCwd", "path", "resolve", "absoluteRootDir", "filename", "undefined", "showConfigPath", "resolveShowConfigPath", "context", "showConfig", "config<PERSON><PERSON><PERSON>", "buildRootChain", "merged", "assumptions", "options", "for<PERSON>ach", "opts", "mergeOptions", "targets", "resolveTargets", "babelrc", "configFile", "browserslistConfigFile", "passPerPreset", "plugins", "map", "descriptor", "createItemFromDescriptor", "presets", "fileHandling", "ignore", "config", "files", "loadPartialConfig", "gens<PERSON>", "showIgnoredFiles", "result", "item", "value", "Plugin", "PartialConfig", "filepath", "constructor", "babelignore", "freeze", "hasFilesystemConfig", "prototype"], "sources": ["../../src/config/partial.ts"], "sourcesContent": ["import path from \"path\";\nimport gensync from \"gensync\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport Plugin from \"./plugin\";\nimport { mergeOptions } from \"./util\";\nimport { createItemFromDescriptor } from \"./item\";\nimport { buildRoot<PERSON>hain } from \"./config-chain\";\nimport type { ConfigContext, FileHandling } from \"./config-chain\";\nimport { getEnv } from \"./helpers/environment\";\nimport { validate } from \"./validation/options\";\n\nimport type {\n  ValidatedOptions,\n  NormalizedOptions,\n  RootMode,\n} from \"./validation/options\";\n\nimport {\n  findConfigUpwards,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./files\";\nimport type { ConfigFile, IgnoreFile } from \"./files\";\nimport { resolveTargets } from \"./resolve-targets\";\n\nfunction resolveRootMode(rootDir: string, rootMode: RootMode): string {\n  switch (rootMode) {\n    case \"root\":\n      return rootDir;\n\n    case \"upward-optional\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      return upwardRootDir === null ? rootDir : upwardRootDir;\n    }\n\n    case \"upward\": {\n      const upwardRootDir = findConfigUpwards(rootDir);\n      if (upwardRootDir !== null) return upwardRootDir;\n\n      throw Object.assign(\n        new Error(\n          `Babel was run with rootMode:\"upward\" but a root could not ` +\n            `be found when searching upward from \"${rootDir}\".\\n` +\n            `One of the following config files must be in the directory tree: ` +\n            `\"${ROOT_CONFIG_FILENAMES.join(\", \")}\".`,\n        ) as any,\n        {\n          code: \"BABEL_ROOT_NOT_FOUND\",\n          dirname: rootDir,\n        },\n      );\n    }\n    default:\n      throw new Error(`Assertion failure - unknown rootMode value.`);\n  }\n}\n\ntype PrivPartialConfig = {\n  options: NormalizedOptions;\n  context: ConfigContext;\n  fileHandling: FileHandling;\n  ignore: IgnoreFile | void;\n  babelrc: ConfigFile | void;\n  config: ConfigFile | void;\n  files: Set<string>;\n};\n\nexport default function* loadPrivatePartialConfig(\n  inputOpts: unknown,\n): Handler<PrivPartialConfig | null> {\n  if (\n    inputOpts != null &&\n    (typeof inputOpts !== \"object\" || Array.isArray(inputOpts))\n  ) {\n    throw new Error(\"Babel options must be an object, null, or undefined\");\n  }\n\n  const args = inputOpts ? validate(\"arguments\", inputOpts) : {};\n\n  const {\n    envName = getEnv(),\n    cwd = \".\",\n    root: rootDir = \".\",\n    rootMode = \"root\",\n    caller,\n    cloneInputAst = true,\n  } = args;\n  const absoluteCwd = path.resolve(cwd);\n  const absoluteRootDir = resolveRootMode(\n    path.resolve(absoluteCwd, rootDir),\n    rootMode,\n  );\n\n  const filename =\n    typeof args.filename === \"string\"\n      ? path.resolve(cwd, args.filename)\n      : undefined;\n\n  const showConfigPath = yield* resolveShowConfigPath(absoluteCwd);\n\n  const context: ConfigContext = {\n    filename,\n    cwd: absoluteCwd,\n    root: absoluteRootDir,\n    envName,\n    caller,\n    showConfig: showConfigPath === filename,\n  };\n\n  const configChain = yield* buildRootChain(args, context);\n  if (!configChain) return null;\n\n  const merged: ValidatedOptions = {\n    assumptions: {},\n  };\n  configChain.options.forEach(opts => {\n    mergeOptions(merged as any, opts);\n  });\n\n  const options: NormalizedOptions = {\n    ...merged,\n    targets: resolveTargets(merged, absoluteRootDir),\n\n    // Tack the passes onto the object itself so that, if this object is\n    // passed back to Babel a second time, it will be in the right structure\n    // to not change behavior.\n    cloneInputAst,\n    babelrc: false,\n    configFile: false,\n    browserslistConfigFile: false,\n    passPerPreset: false,\n    envName: context.envName,\n    cwd: context.cwd,\n    root: context.root,\n    rootMode: \"root\",\n    filename:\n      typeof context.filename === \"string\" ? context.filename : undefined,\n\n    plugins: configChain.plugins.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n    presets: configChain.presets.map(descriptor =>\n      createItemFromDescriptor(descriptor),\n    ),\n  };\n\n  return {\n    options,\n    context,\n    fileHandling: configChain.fileHandling,\n    ignore: configChain.ignore,\n    babelrc: configChain.babelrc,\n    config: configChain.config,\n    files: configChain.files,\n  };\n}\n\ntype LoadPartialConfigOpts = {\n  showIgnoredFiles?: boolean;\n};\n\nexport const loadPartialConfig = gensync(function* (\n  opts?: LoadPartialConfigOpts,\n): Handler<PartialConfig | null> {\n  let showIgnoredFiles = false;\n  // We only extract showIgnoredFiles if opts is an object, so that\n  // loadPrivatePartialConfig can throw the appropriate error if it's not.\n  if (typeof opts === \"object\" && opts !== null && !Array.isArray(opts)) {\n    ({ showIgnoredFiles, ...opts } = opts);\n  }\n\n  const result: PrivPartialConfig | undefined | null =\n    yield* loadPrivatePartialConfig(opts);\n  if (!result) return null;\n\n  const { options, babelrc, ignore, config, fileHandling, files } = result;\n\n  if (fileHandling === \"ignored\" && !showIgnoredFiles) {\n    return null;\n  }\n\n  (options.plugins || []).forEach(item => {\n    // @ts-expect-error todo(flow->ts): better type annotation for `item.value`\n    if (item.value instanceof Plugin) {\n      throw new Error(\n        \"Passing cached plugin instances is not supported in \" +\n          \"babel.loadPartialConfig()\",\n      );\n    }\n  });\n\n  return new PartialConfig(\n    options,\n    babelrc ? babelrc.filepath : undefined,\n    ignore ? ignore.filepath : undefined,\n    config ? config.filepath : undefined,\n    fileHandling,\n    files,\n  );\n});\n\nexport type { PartialConfig };\n\nclass PartialConfig {\n  /**\n   * These properties are public, so any changes to them should be considered\n   * a breaking change to Babel's API.\n   */\n  options: NormalizedOptions;\n  babelrc: string | void;\n  babelignore: string | void;\n  config: string | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n\n  constructor(\n    options: NormalizedOptions,\n    babelrc: string | void,\n    ignore: string | void,\n    config: string | void,\n    fileHandling: FileHandling,\n    files: Set<string>,\n  ) {\n    this.options = options;\n    this.babelignore = ignore;\n    this.babelrc = babelrc;\n    this.config = config;\n    this.fileHandling = fileHandling;\n    this.files = files;\n\n    // Freeze since this is a public API and it should be extremely obvious that\n    // reassigning properties on here does nothing.\n    Object.freeze(this);\n  }\n\n  /**\n   * Returns true if there is a config file in the filesystem for this config.\n   */\n  hasFilesystemConfig(): boolean {\n    return this.babelrc !== undefined || this.config !== undefined;\n  }\n}\nObject.freeze(PartialConfig.prototype);\n"], "mappings": ";;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AAEA;;AACA;;AAQA;;AAMA;;;;;;AAEA,SAASA,eAAT,CAAyBC,OAAzB,EAA0CC,QAA1C,EAAsE;EACpE,QAAQA,QAAR;IACE,KAAK,MAAL;MACE,OAAOD,OAAP;;IAEF,KAAK,iBAAL;MAAwB;QACtB,MAAME,aAAa,GAAG,IAAAC,wBAAA,EAAkBH,OAAlB,CAAtB;QACA,OAAOE,aAAa,KAAK,IAAlB,GAAyBF,OAAzB,GAAmCE,aAA1C;MACD;;IAED,KAAK,QAAL;MAAe;QACb,MAAMA,aAAa,GAAG,IAAAC,wBAAA,EAAkBH,OAAlB,CAAtB;QACA,IAAIE,aAAa,KAAK,IAAtB,EAA4B,OAAOA,aAAP;QAE5B,MAAME,MAAM,CAACC,MAAP,CACJ,IAAIC,KAAJ,CACG,4DAAD,GACG,wCAAuCN,OAAQ,MADlD,GAEG,mEAFH,GAGG,IAAGO,4BAAA,CAAsBC,IAAtB,CAA2B,IAA3B,CAAiC,IAJzC,CADI,EAOJ;UACEC,IAAI,EAAE,sBADR;UAEEC,OAAO,EAAEV;QAFX,CAPI,CAAN;MAYD;;IACD;MACE,MAAM,IAAIM,KAAJ,CAAW,6CAAX,CAAN;EA3BJ;AA6BD;;AAYc,UAAUK,wBAAV,CACbC,SADa,EAEsB;EACnC,IACEA,SAAS,IAAI,IAAb,KACC,OAAOA,SAAP,KAAqB,QAArB,IAAiCC,KAAK,CAACC,OAAN,CAAcF,SAAd,CADlC,CADF,EAGE;IACA,MAAM,IAAIN,KAAJ,CAAU,qDAAV,CAAN;EACD;;EAED,MAAMS,IAAI,GAAGH,SAAS,GAAG,IAAAI,iBAAA,EAAS,WAAT,EAAsBJ,SAAtB,CAAH,GAAsC,EAA5D;EAEA,MAAM;IACJK,OAAO,GAAG,IAAAC,mBAAA,GADN;IAEJC,GAAG,GAAG,GAFF;IAGJC,IAAI,EAAEpB,OAAO,GAAG,GAHZ;IAIJC,QAAQ,GAAG,MAJP;IAKJoB,MALI;IAMJC,aAAa,GAAG;EANZ,IAOFP,IAPJ;;EAQA,MAAMQ,WAAW,GAAGC,OAAA,CAAKC,OAAL,CAAaN,GAAb,CAApB;;EACA,MAAMO,eAAe,GAAG3B,eAAe,CACrCyB,OAAA,CAAKC,OAAL,CAAaF,WAAb,EAA0BvB,OAA1B,CADqC,EAErCC,QAFqC,CAAvC;EAKA,MAAM0B,QAAQ,GACZ,OAAOZ,IAAI,CAACY,QAAZ,KAAyB,QAAzB,GACIH,OAAA,CAAKC,OAAL,CAAaN,GAAb,EAAkBJ,IAAI,CAACY,QAAvB,CADJ,GAEIC,SAHN;EAKA,MAAMC,cAAc,GAAG,OAAO,IAAAC,4BAAA,EAAsBP,WAAtB,CAA9B;EAEA,MAAMQ,OAAsB,GAAG;IAC7BJ,QAD6B;IAE7BR,GAAG,EAAEI,WAFwB;IAG7BH,IAAI,EAAEM,eAHuB;IAI7BT,OAJ6B;IAK7BI,MAL6B;IAM7BW,UAAU,EAAEH,cAAc,KAAKF;EANF,CAA/B;EASA,MAAMM,WAAW,GAAG,OAAO,IAAAC,2BAAA,EAAenB,IAAf,EAAqBgB,OAArB,CAA3B;EACA,IAAI,CAACE,WAAL,EAAkB,OAAO,IAAP;EAElB,MAAME,MAAwB,GAAG;IAC/BC,WAAW,EAAE;EADkB,CAAjC;EAGAH,WAAW,CAACI,OAAZ,CAAoBC,OAApB,CAA4BC,IAAI,IAAI;IAClC,IAAAC,kBAAA,EAAaL,MAAb,EAA4BI,IAA5B;EACD,CAFD;EAIA,MAAMF,OAA0B,qBAC3BF,MAD2B;IAE9BM,OAAO,EAAE,IAAAC,8BAAA,EAAeP,MAAf,EAAuBT,eAAvB,CAFqB;IAO9BJ,aAP8B;IAQ9BqB,OAAO,EAAE,KARqB;IAS9BC,UAAU,EAAE,KATkB;IAU9BC,sBAAsB,EAAE,KAVM;IAW9BC,aAAa,EAAE,KAXe;IAY9B7B,OAAO,EAAEc,OAAO,CAACd,OAZa;IAa9BE,GAAG,EAAEY,OAAO,CAACZ,GAbiB;IAc9BC,IAAI,EAAEW,OAAO,CAACX,IAdgB;IAe9BnB,QAAQ,EAAE,MAfoB;IAgB9B0B,QAAQ,EACN,OAAOI,OAAO,CAACJ,QAAf,KAA4B,QAA5B,GAAuCI,OAAO,CAACJ,QAA/C,GAA0DC,SAjB9B;IAmB9BmB,OAAO,EAAEd,WAAW,CAACc,OAAZ,CAAoBC,GAApB,CAAwBC,UAAU,IACzC,IAAAC,8BAAA,EAAyBD,UAAzB,CADO,CAnBqB;IAsB9BE,OAAO,EAAElB,WAAW,CAACkB,OAAZ,CAAoBH,GAApB,CAAwBC,UAAU,IACzC,IAAAC,8BAAA,EAAyBD,UAAzB,CADO;EAtBqB,EAAhC;EA2BA,OAAO;IACLZ,OADK;IAELN,OAFK;IAGLqB,YAAY,EAAEnB,WAAW,CAACmB,YAHrB;IAILC,MAAM,EAAEpB,WAAW,CAACoB,MAJf;IAKLV,OAAO,EAAEV,WAAW,CAACU,OALhB;IAMLW,MAAM,EAAErB,WAAW,CAACqB,MANf;IAOLC,KAAK,EAAEtB,WAAW,CAACsB;EAPd,CAAP;AASD;;AAMM,MAAMC,iBAAiB,GAAGC,UAAA,CAAQ,WACvClB,IADuC,EAER;EAC/B,IAAImB,gBAAgB,GAAG,KAAvB;;EAGA,IAAI,OAAOnB,IAAP,KAAgB,QAAhB,IAA4BA,IAAI,KAAK,IAArC,IAA6C,CAAC1B,KAAK,CAACC,OAAN,CAAcyB,IAAd,CAAlD,EAAuE;IAAA,YACpCA,IADoC;IAAA,CACpE;MAAEmB;IAAF,SADoE;IAC7CnB,IAD6C;IAAA;EAEtE;;EAED,MAAMoB,MAA4C,GAChD,OAAOhD,wBAAwB,CAAC4B,IAAD,CADjC;EAEA,IAAI,CAACoB,MAAL,EAAa,OAAO,IAAP;EAEb,MAAM;IAAEtB,OAAF;IAAWM,OAAX;IAAoBU,MAApB;IAA4BC,MAA5B;IAAoCF,YAApC;IAAkDG;EAAlD,IAA4DI,MAAlE;;EAEA,IAAIP,YAAY,KAAK,SAAjB,IAA8B,CAACM,gBAAnC,EAAqD;IACnD,OAAO,IAAP;EACD;;EAED,CAACrB,OAAO,CAACU,OAAR,IAAmB,EAApB,EAAwBT,OAAxB,CAAgCsB,IAAI,IAAI;IAEtC,IAAIA,IAAI,CAACC,KAAL,YAAsBC,eAA1B,EAAkC;MAChC,MAAM,IAAIxD,KAAJ,CACJ,yDACE,2BAFE,CAAN;IAID;EACF,CARD;EAUA,OAAO,IAAIyD,aAAJ,CACL1B,OADK,EAELM,OAAO,GAAGA,OAAO,CAACqB,QAAX,GAAsBpC,SAFxB,EAGLyB,MAAM,GAAGA,MAAM,CAACW,QAAV,GAAqBpC,SAHtB,EAIL0B,MAAM,GAAGA,MAAM,CAACU,QAAV,GAAqBpC,SAJtB,EAKLwB,YALK,EAMLG,KANK,CAAP;AAQD,CAtCgC,CAA1B;;;;AA0CP,MAAMQ,aAAN,CAAoB;EAYlBE,WAAW,CACT5B,OADS,EAETM,OAFS,EAGTU,MAHS,EAITC,MAJS,EAKTF,YALS,EAMTG,KANS,EAOT;IAAA,KAdFlB,OAcE;IAAA,KAbFM,OAaE;IAAA,KAZFuB,WAYE;IAAA,KAXFZ,MAWE;IAAA,KAVFF,YAUE;IAAA,KATFG,KASE;IACA,KAAKlB,OAAL,GAAeA,OAAf;IACA,KAAK6B,WAAL,GAAmBb,MAAnB;IACA,KAAKV,OAAL,GAAeA,OAAf;IACA,KAAKW,MAAL,GAAcA,MAAd;IACA,KAAKF,YAAL,GAAoBA,YAApB;IACA,KAAKG,KAAL,GAAaA,KAAb;IAIAnD,MAAM,CAAC+D,MAAP,CAAc,IAAd;EACD;;EAKDC,mBAAmB,GAAY;IAC7B,OAAO,KAAKzB,OAAL,KAAiBf,SAAjB,IAA8B,KAAK0B,MAAL,KAAgB1B,SAArD;EACD;;AArCiB;;AAuCpBxB,MAAM,CAAC+D,MAAP,CAAcJ,aAAa,CAACM,SAA5B"}