{"version": 3, "names": ["TSTypeAnnotation", "node", "token", "space", "optional", "print", "typeAnnotation", "TSTypeParameterInstantiation", "parent", "printList", "params", "type", "length", "TSTypeParameter", "in", "word", "out", "name", "constraint", "default", "TSParameterProperty", "accessibility", "readonly", "_param", "parameter", "TSDeclareFunction", "declare", "_functionHead", "TSDeclareMethod", "_classMethodHead", "TSQualifiedName", "left", "right", "TSCallSignatureDeclaration", "tsPrintSignatureDeclarationBase", "TSConstructSignatureDeclaration", "TSPropertySignature", "initializer", "tsPrintPropertyOrMethodName", "computed", "key", "TSMethodSignature", "kind", "TSIndexSignature", "static", "isStatic", "_parameters", "parameters", "TSAnyKeyword", "TSBigIntKeyword", "TSUnknownKeyword", "TSNumberKeyword", "TSObjectKeyword", "TSBooleanKeyword", "TSStringKeyword", "TSSymbolKeyword", "TSVoidKeyword", "TSUndefinedKeyword", "TSNullKeyword", "TSNeverKeyword", "TSIntrinsicKeyword", "TSThisType", "TSFunctionType", "tsPrintFunctionOrConstructorType", "TSConstructorType", "abstract", "typeParameters", "returnType", "TSTypeReference", "typeName", "TSTypePredicate", "asserts", "parameterName", "TSTypeQuery", "exprName", "TSTypeLiteral", "tsPrintTypeLiteralOrInterfaceBody", "members", "tsPrintBraced", "printer", "indent", "newline", "member", "dedent", "rightBrace", "TSArrayType", "elementType", "TSTupleType", "elementTypes", "TSOptionalType", "TSRestType", "TSNamedTupleMember", "label", "TSUnionType", "tsPrintUnionOrIntersectionType", "TSIntersectionType", "sep", "printJoin", "types", "separator", "TSConditionalType", "checkType", "extendsType", "trueType", "falseType", "TSInferType", "typeParameter", "TSParenthesizedType", "TSTypeOperator", "operator", "TSIndexedAccessType", "objectType", "indexType", "TSMappedType", "nameType", "tokenIfPlusMinus", "self", "tok", "TSLiteralType", "literal", "TSExpressionWithTypeArguments", "expression", "TSInterfaceDeclaration", "id", "extends", "extendz", "body", "TSInterfaceBody", "TSTypeAliasDeclaration", "TSAsExpression", "TSTypeAssertion", "TSInstantiationExpression", "TSEnumDeclaration", "const", "isConst", "TSEnumMember", "TSModuleDeclaration", "global", "TSModuleBlock", "TSImportType", "argument", "qualifier", "TSImportEqualsDeclaration", "isExport", "moduleReference", "TSExternalModuleReference", "TSNonNullExpression", "TSExportAssignment", "TSNamespaceExportDeclaration", "tsPrintClassMemberModifiers", "isField", "override"], "sources": ["../../src/generators/typescript.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function TSTypeAnnotation(this: Printer, node: t.TSTypeAnnotation) {\n  this.token(\":\");\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSTypeParameterInstantiation(\n  this: Printer,\n  node: t.TSTypeParameterInstantiation,\n  parent: t.Node,\n): void {\n  this.token(\"<\");\n  this.printList(node.params, node, {});\n  if (parent.type === \"ArrowFunctionExpression\" && node.params.length === 1) {\n    this.token(\",\");\n  }\n  this.token(\">\");\n}\n\nexport { TSTypeParameterInstantiation as TSTypeParameterDeclaration };\n\nexport function TSTypeParameter(this: Printer, node: t.TSTypeParameter) {\n  if (node.in) {\n    this.word(\"in\");\n    this.space();\n  }\n\n  if (node.out) {\n    this.word(\"out\");\n    this.space();\n  }\n\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (node.name as unknown as string)\n      : (node.name as unknown as t.Identifier).name,\n  );\n\n  if (node.constraint) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.constraint, node);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default, node);\n  }\n}\n\nexport function TSParameterProperty(\n  this: Printer,\n  node: t.TSParameterProperty,\n) {\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n\n  if (node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this._param(node.parameter);\n}\n\nexport function TSDeclareFunction(this: Printer, node: t.TSDeclareFunction) {\n  if (node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this._functionHead(node);\n  this.token(\";\");\n}\n\nexport function TSDeclareMethod(this: Printer, node: t.TSDeclareMethod) {\n  this._classMethodHead(node);\n  this.token(\";\");\n}\n\nexport function TSQualifiedName(this: Printer, node: t.TSQualifiedName) {\n  this.print(node.left, node);\n  this.token(\".\");\n  this.print(node.right, node);\n}\n\nexport function TSCallSignatureDeclaration(\n  this: Printer,\n  node: t.TSCallSignatureDeclaration,\n) {\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSConstructSignatureDeclaration(\n  this: Printer,\n  node: t.TSConstructSignatureDeclaration,\n) {\n  this.word(\"new\");\n  this.space();\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSPropertySignature(\n  this: Printer,\n  node: t.TSPropertySignature,\n) {\n  const { readonly, initializer } = node;\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.print(node.typeAnnotation, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\";\");\n}\n\nexport function tsPrintPropertyOrMethodName(\n  this: Printer,\n  node: t.TSPropertySignature | t.TSMethodSignature,\n) {\n  if (node.computed) {\n    this.token(\"[\");\n  }\n  this.print(node.key, node);\n  if (node.computed) {\n    this.token(\"]\");\n  }\n  if (node.optional) {\n    this.token(\"?\");\n  }\n}\n\nexport function TSMethodSignature(this: Printer, node: t.TSMethodSignature) {\n  const { kind } = node;\n  if (kind === \"set\" || kind === \"get\") {\n    this.word(kind);\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSIndexSignature(this: Printer, node: t.TSIndexSignature) {\n  const { readonly, static: isStatic } = node;\n  if (isStatic) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.token(\"[\");\n  this._parameters(node.parameters, node);\n  this.token(\"]\");\n  this.print(node.typeAnnotation, node);\n  this.token(\";\");\n}\n\nexport function TSAnyKeyword(this: Printer) {\n  this.word(\"any\");\n}\nexport function TSBigIntKeyword(this: Printer) {\n  this.word(\"bigint\");\n}\nexport function TSUnknownKeyword(this: Printer) {\n  this.word(\"unknown\");\n}\nexport function TSNumberKeyword(this: Printer) {\n  this.word(\"number\");\n}\nexport function TSObjectKeyword(this: Printer) {\n  this.word(\"object\");\n}\nexport function TSBooleanKeyword(this: Printer) {\n  this.word(\"boolean\");\n}\nexport function TSStringKeyword(this: Printer) {\n  this.word(\"string\");\n}\nexport function TSSymbolKeyword(this: Printer) {\n  this.word(\"symbol\");\n}\nexport function TSVoidKeyword(this: Printer) {\n  this.word(\"void\");\n}\nexport function TSUndefinedKeyword(this: Printer) {\n  this.word(\"undefined\");\n}\nexport function TSNullKeyword(this: Printer) {\n  this.word(\"null\");\n}\nexport function TSNeverKeyword(this: Printer) {\n  this.word(\"never\");\n}\nexport function TSIntrinsicKeyword(this: Printer) {\n  this.word(\"intrinsic\");\n}\n\nexport function TSThisType(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TSFunctionType(this: Printer, node: t.TSFunctionType) {\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function TSConstructorType(this: Printer, node: t.TSConstructorType) {\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  this.word(\"new\");\n  this.space();\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function tsPrintFunctionOrConstructorType(\n  this: Printer,\n  node: t.TSFunctionType | t.TSConstructorType,\n) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.params\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"=>\");\n  this.space();\n  const returnType = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.returnType\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.typeAnnotation;\n  this.print(returnType.typeAnnotation, node);\n}\n\nexport function TSTypeReference(this: Printer, node: t.TSTypeReference) {\n  this.print(node.typeName, node, true);\n  this.print(node.typeParameters, node, true);\n}\n\nexport function TSTypePredicate(this: Printer, node: t.TSTypePredicate) {\n  if (node.asserts) {\n    this.word(\"asserts\");\n    this.space();\n  }\n  this.print(node.parameterName);\n  if (node.typeAnnotation) {\n    this.space();\n    this.word(\"is\");\n    this.space();\n    this.print(node.typeAnnotation.typeAnnotation);\n  }\n}\n\nexport function TSTypeQuery(this: Printer, node: t.TSTypeQuery) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.exprName);\n\n  if (node.typeParameters) {\n    this.print(node.typeParameters, node);\n  }\n}\n\nexport function TSTypeLiteral(this: Printer, node: t.TSTypeLiteral) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.members, node);\n}\n\nexport function tsPrintTypeLiteralOrInterfaceBody(\n  this: Printer,\n  members: t.TSTypeElement[],\n  node: t.TSType | t.TSInterfaceBody,\n) {\n  tsPrintBraced(this, members, node);\n}\n\nfunction tsPrintBraced(printer: Printer, members: t.Node[], node: t.Node) {\n  printer.token(\"{\");\n  if (members.length) {\n    printer.indent();\n    printer.newline();\n    for (const member of members) {\n      printer.print(member, node);\n      //this.token(sep);\n      printer.newline();\n    }\n    printer.dedent();\n    printer.rightBrace();\n  } else {\n    printer.token(\"}\");\n  }\n}\n\nexport function TSArrayType(this: Printer, node: t.TSArrayType) {\n  this.print(node.elementType, node, true);\n\n  this.token(\"[]\");\n}\n\nexport function TSTupleType(this: Printer, node: t.TSTupleType) {\n  this.token(\"[\");\n  this.printList(node.elementTypes, node);\n  this.token(\"]\");\n}\n\nexport function TSOptionalType(this: Printer, node: t.TSOptionalType) {\n  this.print(node.typeAnnotation, node);\n  this.token(\"?\");\n}\n\nexport function TSRestType(this: Printer, node: t.TSRestType) {\n  this.token(\"...\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSNamedTupleMember(this: Printer, node: t.TSNamedTupleMember) {\n  this.print(node.label, node);\n  if (node.optional) this.token(\"?\");\n  this.token(\":\");\n  this.space();\n  this.print(node.elementType, node);\n}\n\nexport function TSUnionType(this: Printer, node: t.TSUnionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"|\");\n}\n\nexport function TSIntersectionType(this: Printer, node: t.TSIntersectionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"&\");\n}\n\nfunction tsPrintUnionOrIntersectionType(\n  printer: Printer,\n  node: t.TSUnionType | t.TSIntersectionType,\n  sep: \"|\" | \"&\",\n) {\n  printer.printJoin(node.types, node, {\n    separator() {\n      this.space();\n      this.token(sep);\n      this.space();\n    },\n  });\n}\n\nexport function TSConditionalType(this: Printer, node: t.TSConditionalType) {\n  this.print(node.checkType);\n  this.space();\n  this.word(\"extends\");\n  this.space();\n  this.print(node.extendsType);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.trueType);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.falseType);\n}\n\nexport function TSInferType(this: Printer, node: t.TSInferType) {\n  this.token(\"infer\");\n  this.space();\n  this.print(node.typeParameter);\n}\n\nexport function TSParenthesizedType(\n  this: Printer,\n  node: t.TSParenthesizedType,\n) {\n  this.token(\"(\");\n  this.print(node.typeAnnotation, node);\n  this.token(\")\");\n}\n\nexport function TSTypeOperator(this: Printer, node: t.TSTypeOperator) {\n  this.word(node.operator);\n  this.space();\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSIndexedAccessType(\n  this: Printer,\n  node: t.TSIndexedAccessType,\n) {\n  this.print(node.objectType, node, true);\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n\nexport function TSMappedType(this: Printer, node: t.TSMappedType) {\n  const { nameType, optional, readonly, typeParameter } = node;\n  this.token(\"{\");\n  this.space();\n  if (readonly) {\n    tokenIfPlusMinus(this, readonly);\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this.token(\"[\");\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (typeParameter.name as unknown as string)\n      : (typeParameter.name as unknown as t.Identifier).name,\n  );\n  this.space();\n  this.word(\"in\");\n  this.space();\n  this.print(typeParameter.constraint, typeParameter);\n\n  if (nameType) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(nameType, node);\n  }\n\n  this.token(\"]\");\n\n  if (optional) {\n    tokenIfPlusMinus(this, optional);\n    this.token(\"?\");\n  }\n  this.token(\":\");\n  this.space();\n  this.print(node.typeAnnotation, node);\n  this.space();\n  this.token(\"}\");\n}\n\nfunction tokenIfPlusMinus(self: Printer, tok: true | \"+\" | \"-\") {\n  if (tok !== true) {\n    self.token(tok);\n  }\n}\n\nexport function TSLiteralType(this: Printer, node: t.TSLiteralType) {\n  this.print(node.literal, node);\n}\n\nexport function TSExpressionWithTypeArguments(\n  this: Printer,\n  node: t.TSExpressionWithTypeArguments,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSInterfaceDeclaration(\n  this: Printer,\n  node: t.TSInterfaceDeclaration,\n) {\n  const { declare, id, typeParameters, extends: extendz, body } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"interface\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  if (extendz?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(extendz, node);\n  }\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSInterfaceBody(this: Printer, node: t.TSInterfaceBody) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.body, node);\n}\n\nexport function TSTypeAliasDeclaration(\n  this: Printer,\n  node: t.TSTypeAliasDeclaration,\n) {\n  const { declare, id, typeParameters, typeAnnotation } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"type\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(typeAnnotation, node);\n  this.token(\";\");\n}\n\nexport function TSAsExpression(this: Printer, node: t.TSAsExpression) {\n  const { expression, typeAnnotation } = node;\n  this.print(expression, node);\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(typeAnnotation, node);\n}\n\nexport function TSTypeAssertion(this: Printer, node: t.TSTypeAssertion) {\n  const { typeAnnotation, expression } = node;\n  this.token(\"<\");\n  this.print(typeAnnotation, node);\n  this.token(\">\");\n  this.space();\n  this.print(expression, node);\n}\n\nexport function TSInstantiationExpression(\n  this: Printer,\n  node: t.TSInstantiationExpression,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSEnumDeclaration(this: Printer, node: t.TSEnumDeclaration) {\n  const { declare, const: isConst, id, members } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (isConst) {\n    this.word(\"const\");\n    this.space();\n  }\n  this.word(\"enum\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  tsPrintBraced(this, members, node);\n}\n\nexport function TSEnumMember(this: Printer, node: t.TSEnumMember) {\n  const { id, initializer } = node;\n  this.print(id, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\",\");\n}\n\nexport function TSModuleDeclaration(\n  this: Printer,\n  node: t.TSModuleDeclaration,\n) {\n  const { declare, id } = node;\n\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (!node.global) {\n    this.word(id.type === \"Identifier\" ? \"namespace\" : \"module\");\n    this.space();\n  }\n  this.print(id, node);\n\n  if (!node.body) {\n    this.token(\";\");\n    return;\n  }\n\n  let body = node.body;\n  while (body.type === \"TSModuleDeclaration\") {\n    this.token(\".\");\n    this.print(body.id, body);\n    body = body.body;\n  }\n\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSModuleBlock(this: Printer, node: t.TSModuleBlock) {\n  tsPrintBraced(this, node.body, node);\n}\n\nexport function TSImportType(this: Printer, node: t.TSImportType) {\n  const { argument, qualifier, typeParameters } = node;\n  this.word(\"import\");\n  this.token(\"(\");\n  this.print(argument, node);\n  this.token(\")\");\n  if (qualifier) {\n    this.token(\".\");\n    this.print(qualifier, node);\n  }\n  if (typeParameters) {\n    this.print(typeParameters, node);\n  }\n}\n\nexport function TSImportEqualsDeclaration(\n  this: Printer,\n  node: t.TSImportEqualsDeclaration,\n) {\n  const { isExport, id, moduleReference } = node;\n  if (isExport) {\n    this.word(\"export\");\n    this.space();\n  }\n  this.word(\"import\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(moduleReference, node);\n  this.token(\";\");\n}\n\nexport function TSExternalModuleReference(\n  this: Printer,\n  node: t.TSExternalModuleReference,\n) {\n  this.token(\"require(\");\n  this.print(node.expression, node);\n  this.token(\")\");\n}\n\nexport function TSNonNullExpression(\n  this: Printer,\n  node: t.TSNonNullExpression,\n) {\n  this.print(node.expression, node);\n  this.token(\"!\");\n}\n\nexport function TSExportAssignment(this: Printer, node: t.TSExportAssignment) {\n  this.word(\"export\");\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.expression, node);\n  this.token(\";\");\n}\n\nexport function TSNamespaceExportDeclaration(\n  this: Printer,\n  node: t.TSNamespaceExportDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.word(\"namespace\");\n  this.space();\n  this.print(node.id, node);\n}\n\nexport function tsPrintSignatureDeclarationBase(this: Printer, node: any) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? node.params\n    : node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  const returnType = process.env.BABEL_8_BREAKING\n    ? node.returnType\n    : node.typeAnnotation;\n  this.print(returnType, node);\n}\n\nexport function tsPrintClassMemberModifiers(\n  this: Printer,\n  node:\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.TSDeclareMethod,\n) {\n  const isField =\n    node.type === \"ClassAccessorProperty\" || node.type === \"ClassProperty\";\n  if (isField && node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.override) {\n    this.word(\"override\");\n    this.space();\n  }\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  if (isField && node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAASA,gBAAT,CAAyCC,IAAzC,EAAmE;EACxE,KAAKC,SAAL;EACA,KAAKC,KAAL;EAEA,IAAIF,IAAI,CAACG,QAAT,EAAmB,KAAKF,SAAL;EACnB,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAASM,4BAAT,CAELN,IAFK,EAGLO,MAHK,EAIC;EACN,KAAKN,SAAL;EACA,KAAKO,SAAL,CAAeR,IAAI,CAACS,MAApB,EAA4BT,IAA5B,EAAkC,EAAlC;;EACA,IAAIO,MAAM,CAACG,IAAP,KAAgB,yBAAhB,IAA6CV,IAAI,CAACS,MAAL,CAAYE,MAAZ,KAAuB,CAAxE,EAA2E;IACzE,KAAKV,SAAL;EACD;;EACD,KAAKA,SAAL;AACD;;AAIM,SAASW,eAAT,CAAwCZ,IAAxC,EAAiE;EACtE,IAAIA,IAAI,CAACa,EAAT,EAAa;IACX,KAAKC,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;EACD;;EAED,IAAIF,IAAI,CAACe,GAAT,EAAc;IACZ,KAAKD,IAAL,CAAU,KAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKY,IAAL,CAEOd,IAAI,CAACgB,IAFZ;;EAMA,IAAIhB,IAAI,CAACiB,UAAT,EAAqB;IACnB,KAAKf,KAAL;IACA,KAAKY,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACiB,UAAhB,EAA4BjB,IAA5B;EACD;;EAED,IAAIA,IAAI,CAACkB,OAAT,EAAkB;IAChB,KAAKhB,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACkB,OAAhB,EAAyBlB,IAAzB;EACD;AACF;;AAEM,SAASmB,mBAAT,CAELnB,IAFK,EAGL;EACA,IAAIA,IAAI,CAACoB,aAAT,EAAwB;IACtB,KAAKN,IAAL,CAAUd,IAAI,CAACoB,aAAf;IACA,KAAKlB,KAAL;EACD;;EAED,IAAIF,IAAI,CAACqB,QAAT,EAAmB;IACjB,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKoB,MAAL,CAAYtB,IAAI,CAACuB,SAAjB;AACD;;AAEM,SAASC,iBAAT,CAA0CxB,IAA1C,EAAqE;EAC1E,IAAIA,IAAI,CAACyB,OAAT,EAAkB;IAChB,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKwB,aAAL,CAAmB1B,IAAnB;;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS0B,eAAT,CAAwC3B,IAAxC,EAAiE;EACtE,KAAK4B,gBAAL,CAAsB5B,IAAtB;;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS4B,eAAT,CAAwC7B,IAAxC,EAAiE;EACtE,KAAKI,KAAL,CAAWJ,IAAI,CAAC8B,IAAhB,EAAsB9B,IAAtB;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAAC+B,KAAhB,EAAuB/B,IAAvB;AACD;;AAEM,SAASgC,0BAAT,CAELhC,IAFK,EAGL;EACA,KAAKiC,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiC,+BAAT,CAELlC,IAFK,EAGL;EACA,KAAKc,IAAL,CAAU,KAAV;EACA,KAAKZ,KAAL;EACA,KAAK+B,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASkC,mBAAT,CAELnC,IAFK,EAGL;EACA,MAAM;IAAEqB,QAAF;IAAYe;EAAZ,IAA4BpC,IAAlC;;EACA,IAAIqB,QAAJ,EAAc;IACZ,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKmC,2BAAL,CAAiCrC,IAAjC;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;;EACA,IAAIoC,WAAJ,EAAiB;IACf,KAAKlC,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWgC,WAAX,EAAwBpC,IAAxB;EACD;;EACD,KAAKC,SAAL;AACD;;AAEM,SAASoC,2BAAT,CAELrC,IAFK,EAGL;EACA,IAAIA,IAAI,CAACsC,QAAT,EAAmB;IACjB,KAAKrC,SAAL;EACD;;EACD,KAAKG,KAAL,CAAWJ,IAAI,CAACuC,GAAhB,EAAqBvC,IAArB;;EACA,IAAIA,IAAI,CAACsC,QAAT,EAAmB;IACjB,KAAKrC,SAAL;EACD;;EACD,IAAID,IAAI,CAACG,QAAT,EAAmB;IACjB,KAAKF,SAAL;EACD;AACF;;AAEM,SAASuC,iBAAT,CAA0CxC,IAA1C,EAAqE;EAC1E,MAAM;IAAEyC;EAAF,IAAWzC,IAAjB;;EACA,IAAIyC,IAAI,KAAK,KAAT,IAAkBA,IAAI,KAAK,KAA/B,EAAsC;IACpC,KAAK3B,IAAL,CAAU2B,IAAV;IACA,KAAKvC,KAAL;EACD;;EACD,KAAKmC,2BAAL,CAAiCrC,IAAjC;EACA,KAAKiC,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASyC,gBAAT,CAAyC1C,IAAzC,EAAmE;EACxE,MAAM;IAAEqB,QAAF;IAAYsB,MAAM,EAAEC;EAApB,IAAiC5C,IAAvC;;EACA,IAAI4C,QAAJ,EAAc;IACZ,KAAK9B,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAImB,QAAJ,EAAc;IACZ,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKD,SAAL;;EACA,KAAK4C,WAAL,CAAiB7C,IAAI,CAAC8C,UAAtB,EAAkC9C,IAAlC;;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS8C,YAAT,GAAqC;EAC1C,KAAKjC,IAAL,CAAU,KAAV;AACD;;AACM,SAASkC,eAAT,GAAwC;EAC7C,KAAKlC,IAAL,CAAU,QAAV;AACD;;AACM,SAASmC,gBAAT,GAAyC;EAC9C,KAAKnC,IAAL,CAAU,SAAV;AACD;;AACM,SAASoC,eAAT,GAAwC;EAC7C,KAAKpC,IAAL,CAAU,QAAV;AACD;;AACM,SAASqC,eAAT,GAAwC;EAC7C,KAAKrC,IAAL,CAAU,QAAV;AACD;;AACM,SAASsC,gBAAT,GAAyC;EAC9C,KAAKtC,IAAL,CAAU,SAAV;AACD;;AACM,SAASuC,eAAT,GAAwC;EAC7C,KAAKvC,IAAL,CAAU,QAAV;AACD;;AACM,SAASwC,eAAT,GAAwC;EAC7C,KAAKxC,IAAL,CAAU,QAAV;AACD;;AACM,SAASyC,aAAT,GAAsC;EAC3C,KAAKzC,IAAL,CAAU,MAAV;AACD;;AACM,SAAS0C,kBAAT,GAA2C;EAChD,KAAK1C,IAAL,CAAU,WAAV;AACD;;AACM,SAAS2C,aAAT,GAAsC;EAC3C,KAAK3C,IAAL,CAAU,MAAV;AACD;;AACM,SAAS4C,cAAT,GAAuC;EAC5C,KAAK5C,IAAL,CAAU,OAAV;AACD;;AACM,SAAS6C,kBAAT,GAA2C;EAChD,KAAK7C,IAAL,CAAU,WAAV;AACD;;AAEM,SAAS8C,UAAT,GAAmC;EACxC,KAAK9C,IAAL,CAAU,MAAV;AACD;;AAEM,SAAS+C,cAAT,CAAuC7D,IAAvC,EAA+D;EACpE,KAAK8D,gCAAL,CAAsC9D,IAAtC;AACD;;AAEM,SAAS+D,iBAAT,CAA0C/D,IAA1C,EAAqE;EAC1E,IAAIA,IAAI,CAACgE,QAAT,EAAmB;IACjB,KAAKlD,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,KAAV;EACA,KAAKZ,KAAL;EACA,KAAK4D,gCAAL,CAAsC9D,IAAtC;AACD;;AAEM,SAAS8D,gCAAT,CAEL9D,IAFK,EAGL;EACA,MAAM;IAAEiE;EAAF,IAAqBjE,IAA3B;EACA,MAAM8C,UAAU,GAIZ9C,IAAI,CAAC8C,UAJT;EAKA,KAAK1C,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKC,SAAL;;EACA,KAAK4C,WAAL,CAAiBC,UAAjB,EAA6B9C,IAA7B;;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;EACA,KAAKD,KAAL,CAAW,IAAX;EACA,KAAKC,KAAL;EACA,MAAMgE,UAAU,GAIZlE,IAAI,CAACK,cAJT;EAKA,KAAKD,KAAL,CAAW8D,UAAU,CAAC7D,cAAtB,EAAsCL,IAAtC;AACD;;AAEM,SAASmE,eAAT,CAAwCnE,IAAxC,EAAiE;EACtE,KAAKI,KAAL,CAAWJ,IAAI,CAACoE,QAAhB,EAA0BpE,IAA1B,EAAgC,IAAhC;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC,EAAsC,IAAtC;AACD;;AAEM,SAASqE,eAAT,CAAwCrE,IAAxC,EAAiE;EACtE,IAAIA,IAAI,CAACsE,OAAT,EAAkB;IAChB,KAAKxD,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKE,KAAL,CAAWJ,IAAI,CAACuE,aAAhB;;EACA,IAAIvE,IAAI,CAACK,cAAT,EAAyB;IACvB,KAAKH,KAAL;IACA,KAAKY,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAL,CAAoBA,cAA/B;EACD;AACF;;AAEM,SAASmE,WAAT,CAAoCxE,IAApC,EAAyD;EAC9D,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACyE,QAAhB;;EAEA,IAAIzE,IAAI,CAACiE,cAAT,EAAyB;IACvB,KAAK7D,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;EACD;AACF;;AAEM,SAAS0E,aAAT,CAAsC1E,IAAtC,EAA6D;EAClE,KAAK2E,iCAAL,CAAuC3E,IAAI,CAAC4E,OAA5C,EAAqD5E,IAArD;AACD;;AAEM,SAAS2E,iCAAT,CAELC,OAFK,EAGL5E,IAHK,EAIL;EACA6E,aAAa,CAAC,IAAD,EAAOD,OAAP,EAAgB5E,IAAhB,CAAb;AACD;;AAED,SAAS6E,aAAT,CAAuBC,OAAvB,EAAyCF,OAAzC,EAA4D5E,IAA5D,EAA0E;EACxE8E,OAAO,CAAC7E,KAAR,CAAc,GAAd;;EACA,IAAI2E,OAAO,CAACjE,MAAZ,EAAoB;IAClBmE,OAAO,CAACC,MAAR;IACAD,OAAO,CAACE,OAAR;;IACA,KAAK,MAAMC,MAAX,IAAqBL,OAArB,EAA8B;MAC5BE,OAAO,CAAC1E,KAAR,CAAc6E,MAAd,EAAsBjF,IAAtB;MAEA8E,OAAO,CAACE,OAAR;IACD;;IACDF,OAAO,CAACI,MAAR;IACAJ,OAAO,CAACK,UAAR;EACD,CAVD,MAUO;IACLL,OAAO,CAAC7E,KAAR,CAAc,GAAd;EACD;AACF;;AAEM,SAASmF,WAAT,CAAoCpF,IAApC,EAAyD;EAC9D,KAAKI,KAAL,CAAWJ,IAAI,CAACqF,WAAhB,EAA6BrF,IAA7B,EAAmC,IAAnC;EAEA,KAAKC,KAAL,CAAW,IAAX;AACD;;AAEM,SAASqF,WAAT,CAAoCtF,IAApC,EAAyD;EAC9D,KAAKC,SAAL;EACA,KAAKO,SAAL,CAAeR,IAAI,CAACuF,YAApB,EAAkCvF,IAAlC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASuF,cAAT,CAAuCxF,IAAvC,EAA+D;EACpE,KAAKI,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASwF,UAAT,CAAmCzF,IAAnC,EAAuD;EAC5D,KAAKC,KAAL,CAAW,KAAX;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAAS0F,kBAAT,CAA2C1F,IAA3C,EAAuE;EAC5E,KAAKI,KAAL,CAAWJ,IAAI,CAAC2F,KAAhB,EAAuB3F,IAAvB;EACA,IAAIA,IAAI,CAACG,QAAT,EAAmB,KAAKF,SAAL;EACnB,KAAKA,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACqF,WAAhB,EAA6BrF,IAA7B;AACD;;AAEM,SAAS4F,WAAT,CAAoC5F,IAApC,EAAyD;EAC9D6F,8BAA8B,CAAC,IAAD,EAAO7F,IAAP,EAAa,GAAb,CAA9B;AACD;;AAEM,SAAS8F,kBAAT,CAA2C9F,IAA3C,EAAuE;EAC5E6F,8BAA8B,CAAC,IAAD,EAAO7F,IAAP,EAAa,GAAb,CAA9B;AACD;;AAED,SAAS6F,8BAAT,CACEf,OADF,EAEE9E,IAFF,EAGE+F,GAHF,EAIE;EACAjB,OAAO,CAACkB,SAAR,CAAkBhG,IAAI,CAACiG,KAAvB,EAA8BjG,IAA9B,EAAoC;IAClCkG,SAAS,GAAG;MACV,KAAKhG,KAAL;MACA,KAAKD,KAAL,CAAW8F,GAAX;MACA,KAAK7F,KAAL;IACD;;EALiC,CAApC;AAOD;;AAEM,SAASiG,iBAAT,CAA0CnG,IAA1C,EAAqE;EAC1E,KAAKI,KAAL,CAAWJ,IAAI,CAACoG,SAAhB;EACA,KAAKlG,KAAL;EACA,KAAKY,IAAL,CAAU,SAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACqG,WAAhB;EACA,KAAKnG,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACsG,QAAhB;EACA,KAAKpG,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACuG,SAAhB;AACD;;AAEM,SAASC,WAAT,CAAoCxG,IAApC,EAAyD;EAC9D,KAAKC,KAAL,CAAW,OAAX;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACyG,aAAhB;AACD;;AAEM,SAASC,mBAAT,CAEL1G,IAFK,EAGL;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS0G,cAAT,CAAuC3G,IAAvC,EAA+D;EACpE,KAAKc,IAAL,CAAUd,IAAI,CAAC4G,QAAf;EACA,KAAK1G,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAAS6G,mBAAT,CAEL7G,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAAC8G,UAAhB,EAA4B9G,IAA5B,EAAkC,IAAlC;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAAC+G,SAAhB,EAA2B/G,IAA3B;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS+G,YAAT,CAAqChH,IAArC,EAA2D;EAChE,MAAM;IAAEiH,QAAF;IAAY9G,QAAZ;IAAsBkB,QAAtB;IAAgCoF;EAAhC,IAAkDzG,IAAxD;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;;EACA,IAAImB,QAAJ,EAAc;IACZ6F,gBAAgB,CAAC,IAAD,EAAO7F,QAAP,CAAhB;IACA,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKD,SAAL;EACA,KAAKa,IAAL,CAEO2F,aAAa,CAACzF,IAFrB;EAKA,KAAKd,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWqG,aAAa,CAACxF,UAAzB,EAAqCwF,aAArC;;EAEA,IAAIQ,QAAJ,EAAc;IACZ,KAAK/G,KAAL;IACA,KAAKY,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAW6G,QAAX,EAAqBjH,IAArB;EACD;;EAED,KAAKC,SAAL;;EAEA,IAAIE,QAAJ,EAAc;IACZ+G,gBAAgB,CAAC,IAAD,EAAO/G,QAAP,CAAhB;IACA,KAAKF,SAAL;EACD;;EACD,KAAKA,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;AACD;;AAED,SAASiH,gBAAT,CAA0BC,IAA1B,EAAyCC,GAAzC,EAAgE;EAC9D,IAAIA,GAAG,KAAK,IAAZ,EAAkB;IAChBD,IAAI,CAAClH,KAAL,CAAWmH,GAAX;EACD;AACF;;AAEM,SAASC,aAAT,CAAsCrH,IAAtC,EAA6D;EAClE,KAAKI,KAAL,CAAWJ,IAAI,CAACsH,OAAhB,EAAyBtH,IAAzB;AACD;;AAEM,SAASuH,6BAAT,CAELvH,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACwH,UAAhB,EAA4BxH,IAA5B;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;AACD;;AAEM,SAASyH,sBAAT,CAELzH,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWiG,EAAX;IAAezD,cAAf;IAA+B0D,OAAO,EAAEC,OAAxC;IAAiDC;EAAjD,IAA0D7H,IAAhE;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,WAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;EACA,KAAKI,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;;EACA,IAAI4H,OAAJ,YAAIA,OAAO,CAAEjH,MAAb,EAAqB;IACnB,KAAKT,KAAL;IACA,KAAKY,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;IACA,KAAKM,SAAL,CAAeoH,OAAf,EAAwB5H,IAAxB;EACD;;EACD,KAAKE,KAAL;EACA,KAAKE,KAAL,CAAWyH,IAAX,EAAiB7H,IAAjB;AACD;;AAEM,SAAS8H,eAAT,CAAwC9H,IAAxC,EAAiE;EACtE,KAAK2E,iCAAL,CAAuC3E,IAAI,CAAC6H,IAA5C,EAAkD7H,IAAlD;AACD;;AAEM,SAAS+H,sBAAT,CAEL/H,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWiG,EAAX;IAAezD,cAAf;IAA+B5D;EAA/B,IAAkDL,IAAxD;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,MAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;EACA,KAAKI,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS+H,cAAT,CAAuChI,IAAvC,EAA+D;EACpE,MAAM;IAAEwH,UAAF;IAAcnH;EAAd,IAAiCL,IAAvC;EACA,KAAKI,KAAL,CAAWoH,UAAX,EAAuBxH,IAAvB;EACA,KAAKE,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;AACD;;AAEM,SAASiI,eAAT,CAAwCjI,IAAxC,EAAiE;EACtE,MAAM;IAAEK,cAAF;IAAkBmH;EAAlB,IAAiCxH,IAAvC;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWoH,UAAX,EAAuBxH,IAAvB;AACD;;AAEM,SAASkI,yBAAT,CAELlI,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACwH,UAAhB,EAA4BxH,IAA5B;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;AACD;;AAEM,SAASmI,iBAAT,CAA0CnI,IAA1C,EAAqE;EAC1E,MAAM;IAAEyB,OAAF;IAAW2G,KAAK,EAAEC,OAAlB;IAA2BX,EAA3B;IAA+B9C;EAA/B,IAA2C5E,IAAjD;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAImI,OAAJ,EAAa;IACX,KAAKvH,IAAL,CAAU,OAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,MAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;EACA,KAAKE,KAAL;EACA2E,aAAa,CAAC,IAAD,EAAOD,OAAP,EAAgB5E,IAAhB,CAAb;AACD;;AAEM,SAASsI,YAAT,CAAqCtI,IAArC,EAA2D;EAChE,MAAM;IAAE0H,EAAF;IAAMtF;EAAN,IAAsBpC,IAA5B;EACA,KAAKI,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;;EACA,IAAIoC,WAAJ,EAAiB;IACf,KAAKlC,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWgC,WAAX,EAAwBpC,IAAxB;EACD;;EACD,KAAKC,SAAL;AACD;;AAEM,SAASsI,mBAAT,CAELvI,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWiG;EAAX,IAAkB1H,IAAxB;;EAEA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EAED,IAAI,CAACF,IAAI,CAACwI,MAAV,EAAkB;IAChB,KAAK1H,IAAL,CAAU4G,EAAE,CAAChH,IAAH,KAAY,YAAZ,GAA2B,WAA3B,GAAyC,QAAnD;IACA,KAAKR,KAAL;EACD;;EACD,KAAKE,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;;EAEA,IAAI,CAACA,IAAI,CAAC6H,IAAV,EAAgB;IACd,KAAK5H,SAAL;IACA;EACD;;EAED,IAAI4H,IAAI,GAAG7H,IAAI,CAAC6H,IAAhB;;EACA,OAAOA,IAAI,CAACnH,IAAL,KAAc,qBAArB,EAA4C;IAC1C,KAAKT,SAAL;IACA,KAAKG,KAAL,CAAWyH,IAAI,CAACH,EAAhB,EAAoBG,IAApB;IACAA,IAAI,GAAGA,IAAI,CAACA,IAAZ;EACD;;EAED,KAAK3H,KAAL;EACA,KAAKE,KAAL,CAAWyH,IAAX,EAAiB7H,IAAjB;AACD;;AAEM,SAASyI,aAAT,CAAsCzI,IAAtC,EAA6D;EAClE6E,aAAa,CAAC,IAAD,EAAO7E,IAAI,CAAC6H,IAAZ,EAAkB7H,IAAlB,CAAb;AACD;;AAEM,SAAS0I,YAAT,CAAqC1I,IAArC,EAA2D;EAChE,MAAM;IAAE2I,QAAF;IAAYC,SAAZ;IAAuB3E;EAAvB,IAA0CjE,IAAhD;EACA,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKb,SAAL;EACA,KAAKG,KAAL,CAAWuI,QAAX,EAAqB3I,IAArB;EACA,KAAKC,SAAL;;EACA,IAAI2I,SAAJ,EAAe;IACb,KAAK3I,SAAL;IACA,KAAKG,KAAL,CAAWwI,SAAX,EAAsB5I,IAAtB;EACD;;EACD,IAAIiE,cAAJ,EAAoB;IAClB,KAAK7D,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACD;AACF;;AAEM,SAAS6I,yBAAT,CAEL7I,IAFK,EAGL;EACA,MAAM;IAAE8I,QAAF;IAAYpB,EAAZ;IAAgBqB;EAAhB,IAAoC/I,IAA1C;;EACA,IAAI8I,QAAJ,EAAc;IACZ,KAAKhI,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWsH,EAAX,EAAe1H,IAAf;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAW2I,eAAX,EAA4B/I,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS+I,yBAAT,CAELhJ,IAFK,EAGL;EACA,KAAKC,KAAL,CAAW,UAAX;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACwH,UAAhB,EAA4BxH,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASgJ,mBAAT,CAELjJ,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACwH,UAAhB,EAA4BxH,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiJ,kBAAT,CAA2ClJ,IAA3C,EAAuE;EAC5E,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACwH,UAAhB,EAA4BxH,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASkJ,4BAAT,CAELnJ,IAFK,EAGL;EACA,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKY,IAAL,CAAU,WAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAAC0H,EAAhB,EAAoB1H,IAApB;AACD;;AAEM,SAASiC,+BAAT,CAAwDjC,IAAxD,EAAmE;EACxE,MAAM;IAAEiE;EAAF,IAAqBjE,IAA3B;EACA,MAAM8C,UAAU,GAEZ9C,IAAI,CAAC8C,UAFT;EAGA,KAAK1C,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKC,SAAL;;EACA,KAAK4C,WAAL,CAAiBC,UAAjB,EAA6B9C,IAA7B;;EACA,KAAKC,SAAL;EACA,MAAMiE,UAAU,GAEZlE,IAAI,CAACK,cAFT;EAGA,KAAKD,KAAL,CAAW8D,UAAX,EAAuBlE,IAAvB;AACD;;AAEM,SAASoJ,2BAAT,CAELpJ,IAFK,EAQL;EACA,MAAMqJ,OAAO,GACXrJ,IAAI,CAACU,IAAL,KAAc,uBAAd,IAAyCV,IAAI,CAACU,IAAL,KAAc,eADzD;;EAEA,IAAI2I,OAAO,IAAIrJ,IAAI,CAACyB,OAApB,EAA6B;IAC3B,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACoB,aAAT,EAAwB;IACtB,KAAKN,IAAL,CAAUd,IAAI,CAACoB,aAAf;IACA,KAAKlB,KAAL;EACD;;EACD,IAAIF,IAAI,CAAC2C,MAAT,EAAiB;IACf,KAAK7B,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACsJ,QAAT,EAAmB;IACjB,KAAKxI,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACgE,QAAT,EAAmB;IACjB,KAAKlD,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAImJ,OAAO,IAAIrJ,IAAI,CAACqB,QAApB,EAA8B;IAC5B,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;AACF"}