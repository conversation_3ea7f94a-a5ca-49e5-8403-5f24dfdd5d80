{"version": 3, "names": ["Plugin", "constructor", "plugin", "options", "key", "externalDependencies", "finalize", "manipulateOptions", "post", "pre", "visitor", "parserOverride", "generatorOverride", "name"], "sources": ["../../src/config/plugin.ts"], "sourcesContent": ["import { finalize } from \"./helpers/deep-array\";\nimport type { ReadonlyDeepArray } from \"./helpers/deep-array\";\nimport type { PluginObject } from \"./validation/plugins\";\n\nexport default class Plugin {\n  key: string | undefined | null;\n  manipulateOptions?: (options: unknown, parserOpts: unknown) => void;\n  post?: PluginObject[\"post\"];\n  pre?: PluginObject[\"pre\"];\n  visitor: PluginObject[\"visitor\"];\n\n  parserOverride?: Function;\n  generatorOverride?: Function;\n\n  options: {};\n\n  externalDependencies: ReadonlyDeepArray<string>;\n\n  constructor(\n    plugin: PluginObject,\n    options: {},\n    key?: string,\n    externalDependencies: ReadonlyDeepArray<string> = finalize([]),\n  ) {\n    this.key = plugin.name || key;\n\n    this.manipulateOptions = plugin.manipulateOptions;\n    this.post = plugin.post;\n    this.pre = plugin.pre;\n    this.visitor = plugin.visitor || {};\n    this.parserOverride = plugin.parserOverride;\n    this.generatorOverride = plugin.generatorOverride;\n\n    this.options = options;\n    this.externalDependencies = externalDependencies;\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAIe,MAAMA,MAAN,CAAa;EAc1BC,WAAW,CACTC,MADS,EAETC,OAFS,EAGTC,GAHS,EAITC,oBAA+C,GAAG,IAAAC,mBAAA,EAAS,EAAT,CAJzC,EAKT;IAAA,KAlBFF,GAkBE;IAAA,KAjBFG,iBAiBE;IAAA,KAhBFC,IAgBE;IAAA,KAfFC,GAeE;IAAA,KAdFC,OAcE;IAAA,KAZFC,cAYE;IAAA,KAXFC,iBAWE;IAAA,KATFT,OASE;IAAA,KAPFE,oBAOE;IACA,KAAKD,GAAL,GAAWF,MAAM,CAACW,IAAP,IAAeT,GAA1B;IAEA,KAAKG,iBAAL,GAAyBL,MAAM,CAACK,iBAAhC;IACA,KAAKC,IAAL,GAAYN,MAAM,CAACM,IAAnB;IACA,KAAKC,GAAL,GAAWP,MAAM,CAACO,GAAlB;IACA,KAAKC,OAAL,GAAeR,MAAM,CAACQ,OAAP,IAAkB,EAAjC;IACA,KAAKC,cAAL,GAAsBT,MAAM,CAACS,cAA7B;IACA,KAAKC,iBAAL,GAAyBV,MAAM,CAACU,iBAAhC;IAEA,KAAKT,OAAL,GAAeA,OAAf;IACA,KAAKE,oBAAL,GAA4BA,oBAA5B;EACD;;AA/ByB"}