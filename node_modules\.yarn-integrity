{"systemParams": "win32-x64-137", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@icons-pack/react-simple-icons@^13.5.0", "@monaco-editor/react@^4.6.0", "@radix-ui/react-collapsible@^1.1.11", "@radix-ui/react-scroll-area@^1.2.9", "@radix-ui/react-select@^2.2.5", "@radix-ui/react-separator@^1.1.7", "@radix-ui/react-slot@^1.2.3", "@radix-ui/react-tabs@^1.1.12", "@radix-ui/react-tooltip@^1.2.7", "@radix-ui/react-use-controllable-state@^1.2.2", "@shikijs/transformers@^3.8.0", "@tailwindcss/postcss@^4.1.11", "@tauri-apps/api@^1.1.0", "@tauri-apps/cli@^1.1.0", "@types/node@^18.7.10", "@types/react-dom@^18.0.6", "@types/react@^18.0.15", "@vitejs/plugin-react@^2.0.0", "autoprefixer@^10.4.21", "class-variance-authority@^0.7.1", "clsx@^2.1.1", "framer-motion@^12.23.6", "fuse.js@^7.1.0", "lucide-react@^0.525.0", "monaco-editor@^0.44.0", "nanoid@^4.0.0", "postcss@^8.4.17", "react-dnd-html5-backend@^16.0.1", "react-dnd@^16.0.1", "react-dom@^18.2.0", "react-markdown@^10.1.0", "react-resizable-panels@^3.0.3", "react@^18.2.0", "remark-gfm@^4.0.1", "remixicon@^2.5.0", "shiki@^3.8.0", "tailwind-merge@^3.3.1", "tailwindcss@^4.1.11", "tw-animate-css@^1.3.5", "typescript@^4.6.4", "use-stick-to-bottom@^1.1.1", "vite@^3.0.2", "zustand@^4.4.6"], "lockfileEntries": {"@alloc/quick-lru@^5.2.0": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "@ampproject/remapping@^2.1.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "@ampproject/remapping@^2.3.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@^7.18.6": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/compat-data@^7.19.3": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.3.tgz", "@babel/core@^7.18.13": "https://registry.npmjs.org/@babel/core/-/core-7.19.3.tgz", "@babel/generator@^7.19.3": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.3.tgz", "@babel/helper-annotate-as-pure@^7.18.6": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "@babel/helper-compilation-targets@^7.19.3": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.3.tgz", "@babel/helper-environment-visitor@^7.18.9": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "@babel/helper-function-name@^7.19.0": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "@babel/helper-hoist-variables@^7.18.6": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "@babel/helper-module-imports@^7.18.6": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-transforms@^7.19.0": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.19.0.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz", "@babel/helper-plugin-utils@^7.19.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz", "@babel/helper-simple-access@^7.18.6": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.18.6.tgz", "@babel/helper-split-export-declaration@^7.18.6": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "@babel/helper-string-parser@^7.18.10": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.18.10.tgz", "@babel/helper-validator-identifier@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-identifier@^7.19.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-option@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "@babel/helpers@^7.19.0": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.19.0.tgz", "@babel/highlight@^7.18.6": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/parser@^7.18.10": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz", "@babel/parser@^7.19.3": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz", "@babel/plugin-syntax-jsx@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz", "@babel/plugin-transform-react-jsx-development@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.18.6.tgz", "@babel/plugin-transform-react-jsx-self@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.18.6.tgz", "@babel/plugin-transform-react-jsx-source@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.18.6.tgz", "@babel/plugin-transform-react-jsx@^7.18.10": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz", "@babel/plugin-transform-react-jsx@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz", "@babel/runtime@^7.9.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.18.10": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "@babel/traverse@^7.19.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.19.3.tgz", "@babel/traverse@^7.19.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.19.3.tgz", "@babel/types@^7.18.10": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.18.6": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.19.0": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.19.3": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@emnapi/core@^1.4.3": "https://registry.yarnpkg.com/@emnapi/core/-/core-1.4.4.tgz#76620673f3033626c6d79b1420d69f06a6bb153c", "@emnapi/runtime@^1.4.3": "https://registry.yarnpkg.com/@emnapi/runtime/-/runtime-1.4.4.tgz#19a8f00719c51124e2d0fbf4aaad3fa7b0c92524", "@emnapi/wasi-threads@1.0.3": "https://registry.yarnpkg.com/@emnapi/wasi-threads/-/wasi-threads-1.0.3.tgz#83fa228bde0e71668aad6db1af4937473d1d3ab1", "@emnapi/wasi-threads@^1.0.2": "https://registry.yarnpkg.com/@emnapi/wasi-threads/-/wasi-threads-1.0.3.tgz#83fa228bde0e71668aad6db1af4937473d1d3ab1", "@esbuild/android-arm@0.15.10": "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.15.10.tgz#a5f9432eb221afc243c321058ef25fe899886892", "@esbuild/linux-loong64@0.15.10": "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.15.10.tgz#78a42897c2cf8db9fd5f1811f7590393b77774c7", "@floating-ui/core@^1.7.2": "https://registry.yarnpkg.com/@floating-ui/core/-/core-1.7.2.tgz#3d1c35263950b314b6d5a72c8bfb9e3c1551aefd", "@floating-ui/dom@^1.7.2": "https://registry.yarnpkg.com/@floating-ui/dom/-/dom-1.7.2.tgz#3540b051cf5ce0d4f4db5fb2507a76e8ea5b4a45", "@floating-ui/react-dom@^2.0.0": "https://registry.yarnpkg.com/@floating-ui/react-dom/-/react-dom-2.1.4.tgz#a0689be8978352fff2be2dfdd718cf668c488ec3", "@floating-ui/utils@^0.2.10": "https://registry.yarnpkg.com/@floating-ui/utils/-/utils-0.2.10.tgz#a2a1e3812d14525f725d011a73eceb41fef5bc1c", "@icons-pack/react-simple-icons@^13.5.0": "https://registry.yarnpkg.com/@icons-pack/react-simple-icons/-/react-simple-icons-13.5.0.tgz#a9ee5c774e500a011e08b525248f1fc074c186be", "@isaacs/fs-minipass@^4.0.0": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "@jridgewell/gen-mapping@^0.1.0": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/resolve-uri@3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.0.0": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/set-array@^1.0.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/sourcemap-codec@1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.16.tgz", "@monaco-editor/loader@^1.5.0": "https://registry.npmjs.org/@monaco-editor/loader/-/loader-1.5.0.tgz", "@monaco-editor/react@^4.6.0": "https://registry.npmjs.org/@monaco-editor/react/-/react-4.7.0.tgz", "@napi-rs/wasm-runtime@^0.2.11": "https://registry.yarnpkg.com/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz#3e78a8b96e6c33a6c517e1894efbd5385a7cb6f2", "@radix-ui/number@1.1.1": "https://registry.yarnpkg.com/@radix-ui/number/-/number-1.1.1.tgz#7b2c9225fbf1b126539551f5985769d0048d9090", "@radix-ui/primitive@1.1.2": "https://registry.yarnpkg.com/@radix-ui/primitive/-/primitive-1.1.2.tgz#83f415c4425f21e3d27914c12b3272a32e3dae65", "@radix-ui/react-arrow@1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz#e14a2657c81d961598c5e72b73dd6098acc04f09", "@radix-ui/react-collapsible@^1.1.11": "https://registry.yarnpkg.com/@radix-ui/react-collapsible/-/react-collapsible-1.1.11.tgz#a2d132d5baa6f14551f15b1fff29f925cae46b83", "@radix-ui/react-collection@1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-collection/-/react-collection-1.1.7.tgz#d05c25ca9ac4695cc19ba91f42f686e3ea2d9aec", "@radix-ui/react-compose-refs@1.1.2": "https://registry.yarnpkg.com/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz#a2c4c47af6337048ee78ff6dc0d090b390d2bb30", "@radix-ui/react-context@1.1.2": "https://registry.yarnpkg.com/@radix-ui/react-context/-/react-context-1.1.2.tgz#61628ef269a433382c364f6f1e3788a6dc213a36", "@radix-ui/react-direction@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-direction/-/react-direction-1.1.1.tgz#39e5a5769e676c753204b792fbe6cf508e550a14", "@radix-ui/react-dismissable-layer@1.1.10": "https://registry.yarnpkg.com/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz#429b9bada3672c6895a5d6a642aca6ecaf4f18c3", "@radix-ui/react-focus-guards@1.1.2": "https://registry.yarnpkg.com/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz#4ec9a7e50925f7fb661394460045b46212a33bed", "@radix-ui/react-focus-scope@1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz#dfe76fc103537d80bf42723a183773fd07bfb58d", "@radix-ui/react-id@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-id/-/react-id-1.1.1.tgz#1404002e79a03fe062b7e3864aa01e24bd1471f7", "@radix-ui/react-popper@1.2.7": "https://registry.yarnpkg.com/@radix-ui/react-popper/-/react-popper-1.2.7.tgz#531cf2eebb3d3270d58f7d8136e4517646429978", "@radix-ui/react-portal@1.1.9": "https://registry.yarnpkg.com/@radix-ui/react-portal/-/react-portal-1.1.9.tgz#14c3649fe48ec474ac51ed9f2b9f5da4d91c4472", "@radix-ui/react-presence@1.1.4": "https://registry.yarnpkg.com/@radix-ui/react-presence/-/react-presence-1.1.4.tgz#253ac0ad4946c5b4a9c66878335f5cf07c967ced", "@radix-ui/react-primitive@2.1.3": "https://registry.yarnpkg.com/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz#db9b8bcff49e01be510ad79893fb0e4cda50f1bc", "@radix-ui/react-roving-focus@1.1.10": "https://registry.yarnpkg.com/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz#46030496d2a490c4979d29a7e1252465e51e4b0b", "@radix-ui/react-scroll-area@^1.2.9": "https://registry.yarnpkg.com/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.9.tgz#90c49bd3231d7f0796d5d12dabc065afa829cf07", "@radix-ui/react-select@^2.2.5": "https://registry.yarnpkg.com/@radix-ui/react-select/-/react-select-2.2.5.tgz#9e2fa5b8f4cc99b86ef5bba3cb9b73828afb51f0", "@radix-ui/react-separator@^1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-separator/-/react-separator-1.1.7.tgz#a18bd7fd07c10fda1bba14f2a3032e7b1a2b3470", "@radix-ui/react-slot@1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-slot/-/react-slot-1.2.3.tgz#502d6e354fc847d4169c3bc5f189de777f68cfe1", "@radix-ui/react-slot@^1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-slot/-/react-slot-1.2.3.tgz#502d6e354fc847d4169c3bc5f189de777f68cfe1", "@radix-ui/react-tabs@^1.1.12": "https://registry.yarnpkg.com/@radix-ui/react-tabs/-/react-tabs-1.1.12.tgz#99b3522c73db9263f429a6d0f5a9acb88df3b129", "@radix-ui/react-tooltip@^1.2.7": "https://registry.yarnpkg.com/@radix-ui/react-tooltip/-/react-tooltip-1.2.7.tgz#23612ac7a5e8e1f6829e46d0e0ad94afe3976c72", "@radix-ui/react-use-callback-ref@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz#62a4dba8b3255fdc5cc7787faeac1c6e4cc58d40", "@radix-ui/react-use-controllable-state@1.2.2": "https://registry.yarnpkg.com/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz#905793405de57d61a439f4afebbb17d0645f3190", "@radix-ui/react-use-controllable-state@^1.2.2": "https://registry.yarnpkg.com/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz#905793405de57d61a439f4afebbb17d0645f3190", "@radix-ui/react-use-effect-event@0.0.2": "https://registry.yarnpkg.com/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz#090cf30d00a4c7632a15548512e9152217593907", "@radix-ui/react-use-escape-keydown@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz#b3fed9bbea366a118f40427ac40500aa1423cc29", "@radix-ui/react-use-layout-effect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz#0c4230a9eed49d4589c967e2d9c0d9d60a23971e", "@radix-ui/react-use-previous@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz#1a1ad5568973d24051ed0af687766f6c7cb9b5b5", "@radix-ui/react-use-rect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz#01443ca8ed071d33023c1113e5173b5ed8769152", "@radix-ui/react-use-size@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz#6de276ffbc389a537ffe4316f5b0f24129405b37", "@radix-ui/react-visually-hidden@1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.3.tgz#a8c38c8607735dc9f05c32f87ab0f9c2b109efbf", "@radix-ui/rect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/rect/-/rect-1.1.1.tgz#78244efe12930c56fd255d7923865857c41ac8cb", "@react-dnd/asap@^5.0.1": "https://registry.npmjs.org/@react-dnd/asap/-/asap-5.0.2.tgz", "@react-dnd/invariant@^4.0.1": "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-4.0.2.tgz", "@react-dnd/shallowequal@^4.0.1": "https://registry.npmjs.org/@react-dnd/shallowequal/-/shallowequal-4.0.2.tgz", "@shikijs/core@3.8.0": "https://registry.yarnpkg.com/@shikijs/core/-/core-3.8.0.tgz#217a38c9efcbe2d294de6c9042b42663f1938f13", "@shikijs/engine-javascript@3.8.0": "https://registry.yarnpkg.com/@shikijs/engine-javascript/-/engine-javascript-3.8.0.tgz#1b5bde2908a17b6d0416090147eb809be7aa057a", "@shikijs/engine-oniguruma@3.8.0": "https://registry.yarnpkg.com/@shikijs/engine-oniguruma/-/engine-oniguruma-3.8.0.tgz#a3da8320e649ce8e8dc33aa82eafba56ae940c4f", "@shikijs/langs@3.8.0": "https://registry.yarnpkg.com/@shikijs/langs/-/langs-3.8.0.tgz#efaa1121997eb1b2d1669c30662a38c7eb230abf", "@shikijs/themes@3.8.0": "https://registry.yarnpkg.com/@shikijs/themes/-/themes-3.8.0.tgz#7026b6dff470ca26c94c59a2d0efd2b51a5c2d05", "@shikijs/transformers@^3.8.0": "https://registry.yarnpkg.com/@shikijs/transformers/-/transformers-3.8.0.tgz#641f5d534867dc43ca01183740de0a99a35d1422", "@shikijs/types@3.8.0": "https://registry.yarnpkg.com/@shikijs/types/-/types-3.8.0.tgz#1cc483f7075fde00483f054cce4e65a050e1865f", "@shikijs/vscode-textmate@^10.0.2": "https://registry.yarnpkg.com/@shikijs/vscode-textmate/-/vscode-textmate-10.0.2.tgz#a90ab31d0cc1dfb54c66a69e515bf624fa7b2224", "@tailwindcss/node@4.1.11": "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.11.tgz", "@tailwindcss/oxide-android-arm64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.1.11.tgz#1f387d8302f011b61c226deb0c3a1d2bd79c6915", "@tailwindcss/oxide-darwin-arm64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.1.11.tgz#acd35ffb7e4eae83d0a3fe2f8ea36cfcc9b21f7e", "@tailwindcss/oxide-darwin-x64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.1.11.tgz#a0022312993a3893d6ff0312d6e3c83c4636fef4", "@tailwindcss/oxide-freebsd-x64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.1.11.tgz#dd8e55eb0b88fe7995b8148c0e0ae5fa27092d22", "@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.1.11.tgz#02ee99090988847d3f13d277679012cbffcdde37", "@tailwindcss/oxide-linux-arm64-gnu@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.1.11.tgz#4837559c102bebe65089879f6a0278ed473b4813", "@tailwindcss/oxide-linux-arm64-musl@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.1.11.tgz#bec465112a13a1383558ff36afdf28b8a8cb9021", "@tailwindcss/oxide-linux-x64-gnu@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.11.tgz#f9e47e6aa67ff77f32f7412bc9698d4278e101bf", "@tailwindcss/oxide-linux-x64-musl@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.11.tgz#7d6e8adcfb9bc84d8e2e2e8781d661edb9e41ba8", "@tailwindcss/oxide-wasm32-wasi@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.11.tgz#a1762f4939c6ebaa824696fda2fd7db1b85fbed2", "@tailwindcss/oxide-win32-arm64-msvc@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.11.tgz#70ba392dca0fa3707ebe27d2bd6ac3e69d35e3b7", "@tailwindcss/oxide-win32-x64-msvc@4.1.11": "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.11.tgz", "@tailwindcss/oxide@4.1.11": "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.11.tgz", "@tailwindcss/postcss@^4.1.11": "https://registry.npmjs.org/@tailwindcss/postcss/-/postcss-4.1.11.tgz", "@tauri-apps/api@^1.1.0": "https://registry.npmjs.org/@tauri-apps/api/-/api-1.1.0.tgz", "@tauri-apps/cli-darwin-arm64@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-1.1.1.tgz#c6f4553cfb338f24131910a1ebbe9fe74d89b8ae", "@tauri-apps/cli-darwin-x64@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-1.1.1.tgz#11beb8b3dfc43725f0acd3736cd9d181f93526a2", "@tauri-apps/cli-linux-arm-gnueabihf@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-1.1.1.tgz#f4bd3f5839cfcd69132d213efee09ce5da5c6d90", "@tauri-apps/cli-linux-arm64-gnu@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-1.1.1.tgz#2631b2a68e7901ea7281af022d8826c0d82b9edd", "@tauri-apps/cli-linux-arm64-musl@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-1.1.1.tgz#11a4bdc52696583152fc70ea373cf31717cb0f4c", "@tauri-apps/cli-linux-x64-gnu@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-1.1.1.tgz#11acbcc4743562cc8166c92ffc3bc12f68e0987f", "@tauri-apps/cli-linux-x64-musl@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-1.1.1.tgz#7dd81b8380d7e7183fb43a2ccbbc26931718204a", "@tauri-apps/cli-win32-ia32-msvc@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-1.1.1.tgz#1d106ae1c3766f95d7f49bac51eba0d2cd2daf33", "@tauri-apps/cli-win32-x64-msvc@1.1.1": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-1.1.1.tgz", "@tauri-apps/cli@^1.1.0": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-1.1.1.tgz", "@tybys/wasm-util@^0.10.0": "https://registry.yarnpkg.com/@tybys/wasm-util/-/wasm-util-0.10.0.tgz#2fd3cd754b94b378734ce17058d0507c45c88369", "@tybys/wasm-util@^0.9.0": "https://registry.yarnpkg.com/@tybys/wasm-util/-/wasm-util-0.9.0.tgz#3e75eb00604c8d6db470bf18c37b7d984a0e3355", "@types/debug@^4.0.0": "https://registry.yarnpkg.com/@types/debug/-/debug-4.1.12.tgz#a155f21690871953410df4b6b6f53187f0500917", "@types/estree-jsx@^1.0.0": "https://registry.yarnpkg.com/@types/estree-jsx/-/estree-jsx-1.0.5.tgz#858a88ea20f34fe65111f005a689fa1ebf70dc18", "@types/estree@*": "https://registry.yarnpkg.com/@types/estree/-/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e", "@types/estree@^1.0.0": "https://registry.yarnpkg.com/@types/estree/-/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e", "@types/hast@^3.0.0": "https://registry.yarnpkg.com/@types/hast/-/hast-3.0.4.tgz#1d6b39993b82cea6ad783945b0508c25903e15aa", "@types/hast@^3.0.4": "https://registry.yarnpkg.com/@types/hast/-/hast-3.0.4.tgz#1d6b39993b82cea6ad783945b0508c25903e15aa", "@types/mdast@^4.0.0": "https://registry.yarnpkg.com/@types/mdast/-/mdast-4.0.4.tgz#7ccf72edd2f1aa7dd3437e180c64373585804dd6", "@types/ms@*": "https://registry.yarnpkg.com/@types/ms/-/ms-2.1.0.tgz#052aa67a48eccc4309d7f0191b7e41434b90bb78", "@types/node@^18.7.10": "https://registry.npmjs.org/@types/node/-/node-18.8.3.tgz", "@types/prop-types@*": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "@types/react-dom@^18.0.6": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.0.6.tgz", "@types/react@*": "https://registry.npmjs.org/@types/react/-/react-18.0.21.tgz", "@types/react@^18.0.15": "https://registry.npmjs.org/@types/react/-/react-18.0.21.tgz", "@types/scheduler@*": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "@types/unist@*": "https://registry.yarnpkg.com/@types/unist/-/unist-3.0.3.tgz#acaab0f919ce69cce629c2d4ed2eb4adc1b6c20c", "@types/unist@^2.0.0": "https://registry.yarnpkg.com/@types/unist/-/unist-2.0.11.tgz#11af57b127e32487774841f7a4e54eab166d03c4", "@types/unist@^3.0.0": "https://registry.yarnpkg.com/@types/unist/-/unist-3.0.3.tgz#acaab0f919ce69cce629c2d4ed2eb4adc1b6c20c", "@ungap/structured-clone@^1.0.0": "https://registry.yarnpkg.com/@ungap/structured-clone/-/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8", "@vitejs/plugin-react@^2.0.0": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.1.0.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "aria-hidden@^1.2.4": "https://registry.yarnpkg.com/aria-hidden/-/aria-hidden-1.2.6.tgz#73051c9b088114c795b1ea414e9c0fff874ffc1a", "autoprefixer@^10.4.21": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "bail@^2.0.0": "https://registry.yarnpkg.com/bail/-/bail-2.0.2.tgz#d26f5cd8fe5d6f832a31517b9f7c356040ba6d5d", "browserslist@^4.21.3": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "caniuse-lite@^1.0.30001400": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001418.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "caniuse-lite@^1.0.30001726": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "ccount@^2.0.0": "https://registry.yarnpkg.com/ccount/-/ccount-2.0.1.tgz#17a3bf82302e0870d6da43a01311a8bc02a3ecf5", "chalk@^2.0.0": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "character-entities-html4@^2.0.0": "https://registry.yarnpkg.com/character-entities-html4/-/character-entities-html4-2.1.0.tgz#1f1adb940c971a4b22ba39ddca6b618dc6e56b2b", "character-entities-legacy@^3.0.0": "https://registry.yarnpkg.com/character-entities-legacy/-/character-entities-legacy-3.0.0.tgz#76bc83a90738901d7bc223a9e93759fdd560125b", "character-entities@^2.0.0": "https://registry.yarnpkg.com/character-entities/-/character-entities-2.0.2.tgz#2d09c2e72cd9523076ccb21157dff66ad43fcc22", "character-reference-invalid@^2.0.0": "https://registry.yarnpkg.com/character-reference-invalid/-/character-reference-invalid-2.0.1.tgz#85c66b041e43b47210faf401278abf808ac45cb9", "chownr@^3.0.0": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "class-variance-authority@^0.7.1": "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz", "clsx@^2.1.1": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "comma-separated-tokens@^2.0.0": "https://registry.yarnpkg.com/comma-separated-tokens/-/comma-separated-tokens-2.0.3.tgz#4e89c9458acb61bc8fef19f4529973b2392839ee", "convert-source-map@^1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz", "csstype@^3.0.2": "https://registry.npmjs.org/csstype/-/csstype-3.1.1.tgz", "debug@^4.0.0": "https://registry.yarnpkg.com/debug/-/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "decode-named-character-reference@^1.0.0": "https://registry.yarnpkg.com/decode-named-character-reference/-/decode-named-character-reference-1.2.0.tgz#25c32ae6dd5e21889549d40f676030e9514cc0ed", "dequal@^2.0.0": "https://registry.yarnpkg.com/dequal/-/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be", "detect-libc@^2.0.3": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "detect-libc@^2.0.4": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "detect-node-es@^1.1.0": "https://registry.yarnpkg.com/detect-node-es/-/detect-node-es-1.1.0.tgz#163acdf643330caa0b4cd7c21e7ee7755d6fa493", "devlop@^1.0.0": "https://registry.yarnpkg.com/devlop/-/devlop-1.1.0.tgz#4db7c2ca4dc6e0e834c30be70c94bbc976dc7018", "devlop@^1.1.0": "https://registry.yarnpkg.com/devlop/-/devlop-1.1.0.tgz#4db7c2ca4dc6e0e834c30be70c94bbc976dc7018", "dnd-core@^16.0.1": "https://registry.npmjs.org/dnd-core/-/dnd-core-16.0.1.tgz", "electron-to-chromium@^1.4.251": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.276.tgz", "electron-to-chromium@^1.5.173": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.184.tgz", "enhanced-resolve@^5.18.1": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "esbuild-android-64@0.15.10": "https://registry.yarnpkg.com/esbuild-android-64/-/esbuild-android-64-0.15.10.tgz#8a59a84acbf2eca96996cadc35642cf055c494f0", "esbuild-android-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.10.tgz#f453851dc1d8c5409a38cf7613a33852faf4915d", "esbuild-darwin-64@0.15.10": "https://registry.yarnpkg.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.10.tgz#778bd29c8186ff47b176c8af58c08cf0fb8e6b86", "esbuild-darwin-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.10.tgz#b30bbefb46dc3c5d4708b0435e52f6456578d6df", "esbuild-freebsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.10.tgz#ab301c5f6ded5110dbdd611140bef1a7c2e99236", "esbuild-freebsd-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.10.tgz#a5b09b867a6ff49110f52343b6f12265db63d43f", "esbuild-linux-32@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-32/-/esbuild-linux-32-0.15.10.tgz#5282fe9915641caf9c8070e4ba2c3e16d358f837", "esbuild-linux-64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-64/-/esbuild-linux-64-0.15.10.tgz#f3726e85a00149580cb19f8abfabcbb96f5d52bb", "esbuild-linux-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.10.tgz#2f0056e9d5286edb0185b56655caa8c574d8dbe7", "esbuild-linux-arm@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.10.tgz#40a9270da3c8ffa32cf72e24a79883e323dff08d", "esbuild-linux-mips64le@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.10.tgz#90ce1c4ee0202edb4ac69807dea77f7e5804abc4", "esbuild-linux-ppc64le@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.10.tgz#782837ae7bd5b279178106c9dd801755a21fabdf", "esbuild-linux-riscv64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.10.tgz#d7420d806ece5174f24f4634303146f915ab4207", "esbuild-linux-s390x@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.10.tgz#21fdf0cb3494a7fb520a71934e4dffce67fe47be", "esbuild-netbsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.10.tgz#6c06b3107e3df53de381e6299184d4597db0440f", "esbuild-openbsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.10.tgz#4daef5f5d8e74bbda53b65160029445d582570cf", "esbuild-sunos-64@0.15.10": "https://registry.yarnpkg.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.10.tgz#5fe7bef267a02f322fd249a8214d0274937388a7", "esbuild-windows-32@0.15.10": "https://registry.yarnpkg.com/esbuild-windows-32/-/esbuild-windows-32-0.15.10.tgz#48e3dde25ab0135579a288b30ab6ddef6d1f0b28", "esbuild-windows-64@0.15.10": "https://registry.npmjs.org/esbuild-windows-64/-/esbuild-windows-64-0.15.10.tgz", "esbuild-windows-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.10.tgz#5a6fcf2fa49e895949bf5495cf088ab1b43ae879", "esbuild@^0.15.9": "https://registry.npmjs.org/esbuild/-/esbuild-0.15.10.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^5.0.0": "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz#4683126b500b61762f2dbebace1806e8be31b1c8", "estree-util-is-identifier-name@^3.0.0": "https://registry.yarnpkg.com/estree-util-is-identifier-name/-/estree-util-is-identifier-name-3.0.0.tgz#0b5ef4c4ff13508b34dcd01ecfa945f61fce5dbd", "extend@^3.0.0": "https://registry.yarnpkg.com/extend/-/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "framer-motion@^12.23.6": "https://registry.npmjs.org/framer-motion/-/framer-motion-12.23.6.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.1": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "fuse.js@^7.1.0": "https://registry.npmjs.org/fuse.js/-/fuse.js-7.1.0.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-nonce@^1.0.0": "https://registry.yarnpkg.com/get-nonce/-/get-nonce-1.0.1.tgz#fdf3f0278073820d2ce9426c18f07481b1e0cdf3", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has@^1.0.3": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "hast-util-to-html@^9.0.5": "https://registry.yarnpkg.com/hast-util-to-html/-/hast-util-to-html-9.0.5.tgz#ccc673a55bb8e85775b08ac28380f72d47167005", "hast-util-to-jsx-runtime@^2.0.0": "https://registry.yarnpkg.com/hast-util-to-jsx-runtime/-/hast-util-to-jsx-runtime-2.3.6.tgz#ff31897aae59f62232e21594eac7ef6b63333e98", "hast-util-whitespace@^3.0.0": "https://registry.yarnpkg.com/hast-util-whitespace/-/hast-util-whitespace-3.0.0.tgz#7778ed9d3c92dd9e8c5c8f648a49c21fc51cb621", "hoist-non-react-statics@^3.3.2": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "html-url-attributes@^3.0.0": "https://registry.yarnpkg.com/html-url-attributes/-/html-url-attributes-3.0.1.tgz#83b052cd5e437071b756cd74ae70f708870c2d87", "html-void-elements@^3.0.0": "https://registry.yarnpkg.com/html-void-elements/-/html-void-elements-3.0.0.tgz#fc9dbd84af9e747249034d4d62602def6517f1d7", "inline-style-parser@0.2.4": "https://registry.yarnpkg.com/inline-style-parser/-/inline-style-parser-0.2.4.tgz#f4af5fe72e612839fcd453d989a586566d695f22", "is-alphabetical@^2.0.0": "https://registry.yarnpkg.com/is-alphabetical/-/is-alphabetical-2.0.1.tgz#01072053ea7c1036df3c7d19a6daaec7f19e789b", "is-alphanumerical@^2.0.0": "https://registry.yarnpkg.com/is-alphanumerical/-/is-alphanumerical-2.0.1.tgz#7c03fbe96e3e931113e57f964b0a368cc2dfd875", "is-core-module@^2.9.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.10.0.tgz", "is-decimal@^2.0.0": "https://registry.yarnpkg.com/is-decimal/-/is-decimal-2.0.1.tgz#9469d2dc190d0214fd87d78b78caecc0cc14eef7", "is-hexadecimal@^2.0.0": "https://registry.yarnpkg.com/is-hexadecimal/-/is-hexadecimal-2.0.1.tgz#86b5bf668fca307498d319dfc03289d781a90027", "is-plain-obj@^4.0.0": "https://registry.yarnpkg.com/is-plain-obj/-/is-plain-obj-4.1.0.tgz#d65025edec3657ce032fd7db63c97883eaed71f0", "jiti@^2.4.2": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "jsesc@^2.5.1": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "json5@^2.2.1": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "lightningcss-darwin-arm64@1.30.1": "https://registry.yarnpkg.com/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz#3d47ce5e221b9567c703950edf2529ca4a3700ae", "lightningcss-darwin-x64@1.30.1": "https://registry.yarnpkg.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz#e81105d3fd6330860c15fe860f64d39cff5fbd22", "lightningcss-freebsd-x64@1.30.1": "https://registry.yarnpkg.com/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz#a0e732031083ff9d625c5db021d09eb085af8be4", "lightningcss-linux-arm-gnueabihf@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz#1f5ecca6095528ddb649f9304ba2560c72474908", "lightningcss-linux-arm64-gnu@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz#eee7799726103bffff1e88993df726f6911ec009", "lightningcss-linux-arm64-musl@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz#f2e4b53f42892feeef8f620cbb889f7c064a7dfe", "lightningcss-linux-x64-gnu@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz#2fc7096224bc000ebb97eea94aea248c5b0eb157", "lightningcss-linux-x64-musl@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz#66dca2b159fd819ea832c44895d07e5b31d75f26", "lightningcss-win32-arm64-msvc@1.30.1": "https://registry.yarnpkg.com/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz#7d8110a19d7c2d22bfdf2f2bb8be68e7d1b69039", "lightningcss-win32-x64-msvc@1.30.1": "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", "lightningcss@1.30.1": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz", "longest-streak@^3.0.0": "https://registry.yarnpkg.com/longest-streak/-/longest-streak-3.1.0.tgz#62fa67cd958742a1574af9f39866364102d90cd4", "loose-envify@^1.1.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lucide-react@^0.525.0": "https://registry.yarnpkg.com/lucide-react/-/lucide-react-0.525.0.tgz#5f7bcecd65e4f9b2b5b6b5d295e3376df032d5e3", "magic-string@^0.26.2": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.6.tgz", "magic-string@^0.30.17": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "markdown-table@^3.0.0": "https://registry.yarnpkg.com/markdown-table/-/markdown-table-3.0.4.tgz#fe44d6d410ff9d6f2ea1797a3f60aa4d2b631c2a", "mdast-util-find-and-replace@^3.0.0": "https://registry.yarnpkg.com/mdast-util-find-and-replace/-/mdast-util-find-and-replace-3.0.2.tgz#70a3174c894e14df722abf43bc250cbae44b11df", "mdast-util-from-markdown@^2.0.0": "https://registry.yarnpkg.com/mdast-util-from-markdown/-/mdast-util-from-markdown-2.0.2.tgz#4850390ca7cf17413a9b9a0fbefcd1bc0eb4160a", "mdast-util-gfm-autolink-literal@^2.0.0": "https://registry.yarnpkg.com/mdast-util-gfm-autolink-literal/-/mdast-util-gfm-autolink-literal-2.0.1.tgz#abd557630337bd30a6d5a4bd8252e1c2dc0875d5", "mdast-util-gfm-footnote@^2.0.0": "https://registry.yarnpkg.com/mdast-util-gfm-footnote/-/mdast-util-gfm-footnote-2.1.0.tgz#7778e9d9ca3df7238cc2bd3fa2b1bf6a65b19403", "mdast-util-gfm-strikethrough@^2.0.0": "https://registry.yarnpkg.com/mdast-util-gfm-strikethrough/-/mdast-util-gfm-strikethrough-2.0.0.tgz#d44ef9e8ed283ac8c1165ab0d0dfd058c2764c16", "mdast-util-gfm-table@^2.0.0": "https://registry.yarnpkg.com/mdast-util-gfm-table/-/mdast-util-gfm-table-2.0.0.tgz#7a435fb6223a72b0862b33afbd712b6dae878d38", "mdast-util-gfm-task-list-item@^2.0.0": "https://registry.yarnpkg.com/mdast-util-gfm-task-list-item/-/mdast-util-gfm-task-list-item-2.0.0.tgz#e68095d2f8a4303ef24094ab642e1047b991a936", "mdast-util-gfm@^3.0.0": "https://registry.yarnpkg.com/mdast-util-gfm/-/mdast-util-gfm-3.1.0.tgz#2cdf63b92c2a331406b0fb0db4c077c1b0331751", "mdast-util-mdx-expression@^2.0.0": "https://registry.yarnpkg.com/mdast-util-mdx-expression/-/mdast-util-mdx-expression-2.0.1.tgz#43f0abac9adc756e2086f63822a38c8d3c3a5096", "mdast-util-mdx-jsx@^3.0.0": "https://registry.yarnpkg.com/mdast-util-mdx-jsx/-/mdast-util-mdx-jsx-3.2.0.tgz#fd04c67a2a7499efb905a8a5c578dddc9fdada0d", "mdast-util-mdxjs-esm@^2.0.0": "https://registry.yarnpkg.com/mdast-util-mdxjs-esm/-/mdast-util-mdxjs-esm-2.0.1.tgz#019cfbe757ad62dd557db35a695e7314bcc9fa97", "mdast-util-phrasing@^4.0.0": "https://registry.yarnpkg.com/mdast-util-phrasing/-/mdast-util-phrasing-4.1.0.tgz#7cc0a8dec30eaf04b7b1a9661a92adb3382aa6e3", "mdast-util-to-hast@^13.0.0": "https://registry.yarnpkg.com/mdast-util-to-hast/-/mdast-util-to-hast-13.2.0.tgz#5ca58e5b921cc0a3ded1bc02eed79a4fe4fe41f4", "mdast-util-to-markdown@^2.0.0": "https://registry.yarnpkg.com/mdast-util-to-markdown/-/mdast-util-to-markdown-2.1.2.tgz#f910ffe60897f04bb4b7e7ee434486f76288361b", "mdast-util-to-string@^4.0.0": "https://registry.yarnpkg.com/mdast-util-to-string/-/mdast-util-to-string-4.0.0.tgz#7a5121475556a04e7eddeb67b264aae79d312814", "micromark-core-commonmark@^2.0.0": "https://registry.yarnpkg.com/micromark-core-commonmark/-/micromark-core-commonmark-2.0.3.tgz#c691630e485021a68cf28dbc2b2ca27ebf678cd4", "micromark-extension-gfm-autolink-literal@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-autolink-literal/-/micromark-extension-gfm-autolink-literal-2.1.0.tgz#6286aee9686c4462c1e3552a9d505feddceeb935", "micromark-extension-gfm-footnote@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-footnote/-/micromark-extension-gfm-footnote-2.1.0.tgz#4dab56d4e398b9853f6fe4efac4fc9361f3e0750", "micromark-extension-gfm-strikethrough@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-strikethrough/-/micromark-extension-gfm-strikethrough-2.1.0.tgz#86106df8b3a692b5f6a92280d3879be6be46d923", "micromark-extension-gfm-table@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-table/-/micromark-extension-gfm-table-2.1.1.tgz#fac70bcbf51fe65f5f44033118d39be8a9b5940b", "micromark-extension-gfm-tagfilter@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-tagfilter/-/micromark-extension-gfm-tagfilter-2.0.0.tgz#f26d8a7807b5985fba13cf61465b58ca5ff7dc57", "micromark-extension-gfm-task-list-item@^2.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm-task-list-item/-/micromark-extension-gfm-task-list-item-2.1.0.tgz#bcc34d805639829990ec175c3eea12bb5b781f2c", "micromark-extension-gfm@^3.0.0": "https://registry.yarnpkg.com/micromark-extension-gfm/-/micromark-extension-gfm-3.0.0.tgz#3e13376ab95dd7a5cfd0e29560dfe999657b3c5b", "micromark-factory-destination@^2.0.0": "https://registry.yarnpkg.com/micromark-factory-destination/-/micromark-factory-destination-2.0.1.tgz#8fef8e0f7081f0474fbdd92deb50c990a0264639", "micromark-factory-label@^2.0.0": "https://registry.yarnpkg.com/micromark-factory-label/-/micromark-factory-label-2.0.1.tgz#5267efa97f1e5254efc7f20b459a38cb21058ba1", "micromark-factory-space@^2.0.0": "https://registry.yarnpkg.com/micromark-factory-space/-/micromark-factory-space-2.0.1.tgz#36d0212e962b2b3121f8525fc7a3c7c029f334fc", "micromark-factory-title@^2.0.0": "https://registry.yarnpkg.com/micromark-factory-title/-/micromark-factory-title-2.0.1.tgz#237e4aa5d58a95863f01032d9ee9b090f1de6e94", "micromark-factory-whitespace@^2.0.0": "https://registry.yarnpkg.com/micromark-factory-whitespace/-/micromark-factory-whitespace-2.0.1.tgz#06b26b2983c4d27bfcc657b33e25134d4868b0b1", "micromark-util-character@^2.0.0": "https://registry.yarnpkg.com/micromark-util-character/-/micromark-util-character-2.1.1.tgz#2f987831a40d4c510ac261e89852c4e9703ccda6", "micromark-util-chunked@^2.0.0": "https://registry.yarnpkg.com/micromark-util-chunked/-/micromark-util-chunked-2.0.1.tgz#47fbcd93471a3fccab86cff03847fc3552db1051", "micromark-util-classify-character@^2.0.0": "https://registry.yarnpkg.com/micromark-util-classify-character/-/micromark-util-classify-character-2.0.1.tgz#d399faf9c45ca14c8b4be98b1ea481bced87b629", "micromark-util-combine-extensions@^2.0.0": "https://registry.yarnpkg.com/micromark-util-combine-extensions/-/micromark-util-combine-extensions-2.0.1.tgz#2a0f490ab08bff5cc2fd5eec6dd0ca04f89b30a9", "micromark-util-decode-numeric-character-reference@^2.0.0": "https://registry.yarnpkg.com/micromark-util-decode-numeric-character-reference/-/micromark-util-decode-numeric-character-reference-2.0.2.tgz#fcf15b660979388e6f118cdb6bf7d79d73d26fe5", "micromark-util-decode-string@^2.0.0": "https://registry.yarnpkg.com/micromark-util-decode-string/-/micromark-util-decode-string-2.0.1.tgz#6cb99582e5d271e84efca8e61a807994d7161eb2", "micromark-util-encode@^2.0.0": "https://registry.yarnpkg.com/micromark-util-encode/-/micromark-util-encode-2.0.1.tgz#0d51d1c095551cfaac368326963cf55f15f540b8", "micromark-util-html-tag-name@^2.0.0": "https://registry.yarnpkg.com/micromark-util-html-tag-name/-/micromark-util-html-tag-name-2.0.1.tgz#e40403096481986b41c106627f98f72d4d10b825", "micromark-util-normalize-identifier@^2.0.0": "https://registry.yarnpkg.com/micromark-util-normalize-identifier/-/micromark-util-normalize-identifier-2.0.1.tgz#c30d77b2e832acf6526f8bf1aa47bc9c9438c16d", "micromark-util-resolve-all@^2.0.0": "https://registry.yarnpkg.com/micromark-util-resolve-all/-/micromark-util-resolve-all-2.0.1.tgz#e1a2d62cdd237230a2ae11839027b19381e31e8b", "micromark-util-sanitize-uri@^2.0.0": "https://registry.yarnpkg.com/micromark-util-sanitize-uri/-/micromark-util-sanitize-uri-2.0.1.tgz#ab89789b818a58752b73d6b55238621b7faa8fd7", "micromark-util-subtokenize@^2.0.0": "https://registry.yarnpkg.com/micromark-util-subtokenize/-/micromark-util-subtokenize-2.1.0.tgz#d8ade5ba0f3197a1cf6a2999fbbfe6357a1a19ee", "micromark-util-symbol@^2.0.0": "https://registry.yarnpkg.com/micromark-util-symbol/-/micromark-util-symbol-2.0.1.tgz#e5da494e8eb2b071a0d08fb34f6cefec6c0a19b8", "micromark-util-types@^2.0.0": "https://registry.yarnpkg.com/micromark-util-types/-/micromark-util-types-2.0.2.tgz#f00225f5f5a0ebc3254f96c36b6605c4b393908e", "micromark@^4.0.0": "https://registry.yarnpkg.com/micromark/-/micromark-4.0.2.tgz#91395a3e1884a198e62116e33c9c568e39936fdb", "minipass@^7.0.4": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minizlib@^3.0.1": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "mkdirp@^3.0.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "monaco-editor@^0.44.0": "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.44.0.tgz", "motion-dom@^12.23.6": "https://registry.npmjs.org/motion-dom/-/motion-dom-12.23.6.tgz", "motion-utils@^12.23.6": "https://registry.npmjs.org/motion-utils/-/motion-utils-12.23.6.tgz", "ms@2.1.2": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "ms@^2.1.3": "https://registry.yarnpkg.com/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "nanoid@^3.3.4": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz", "nanoid@^4.0.0": "https://registry.npmjs.org/nanoid/-/nanoid-4.0.0.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "node-releases@^2.0.6": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.6.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "oniguruma-parser@^0.12.1": "https://registry.yarnpkg.com/oniguruma-parser/-/oniguruma-parser-0.12.1.tgz#82ba2208d7a2b69ee344b7efe0ae930c627dcc4a", "oniguruma-to-es@^4.3.3": "https://registry.yarnpkg.com/oniguruma-to-es/-/oniguruma-to-es-4.3.3.tgz#50db2c1e28ec365e102c1863dfd3d1d1ad18613e", "parse-entities@^4.0.0": "https://registry.yarnpkg.com/parse-entities/-/parse-entities-4.0.2.tgz#61d46f5ed28e4ee62e9ddc43d6b010188443f159", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^8.4.16": "https://registry.npmjs.org/postcss/-/postcss-8.4.17.tgz", "postcss@^8.4.17": "https://registry.npmjs.org/postcss/-/postcss-8.4.17.tgz", "postcss@^8.4.41": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "property-information@^7.0.0": "https://registry.yarnpkg.com/property-information/-/property-information-7.1.0.tgz#b622e8646e02b580205415586b40804d3e8bfd5d", "react-dnd-html5-backend@^16.0.1": "https://registry.npmjs.org/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz", "react-dnd@^16.0.1": "https://registry.npmjs.org/react-dnd/-/react-dnd-16.0.1.tgz", "react-dom@^18.2.0": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "react-is@^16.7.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-markdown@^10.1.0": "https://registry.yarnpkg.com/react-markdown/-/react-markdown-10.1.0.tgz#e22bc20faddbc07605c15284255653c0f3bad5ca", "react-refresh@^0.14.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.0.tgz", "react-remove-scroll-bar@^2.3.7": "https://registry.yarnpkg.com/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz#99c20f908ee467b385b68a3469b4a3e750012223", "react-remove-scroll@^2.6.3": "https://registry.yarnpkg.com/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz#d2101d414f6d81d7d3bf033f3c1cb4785789f753", "react-resizable-panels@^3.0.3": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.3.tgz", "react-style-singleton@^2.2.2": "https://registry.yarnpkg.com/react-style-singleton/-/react-style-singleton-2.2.3.tgz#4265608be69a4d70cfe3047f2c6c88b2c3ace388", "react-style-singleton@^2.2.3": "https://registry.yarnpkg.com/react-style-singleton/-/react-style-singleton-2.2.3.tgz#4265608be69a4d70cfe3047f2c6c88b2c3ace388", "react@^18.2.0": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "redux@^4.2.0": "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz", "regex-recursion@^6.0.2": "https://registry.yarnpkg.com/regex-recursion/-/regex-recursion-6.0.2.tgz#a0b1977a74c87f073377b938dbedfab2ea582b33", "regex-utilities@^2.3.0": "https://registry.yarnpkg.com/regex-utilities/-/regex-utilities-2.3.0.tgz#87163512a15dce2908cf079c8960d5158ff43280", "regex@^6.0.1": "https://registry.yarnpkg.com/regex/-/regex-6.0.1.tgz#282fa4435d0c700b09c0eb0982b602e05ab6a34f", "remark-gfm@^4.0.1": "https://registry.yarnpkg.com/remark-gfm/-/remark-gfm-4.0.1.tgz#33227b2a74397670d357bf05c098eaf8513f0d6b", "remark-parse@^11.0.0": "https://registry.yarnpkg.com/remark-parse/-/remark-parse-11.0.0.tgz#aa60743fcb37ebf6b069204eb4da304e40db45a1", "remark-rehype@^11.0.0": "https://registry.yarnpkg.com/remark-rehype/-/remark-rehype-11.1.2.tgz#2addaadda80ca9bd9aa0da763e74d16327683b37", "remark-stringify@^11.0.0": "https://registry.yarnpkg.com/remark-stringify/-/remark-stringify-11.0.0.tgz#4c5b01dd711c269df1aaae11743eb7e2e7636fd3", "remixicon@^2.5.0": "https://registry.npmjs.org/remixicon/-/remixicon-2.5.0.tgz", "resolve@^1.22.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz", "rollup@~2.78.0": "https://registry.npmjs.org/rollup/-/rollup-2.78.1.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "scheduler@^0.23.0": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "shiki@^3.8.0": "https://registry.yarnpkg.com/shiki/-/shiki-3.8.0.tgz#8765c0f7852e901a8c9adb74967334070c026bd4", "source-map-js@^1.0.2": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "space-separated-tokens@^2.0.0": "https://registry.yarnpkg.com/space-separated-tokens/-/space-separated-tokens-2.0.2.tgz#1ecd9d2350a3844572c3f4a312bceb018348859f", "state-local@^1.0.6": "https://registry.npmjs.org/state-local/-/state-local-1.0.7.tgz", "stringify-entities@^4.0.0": "https://registry.yarnpkg.com/stringify-entities/-/stringify-entities-4.0.4.tgz#b3b79ef5f277cc4ac73caeb0236c5ba939b3a4f3", "style-to-js@^1.0.0": "https://registry.yarnpkg.com/style-to-js/-/style-to-js-1.1.17.tgz#488b1558a8c1fd05352943f088cc3ce376813d83", "style-to-object@1.0.9": "https://registry.yarnpkg.com/style-to-object/-/style-to-object-1.0.9.tgz#35c65b713f4a6dba22d3d0c61435f965423653f0", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "tailwind-merge@^3.3.1": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.3.1.tgz", "tailwindcss@4.1.11": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz", "tailwindcss@^4.1.11": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "tar@^7.4.3": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "to-fast-properties@^2.0.0": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "trim-lines@^3.0.0": "https://registry.yarnpkg.com/trim-lines/-/trim-lines-3.0.1.tgz#d802e332a07df861c48802c04321017b1bd87338", "trough@^2.0.0": "https://registry.yarnpkg.com/trough/-/trough-2.2.0.tgz#94a60bd6bd375c152c1df911a4b11d5b0256f50f", "tslib@^2.0.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tslib@^2.1.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tslib@^2.4.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tslib@^2.8.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tw-animate-css@^1.3.5": "https://registry.npmjs.org/tw-animate-css/-/tw-animate-css-1.3.5.tgz", "typescript@^4.6.4": "https://registry.npmjs.org/typescript/-/typescript-4.8.4.tgz", "unified@^11.0.0": "https://registry.yarnpkg.com/unified/-/unified-11.0.5.tgz#f66677610a5c0a9ee90cab2b8d4d66037026d9e1", "unist-util-is@^6.0.0": "https://registry.yarnpkg.com/unist-util-is/-/unist-util-is-6.0.0.tgz#b775956486aff107a9ded971d996c173374be424", "unist-util-position@^5.0.0": "https://registry.yarnpkg.com/unist-util-position/-/unist-util-position-5.0.0.tgz#678f20ab5ca1207a97d7ea8a388373c9cf896be4", "unist-util-stringify-position@^4.0.0": "https://registry.yarnpkg.com/unist-util-stringify-position/-/unist-util-stringify-position-4.0.0.tgz#449c6e21a880e0855bf5aabadeb3a740314abac2", "unist-util-visit-parents@^6.0.0": "https://registry.yarnpkg.com/unist-util-visit-parents/-/unist-util-visit-parents-6.0.1.tgz#4d5f85755c3b8f0dc69e21eca5d6d82d22162815", "unist-util-visit@^5.0.0": "https://registry.yarnpkg.com/unist-util-visit/-/unist-util-visit-5.0.0.tgz#a7de1f31f72ffd3519ea71814cccf5fd6a9217d6", "update-browserslist-db@^1.0.9": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "use-callback-ref@^1.3.3": "https://registry.yarnpkg.com/use-callback-ref/-/use-callback-ref-1.3.3.tgz#98d9fab067075841c5b2c6852090d5d0feabe2bf", "use-sidecar@^1.1.3": "https://registry.yarnpkg.com/use-sidecar/-/use-sidecar-1.1.3.tgz#10e7fd897d130b896e2c546c63a5e8233d00efdb", "use-stick-to-bottom@^1.1.1": "https://registry.yarnpkg.com/use-stick-to-bottom/-/use-stick-to-bottom-1.1.1.tgz#999cfa6c5975525ab13f4e4d18315e59f1361869", "use-sync-external-store@^1.2.2": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "vfile-message@^4.0.0": "https://registry.yarnpkg.com/vfile-message/-/vfile-message-4.0.2.tgz#c883c9f677c72c166362fd635f21fc165a7d1181", "vfile@^6.0.0": "https://registry.yarnpkg.com/vfile/-/vfile-6.0.3.tgz#3652ab1c496531852bf55a6bac57af981ebc38ab", "vite@^3.0.2": "https://registry.npmjs.org/vite/-/vite-3.1.6.tgz", "yallist@^5.0.0": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "zustand@^4.4.6": "https://registry.npmjs.org/zustand/-/zustand-4.5.7.tgz", "zwitch@^2.0.0": "https://registry.yarnpkg.com/zwitch/-/zwitch-2.0.4.tgz#c827d4b0acb76fc3e685a4c6ec2902d51070e9d7", "zwitch@^2.0.4": "https://registry.yarnpkg.com/zwitch/-/zwitch-2.0.4.tgz#c827d4b0acb76fc3e685a4c6ec2902d51070e9d7"}, "files": [], "artifacts": {}}