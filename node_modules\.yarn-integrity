{"systemParams": "win32-x64-137", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@monaco-editor/react@^4.6.0", "@radix-ui/react-scroll-area@^1.2.9", "@radix-ui/react-separator@^1.1.7", "@radix-ui/react-slot@^1.2.3", "@radix-ui/react-tabs@^1.1.12", "@radix-ui/react-tooltip@^1.2.7", "@tailwindcss/postcss@^4.1.11", "@tauri-apps/api@^1.1.0", "@tauri-apps/cli@^1.1.0", "@types/node@^18.7.10", "@types/react-dom@^18.0.6", "@types/react@^18.0.15", "@vitejs/plugin-react@^2.0.0", "autoprefixer@^10.4.21", "class-variance-authority@^0.7.1", "clsx@^2.1.1", "framer-motion@^12.23.6", "fuse.js@^7.1.0", "lucide-react@^0.525.0", "monaco-editor@^0.44.0", "nanoid@^4.0.0", "postcss@^8.4.17", "react-dnd-html5-backend@^16.0.1", "react-dnd@^16.0.1", "react-dom@^18.2.0", "react-resizable-panels@^3.0.3", "react@^18.2.0", "remixicon@^2.5.0", "tailwind-merge@^3.3.1", "tailwindcss@^4.1.11", "tw-animate-css@^1.3.5", "typescript@^4.6.4", "vite@^3.0.2", "zustand@^4.4.6"], "lockfileEntries": {"@alloc/quick-lru@^5.2.0": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "@ampproject/remapping@^2.1.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "@ampproject/remapping@^2.3.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@^7.18.6": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "@babel/compat-data@^7.19.3": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.19.3.tgz", "@babel/core@^7.18.13": "https://registry.npmjs.org/@babel/core/-/core-7.19.3.tgz", "@babel/generator@^7.19.3": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.3.tgz", "@babel/helper-annotate-as-pure@^7.18.6": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.18.6.tgz", "@babel/helper-compilation-targets@^7.19.3": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.19.3.tgz", "@babel/helper-environment-visitor@^7.18.9": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "@babel/helper-function-name@^7.19.0": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "@babel/helper-hoist-variables@^7.18.6": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "@babel/helper-module-imports@^7.18.6": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "@babel/helper-module-transforms@^7.19.0": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.19.0.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz", "@babel/helper-plugin-utils@^7.19.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.19.0.tgz", "@babel/helper-simple-access@^7.18.6": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.18.6.tgz", "@babel/helper-split-export-declaration@^7.18.6": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "@babel/helper-string-parser@^7.18.10": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.18.10.tgz", "@babel/helper-validator-identifier@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-identifier@^7.19.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "@babel/helper-validator-option@^7.18.6": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "@babel/helpers@^7.19.0": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.19.0.tgz", "@babel/highlight@^7.18.6": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "@babel/parser@^7.18.10": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz", "@babel/parser@^7.19.3": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz", "@babel/plugin-syntax-jsx@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.18.6.tgz", "@babel/plugin-transform-react-jsx-development@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.18.6.tgz", "@babel/plugin-transform-react-jsx-self@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.18.6.tgz", "@babel/plugin-transform-react-jsx-source@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.18.6.tgz", "@babel/plugin-transform-react-jsx@^7.18.10": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz", "@babel/plugin-transform-react-jsx@^7.18.6": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.19.0.tgz", "@babel/runtime@^7.9.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.18.10": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "@babel/traverse@^7.19.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.19.3.tgz", "@babel/traverse@^7.19.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.19.3.tgz", "@babel/types@^7.18.10": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.18.6": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.19.0": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@babel/types@^7.19.3": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "@emnapi/core@^1.4.3": "https://registry.yarnpkg.com/@emnapi/core/-/core-1.4.4.tgz#76620673f3033626c6d79b1420d69f06a6bb153c", "@emnapi/runtime@^1.4.3": "https://registry.yarnpkg.com/@emnapi/runtime/-/runtime-1.4.4.tgz#19a8f00719c51124e2d0fbf4aaad3fa7b0c92524", "@emnapi/wasi-threads@1.0.3": "https://registry.yarnpkg.com/@emnapi/wasi-threads/-/wasi-threads-1.0.3.tgz#83fa228bde0e71668aad6db1af4937473d1d3ab1", "@emnapi/wasi-threads@^1.0.2": "https://registry.yarnpkg.com/@emnapi/wasi-threads/-/wasi-threads-1.0.3.tgz#83fa228bde0e71668aad6db1af4937473d1d3ab1", "@esbuild/android-arm@0.15.10": "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.15.10.tgz#a5f9432eb221afc243c321058ef25fe899886892", "@esbuild/linux-loong64@0.15.10": "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.15.10.tgz#78a42897c2cf8db9fd5f1811f7590393b77774c7", "@floating-ui/core@^1.7.2": "https://registry.yarnpkg.com/@floating-ui/core/-/core-1.7.2.tgz#3d1c35263950b314b6d5a72c8bfb9e3c1551aefd", "@floating-ui/dom@^1.7.2": "https://registry.yarnpkg.com/@floating-ui/dom/-/dom-1.7.2.tgz#3540b051cf5ce0d4f4db5fb2507a76e8ea5b4a45", "@floating-ui/react-dom@^2.0.0": "https://registry.yarnpkg.com/@floating-ui/react-dom/-/react-dom-2.1.4.tgz#a0689be8978352fff2be2dfdd718cf668c488ec3", "@floating-ui/utils@^0.2.10": "https://registry.yarnpkg.com/@floating-ui/utils/-/utils-0.2.10.tgz#a2a1e3812d14525f725d011a73eceb41fef5bc1c", "@isaacs/fs-minipass@^4.0.0": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "@jridgewell/gen-mapping@^0.1.0": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "@jridgewell/resolve-uri@3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.0.0": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/set-array@^1.0.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "@jridgewell/sourcemap-codec@1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "@jridgewell/trace-mapping@^0.3.9": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.16.tgz", "@monaco-editor/loader@^1.5.0": "https://registry.npmjs.org/@monaco-editor/loader/-/loader-1.5.0.tgz", "@monaco-editor/react@^4.6.0": "https://registry.npmjs.org/@monaco-editor/react/-/react-4.7.0.tgz", "@napi-rs/wasm-runtime@^0.2.11": "https://registry.yarnpkg.com/@napi-rs/wasm-runtime/-/wasm-runtime-0.2.12.tgz#3e78a8b96e6c33a6c517e1894efbd5385a7cb6f2", "@radix-ui/number@1.1.1": "https://registry.yarnpkg.com/@radix-ui/number/-/number-1.1.1.tgz#7b2c9225fbf1b126539551f5985769d0048d9090", "@radix-ui/primitive@1.1.2": "https://registry.yarnpkg.com/@radix-ui/primitive/-/primitive-1.1.2.tgz#83f415c4425f21e3d27914c12b3272a32e3dae65", "@radix-ui/react-arrow@1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz#e14a2657c81d961598c5e72b73dd6098acc04f09", "@radix-ui/react-collection@1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-collection/-/react-collection-1.1.7.tgz#d05c25ca9ac4695cc19ba91f42f686e3ea2d9aec", "@radix-ui/react-compose-refs@1.1.2": "https://registry.yarnpkg.com/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz#a2c4c47af6337048ee78ff6dc0d090b390d2bb30", "@radix-ui/react-context@1.1.2": "https://registry.yarnpkg.com/@radix-ui/react-context/-/react-context-1.1.2.tgz#61628ef269a433382c364f6f1e3788a6dc213a36", "@radix-ui/react-direction@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-direction/-/react-direction-1.1.1.tgz#39e5a5769e676c753204b792fbe6cf508e550a14", "@radix-ui/react-dismissable-layer@1.1.10": "https://registry.yarnpkg.com/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz#429b9bada3672c6895a5d6a642aca6ecaf4f18c3", "@radix-ui/react-id@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-id/-/react-id-1.1.1.tgz#1404002e79a03fe062b7e3864aa01e24bd1471f7", "@radix-ui/react-popper@1.2.7": "https://registry.yarnpkg.com/@radix-ui/react-popper/-/react-popper-1.2.7.tgz#531cf2eebb3d3270d58f7d8136e4517646429978", "@radix-ui/react-portal@1.1.9": "https://registry.yarnpkg.com/@radix-ui/react-portal/-/react-portal-1.1.9.tgz#14c3649fe48ec474ac51ed9f2b9f5da4d91c4472", "@radix-ui/react-presence@1.1.4": "https://registry.yarnpkg.com/@radix-ui/react-presence/-/react-presence-1.1.4.tgz#253ac0ad4946c5b4a9c66878335f5cf07c967ced", "@radix-ui/react-primitive@2.1.3": "https://registry.yarnpkg.com/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz#db9b8bcff49e01be510ad79893fb0e4cda50f1bc", "@radix-ui/react-roving-focus@1.1.10": "https://registry.yarnpkg.com/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz#46030496d2a490c4979d29a7e1252465e51e4b0b", "@radix-ui/react-scroll-area@^1.2.9": "https://registry.yarnpkg.com/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.9.tgz#90c49bd3231d7f0796d5d12dabc065afa829cf07", "@radix-ui/react-separator@^1.1.7": "https://registry.yarnpkg.com/@radix-ui/react-separator/-/react-separator-1.1.7.tgz#a18bd7fd07c10fda1bba14f2a3032e7b1a2b3470", "@radix-ui/react-slot@1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-slot/-/react-slot-1.2.3.tgz#502d6e354fc847d4169c3bc5f189de777f68cfe1", "@radix-ui/react-slot@^1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-slot/-/react-slot-1.2.3.tgz#502d6e354fc847d4169c3bc5f189de777f68cfe1", "@radix-ui/react-tabs@^1.1.12": "https://registry.yarnpkg.com/@radix-ui/react-tabs/-/react-tabs-1.1.12.tgz#99b3522c73db9263f429a6d0f5a9acb88df3b129", "@radix-ui/react-tooltip@^1.2.7": "https://registry.yarnpkg.com/@radix-ui/react-tooltip/-/react-tooltip-1.2.7.tgz#23612ac7a5e8e1f6829e46d0e0ad94afe3976c72", "@radix-ui/react-use-callback-ref@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz#62a4dba8b3255fdc5cc7787faeac1c6e4cc58d40", "@radix-ui/react-use-controllable-state@1.2.2": "https://registry.yarnpkg.com/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz#905793405de57d61a439f4afebbb17d0645f3190", "@radix-ui/react-use-effect-event@0.0.2": "https://registry.yarnpkg.com/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz#090cf30d00a4c7632a15548512e9152217593907", "@radix-ui/react-use-escape-keydown@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz#b3fed9bbea366a118f40427ac40500aa1423cc29", "@radix-ui/react-use-layout-effect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz#0c4230a9eed49d4589c967e2d9c0d9d60a23971e", "@radix-ui/react-use-rect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz#01443ca8ed071d33023c1113e5173b5ed8769152", "@radix-ui/react-use-size@1.1.1": "https://registry.yarnpkg.com/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz#6de276ffbc389a537ffe4316f5b0f24129405b37", "@radix-ui/react-visually-hidden@1.2.3": "https://registry.yarnpkg.com/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.3.tgz#a8c38c8607735dc9f05c32f87ab0f9c2b109efbf", "@radix-ui/rect@1.1.1": "https://registry.yarnpkg.com/@radix-ui/rect/-/rect-1.1.1.tgz#78244efe12930c56fd255d7923865857c41ac8cb", "@react-dnd/asap@^5.0.1": "https://registry.npmjs.org/@react-dnd/asap/-/asap-5.0.2.tgz", "@react-dnd/invariant@^4.0.1": "https://registry.npmjs.org/@react-dnd/invariant/-/invariant-4.0.2.tgz", "@react-dnd/shallowequal@^4.0.1": "https://registry.npmjs.org/@react-dnd/shallowequal/-/shallowequal-4.0.2.tgz", "@tailwindcss/node@4.1.11": "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.11.tgz", "@tailwindcss/oxide-android-arm64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-android-arm64/-/oxide-android-arm64-4.1.11.tgz#1f387d8302f011b61c226deb0c3a1d2bd79c6915", "@tailwindcss/oxide-darwin-arm64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-darwin-arm64/-/oxide-darwin-arm64-4.1.11.tgz#acd35ffb7e4eae83d0a3fe2f8ea36cfcc9b21f7e", "@tailwindcss/oxide-darwin-x64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-darwin-x64/-/oxide-darwin-x64-4.1.11.tgz#a0022312993a3893d6ff0312d6e3c83c4636fef4", "@tailwindcss/oxide-freebsd-x64@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-freebsd-x64/-/oxide-freebsd-x64-4.1.11.tgz#dd8e55eb0b88fe7995b8148c0e0ae5fa27092d22", "@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm-gnueabihf/-/oxide-linux-arm-gnueabihf-4.1.11.tgz#02ee99090988847d3f13d277679012cbffcdde37", "@tailwindcss/oxide-linux-arm64-gnu@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm64-gnu/-/oxide-linux-arm64-gnu-4.1.11.tgz#4837559c102bebe65089879f6a0278ed473b4813", "@tailwindcss/oxide-linux-arm64-musl@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-arm64-musl/-/oxide-linux-arm64-musl-4.1.11.tgz#bec465112a13a1383558ff36afdf28b8a8cb9021", "@tailwindcss/oxide-linux-x64-gnu@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-x64-gnu/-/oxide-linux-x64-gnu-4.1.11.tgz#f9e47e6aa67ff77f32f7412bc9698d4278e101bf", "@tailwindcss/oxide-linux-x64-musl@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-linux-x64-musl/-/oxide-linux-x64-musl-4.1.11.tgz#7d6e8adcfb9bc84d8e2e2e8781d661edb9e41ba8", "@tailwindcss/oxide-wasm32-wasi@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-wasm32-wasi/-/oxide-wasm32-wasi-4.1.11.tgz#a1762f4939c6ebaa824696fda2fd7db1b85fbed2", "@tailwindcss/oxide-win32-arm64-msvc@4.1.11": "https://registry.yarnpkg.com/@tailwindcss/oxide-win32-arm64-msvc/-/oxide-win32-arm64-msvc-4.1.11.tgz#70ba392dca0fa3707ebe27d2bd6ac3e69d35e3b7", "@tailwindcss/oxide-win32-x64-msvc@4.1.11": "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.11.tgz", "@tailwindcss/oxide@4.1.11": "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.11.tgz", "@tailwindcss/postcss@^4.1.11": "https://registry.npmjs.org/@tailwindcss/postcss/-/postcss-4.1.11.tgz", "@tauri-apps/api@^1.1.0": "https://registry.npmjs.org/@tauri-apps/api/-/api-1.1.0.tgz", "@tauri-apps/cli-darwin-arm64@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-darwin-arm64/-/cli-darwin-arm64-1.1.1.tgz#c6f4553cfb338f24131910a1ebbe9fe74d89b8ae", "@tauri-apps/cli-darwin-x64@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-darwin-x64/-/cli-darwin-x64-1.1.1.tgz#11beb8b3dfc43725f0acd3736cd9d181f93526a2", "@tauri-apps/cli-linux-arm-gnueabihf@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm-gnueabihf/-/cli-linux-arm-gnueabihf-1.1.1.tgz#f4bd3f5839cfcd69132d213efee09ce5da5c6d90", "@tauri-apps/cli-linux-arm64-gnu@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm64-gnu/-/cli-linux-arm64-gnu-1.1.1.tgz#2631b2a68e7901ea7281af022d8826c0d82b9edd", "@tauri-apps/cli-linux-arm64-musl@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-arm64-musl/-/cli-linux-arm64-musl-1.1.1.tgz#11a4bdc52696583152fc70ea373cf31717cb0f4c", "@tauri-apps/cli-linux-x64-gnu@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-x64-gnu/-/cli-linux-x64-gnu-1.1.1.tgz#11acbcc4743562cc8166c92ffc3bc12f68e0987f", "@tauri-apps/cli-linux-x64-musl@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-linux-x64-musl/-/cli-linux-x64-musl-1.1.1.tgz#7dd81b8380d7e7183fb43a2ccbbc26931718204a", "@tauri-apps/cli-win32-ia32-msvc@1.1.1": "https://registry.yarnpkg.com/@tauri-apps/cli-win32-ia32-msvc/-/cli-win32-ia32-msvc-1.1.1.tgz#1d106ae1c3766f95d7f49bac51eba0d2cd2daf33", "@tauri-apps/cli-win32-x64-msvc@1.1.1": "https://registry.npmjs.org/@tauri-apps/cli-win32-x64-msvc/-/cli-win32-x64-msvc-1.1.1.tgz", "@tauri-apps/cli@^1.1.0": "https://registry.npmjs.org/@tauri-apps/cli/-/cli-1.1.1.tgz", "@tybys/wasm-util@^0.10.0": "https://registry.yarnpkg.com/@tybys/wasm-util/-/wasm-util-0.10.0.tgz#2fd3cd754b94b378734ce17058d0507c45c88369", "@tybys/wasm-util@^0.9.0": "https://registry.yarnpkg.com/@tybys/wasm-util/-/wasm-util-0.9.0.tgz#3e75eb00604c8d6db470bf18c37b7d984a0e3355", "@types/node@^18.7.10": "https://registry.npmjs.org/@types/node/-/node-18.8.3.tgz", "@types/prop-types@*": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.5.tgz", "@types/react-dom@^18.0.6": "https://registry.npmjs.org/@types/react-dom/-/react-dom-18.0.6.tgz", "@types/react@*": "https://registry.npmjs.org/@types/react/-/react-18.0.21.tgz", "@types/react@^18.0.15": "https://registry.npmjs.org/@types/react/-/react-18.0.21.tgz", "@types/scheduler@*": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "@vitejs/plugin-react@^2.0.0": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-2.1.0.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "autoprefixer@^10.4.21": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "browserslist@^4.21.3": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "caniuse-lite@^1.0.30001400": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001418.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "caniuse-lite@^1.0.30001726": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001727.tgz", "chalk@^2.0.0": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chownr@^3.0.0": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "class-variance-authority@^0.7.1": "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz", "clsx@^2.1.1": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "convert-source-map@^1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.8.0.tgz", "csstype@^3.0.2": "https://registry.npmjs.org/csstype/-/csstype-3.1.1.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "detect-libc@^2.0.3": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "detect-libc@^2.0.4": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz", "dnd-core@^16.0.1": "https://registry.npmjs.org/dnd-core/-/dnd-core-16.0.1.tgz", "electron-to-chromium@^1.4.251": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.276.tgz", "electron-to-chromium@^1.5.173": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.184.tgz", "enhanced-resolve@^5.18.1": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz", "esbuild-android-64@0.15.10": "https://registry.yarnpkg.com/esbuild-android-64/-/esbuild-android-64-0.15.10.tgz#8a59a84acbf2eca96996cadc35642cf055c494f0", "esbuild-android-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-android-arm64/-/esbuild-android-arm64-0.15.10.tgz#f453851dc1d8c5409a38cf7613a33852faf4915d", "esbuild-darwin-64@0.15.10": "https://registry.yarnpkg.com/esbuild-darwin-64/-/esbuild-darwin-64-0.15.10.tgz#778bd29c8186ff47b176c8af58c08cf0fb8e6b86", "esbuild-darwin-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-darwin-arm64/-/esbuild-darwin-arm64-0.15.10.tgz#b30bbefb46dc3c5d4708b0435e52f6456578d6df", "esbuild-freebsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-freebsd-64/-/esbuild-freebsd-64-0.15.10.tgz#ab301c5f6ded5110dbdd611140bef1a7c2e99236", "esbuild-freebsd-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-freebsd-arm64/-/esbuild-freebsd-arm64-0.15.10.tgz#a5b09b867a6ff49110f52343b6f12265db63d43f", "esbuild-linux-32@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-32/-/esbuild-linux-32-0.15.10.tgz#5282fe9915641caf9c8070e4ba2c3e16d358f837", "esbuild-linux-64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-64/-/esbuild-linux-64-0.15.10.tgz#f3726e85a00149580cb19f8abfabcbb96f5d52bb", "esbuild-linux-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-arm64/-/esbuild-linux-arm64-0.15.10.tgz#2f0056e9d5286edb0185b56655caa8c574d8dbe7", "esbuild-linux-arm@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-arm/-/esbuild-linux-arm-0.15.10.tgz#40a9270da3c8ffa32cf72e24a79883e323dff08d", "esbuild-linux-mips64le@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-mips64le/-/esbuild-linux-mips64le-0.15.10.tgz#90ce1c4ee0202edb4ac69807dea77f7e5804abc4", "esbuild-linux-ppc64le@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-ppc64le/-/esbuild-linux-ppc64le-0.15.10.tgz#782837ae7bd5b279178106c9dd801755a21fabdf", "esbuild-linux-riscv64@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-riscv64/-/esbuild-linux-riscv64-0.15.10.tgz#d7420d806ece5174f24f4634303146f915ab4207", "esbuild-linux-s390x@0.15.10": "https://registry.yarnpkg.com/esbuild-linux-s390x/-/esbuild-linux-s390x-0.15.10.tgz#21fdf0cb3494a7fb520a71934e4dffce67fe47be", "esbuild-netbsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-netbsd-64/-/esbuild-netbsd-64-0.15.10.tgz#6c06b3107e3df53de381e6299184d4597db0440f", "esbuild-openbsd-64@0.15.10": "https://registry.yarnpkg.com/esbuild-openbsd-64/-/esbuild-openbsd-64-0.15.10.tgz#4daef5f5d8e74bbda53b65160029445d582570cf", "esbuild-sunos-64@0.15.10": "https://registry.yarnpkg.com/esbuild-sunos-64/-/esbuild-sunos-64-0.15.10.tgz#5fe7bef267a02f322fd249a8214d0274937388a7", "esbuild-windows-32@0.15.10": "https://registry.yarnpkg.com/esbuild-windows-32/-/esbuild-windows-32-0.15.10.tgz#48e3dde25ab0135579a288b30ab6ddef6d1f0b28", "esbuild-windows-64@0.15.10": "https://registry.npmjs.org/esbuild-windows-64/-/esbuild-windows-64-0.15.10.tgz", "esbuild-windows-arm64@0.15.10": "https://registry.yarnpkg.com/esbuild-windows-arm64/-/esbuild-windows-arm64-0.15.10.tgz#5a6fcf2fa49e895949bf5495cf088ab1b43ae879", "esbuild@^0.15.9": "https://registry.npmjs.org/esbuild/-/esbuild-0.15.10.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "framer-motion@^12.23.6": "https://registry.npmjs.org/framer-motion/-/framer-motion-12.23.6.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.1": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "fuse.js@^7.1.0": "https://registry.npmjs.org/fuse.js/-/fuse.js-7.1.0.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has@^1.0.3": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "hoist-non-react-statics@^3.3.2": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "is-core-module@^2.9.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.10.0.tgz", "jiti@^2.4.2": "https://registry.npmjs.org/jiti/-/jiti-2.4.2.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "jsesc@^2.5.1": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "json5@^2.2.1": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "lightningcss-darwin-arm64@1.30.1": "https://registry.yarnpkg.com/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.30.1.tgz#3d47ce5e221b9567c703950edf2529ca4a3700ae", "lightningcss-darwin-x64@1.30.1": "https://registry.yarnpkg.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.30.1.tgz#e81105d3fd6330860c15fe860f64d39cff5fbd22", "lightningcss-freebsd-x64@1.30.1": "https://registry.yarnpkg.com/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.30.1.tgz#a0e732031083ff9d625c5db021d09eb085af8be4", "lightningcss-linux-arm-gnueabihf@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.30.1.tgz#1f5ecca6095528ddb649f9304ba2560c72474908", "lightningcss-linux-arm64-gnu@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.30.1.tgz#eee7799726103bffff1e88993df726f6911ec009", "lightningcss-linux-arm64-musl@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.30.1.tgz#f2e4b53f42892feeef8f620cbb889f7c064a7dfe", "lightningcss-linux-x64-gnu@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.30.1.tgz#2fc7096224bc000ebb97eea94aea248c5b0eb157", "lightningcss-linux-x64-musl@1.30.1": "https://registry.yarnpkg.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.30.1.tgz#66dca2b159fd819ea832c44895d07e5b31d75f26", "lightningcss-win32-arm64-msvc@1.30.1": "https://registry.yarnpkg.com/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.30.1.tgz#7d8110a19d7c2d22bfdf2f2bb8be68e7d1b69039", "lightningcss-win32-x64-msvc@1.30.1": "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz", "lightningcss@1.30.1": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz", "loose-envify@^1.1.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lucide-react@^0.525.0": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.525.0.tgz", "magic-string@^0.26.2": "https://registry.npmjs.org/magic-string/-/magic-string-0.26.6.tgz", "magic-string@^0.30.17": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "minipass@^7.0.4": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minizlib@^3.0.1": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "mkdirp@^3.0.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "monaco-editor@^0.44.0": "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.44.0.tgz", "motion-dom@^12.23.6": "https://registry.npmjs.org/motion-dom/-/motion-dom-12.23.6.tgz", "motion-utils@^12.23.6": "https://registry.npmjs.org/motion-utils/-/motion-utils-12.23.6.tgz", "ms@2.1.2": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "nanoid@^3.3.4": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.4.tgz", "nanoid@^4.0.0": "https://registry.npmjs.org/nanoid/-/nanoid-4.0.0.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "node-releases@^2.0.6": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.6.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^8.4.16": "https://registry.npmjs.org/postcss/-/postcss-8.4.17.tgz", "postcss@^8.4.17": "https://registry.npmjs.org/postcss/-/postcss-8.4.17.tgz", "postcss@^8.4.41": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "react-dnd-html5-backend@^16.0.1": "https://registry.npmjs.org/react-dnd-html5-backend/-/react-dnd-html5-backend-16.0.1.tgz", "react-dnd@^16.0.1": "https://registry.npmjs.org/react-dnd/-/react-dnd-16.0.1.tgz", "react-dom@^18.2.0": "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz", "react-is@^16.7.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-refresh@^0.14.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.0.tgz", "react-resizable-panels@^3.0.3": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.3.tgz", "react@^18.2.0": "https://registry.npmjs.org/react/-/react-18.2.0.tgz", "redux@^4.2.0": "https://registry.npmjs.org/redux/-/redux-4.2.1.tgz", "remixicon@^2.5.0": "https://registry.npmjs.org/remixicon/-/remixicon-2.5.0.tgz", "resolve@^1.22.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.1.tgz", "rollup@~2.78.0": "https://registry.npmjs.org/rollup/-/rollup-2.78.1.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "scheduler@^0.23.0": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "source-map-js@^1.0.2": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.0.2.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "state-local@^1.0.6": "https://registry.npmjs.org/state-local/-/state-local-1.0.7.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "tailwind-merge@^3.3.1": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.3.1.tgz", "tailwindcss@4.1.11": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz", "tailwindcss@^4.1.11": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "tar@^7.4.3": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "to-fast-properties@^2.0.0": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "tslib@^2.4.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tslib@^2.8.0": "https://registry.yarnpkg.com/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f", "tw-animate-css@^1.3.5": "https://registry.npmjs.org/tw-animate-css/-/tw-animate-css-1.3.5.tgz", "typescript@^4.6.4": "https://registry.npmjs.org/typescript/-/typescript-4.8.4.tgz", "update-browserslist-db@^1.0.9": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "use-sync-external-store@^1.2.2": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "vite@^3.0.2": "https://registry.npmjs.org/vite/-/vite-3.1.6.tgz", "yallist@^5.0.0": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "zustand@^4.4.6": "https://registry.npmjs.org/zustand/-/zustand-4.5.7.tgz"}, "files": [], "artifacts": {}}