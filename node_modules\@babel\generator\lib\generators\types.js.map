{"version": 3, "names": ["isAssignmentPattern", "isIdentifier", "Identifier", "node", "exactSource", "loc", "word", "name", "ArgumentPlaceholder", "token", "RestElement", "print", "argument", "ObjectExpression", "props", "properties", "printInnerComments", "length", "space", "printList", "indent", "statement", "ObjectMethod", "printJoin", "decorators", "_methodHead", "body", "ObjectProperty", "computed", "key", "value", "left", "shorthand", "ArrayExpression", "elems", "elements", "len", "i", "elem", "RecordExpression", "startToken", "endToken", "format", "recordAndTupleSyntaxType", "Error", "JSON", "stringify", "TupleExpression", "RegExpLiteral", "pattern", "flags", "<PERSON>olean<PERSON>iter<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NumericLiteral", "raw", "getPossibleRaw", "opts", "jsescOption", "numbers", "number", "jsesc", "minified", "StringLiteral", "undefined", "val", "Object", "assign", "jsonCompatibleStrings", "json", "BigIntLiteral", "DecimalLiteral", "validTopicTokenSet", "Set", "TopicReference", "topicToken", "has", "givenTopicTokenJSON", "validTopics", "Array", "from", "v", "join", "PipelineTopicExpression", "expression", "PipelineBareFunction", "callee", "PipelinePrimaryTopicReference"], "sources": ["../../src/generators/types.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport { isAssignmentPattern, isIdentifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport jsesc from \"jsesc\";\n\nexport function Identifier(this: Printer, node: t.Identifier) {\n  this.exactSource(node.loc, () => {\n    this.word(node.name);\n  });\n}\n\nexport function ArgumentPlaceholder(this: Printer) {\n  this.token(\"?\");\n}\n\nexport function RestElement(this: Printer, node: t.RestElement) {\n  this.token(\"...\");\n  this.print(node.argument, node);\n}\n\nexport { RestElement as SpreadElement };\n\nexport function ObjectExpression(this: Printer, node: t.ObjectExpression) {\n  const props = node.properties;\n\n  this.token(\"{\");\n  this.printInnerComments(node);\n\n  if (props.length) {\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n  }\n\n  this.token(\"}\");\n}\n\nexport { ObjectExpression as ObjectPattern };\n\nexport function ObjectMethod(this: Printer, node: t.ObjectMethod) {\n  this.printJoin(node.decorators, node);\n  this._methodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ObjectProperty(this: Printer, node: t.ObjectProperty) {\n  this.printJoin(node.decorators, node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // print `({ foo: foo = 5 } = {})` as `({ foo = 5 } = {});`\n    if (\n      isAssignmentPattern(node.value) &&\n      isIdentifier(node.key) &&\n      // @ts-expect-error todo(flow->ts) `.name` does not exist on some types in union\n      node.key.name === node.value.left.name\n    ) {\n      this.print(node.value, node);\n      return;\n    }\n\n    this.print(node.key, node);\n\n    // shorthand!\n    if (\n      node.shorthand &&\n      isIdentifier(node.key) &&\n      isIdentifier(node.value) &&\n      node.key.name === node.value.name\n    ) {\n      return;\n    }\n  }\n\n  this.token(\":\");\n  this.space();\n  this.print(node.value, node);\n}\n\nexport function ArrayExpression(this: Printer, node: t.ArrayExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  this.token(\"[\");\n  this.printInnerComments(node);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    } else {\n      // If the array expression ends with a hole, that hole\n      // will be ignored by the interpreter, but if it ends with\n      // two (or more) holes, we need to write out two (or more)\n      // commas so that the resulting code is interpreted with\n      // both (all) of the holes.\n      this.token(\",\");\n    }\n  }\n\n  this.token(\"]\");\n}\n\nexport { ArrayExpression as ArrayPattern };\n\nexport function RecordExpression(this: Printer, node: t.RecordExpression) {\n  const props = node.properties;\n\n  let startToken;\n  let endToken;\n  if (this.format.recordAndTupleSyntaxType === \"bar\") {\n    startToken = \"{|\";\n    endToken = \"|}\";\n  } else if (\n    this.format.recordAndTupleSyntaxType !== \"hash\" &&\n    this.format.recordAndTupleSyntaxType != null\n  ) {\n    throw new Error(\n      `The \"recordAndTupleSyntaxType\" generator option must be \"bar\" or \"hash\" (${JSON.stringify(\n        this.format.recordAndTupleSyntaxType,\n      )} received).`,\n    );\n  } else {\n    startToken = \"#{\";\n    endToken = \"}\";\n  }\n\n  this.token(startToken);\n  this.printInnerComments(node);\n\n  if (props.length) {\n    this.space();\n    this.printList(props, node, { indent: true, statement: true });\n    this.space();\n  }\n  this.token(endToken);\n}\n\nexport function TupleExpression(this: Printer, node: t.TupleExpression) {\n  const elems = node.elements;\n  const len = elems.length;\n\n  let startToken;\n  let endToken;\n  if (this.format.recordAndTupleSyntaxType === \"bar\") {\n    startToken = \"[|\";\n    endToken = \"|]\";\n  } else if (this.format.recordAndTupleSyntaxType === \"hash\") {\n    startToken = \"#[\";\n    endToken = \"]\";\n  } else {\n    throw new Error(\n      `${this.format.recordAndTupleSyntaxType} is not a valid recordAndTuple syntax type`,\n    );\n  }\n\n  this.token(startToken);\n  this.printInnerComments(node);\n\n  for (let i = 0; i < elems.length; i++) {\n    const elem = elems[i];\n    if (elem) {\n      if (i > 0) this.space();\n      this.print(elem, node);\n      if (i < len - 1) this.token(\",\");\n    }\n  }\n\n  this.token(endToken);\n}\n\nexport function RegExpLiteral(this: Printer, node: t.RegExpLiteral) {\n  this.word(`/${node.pattern}/${node.flags}`);\n}\n\nexport function BooleanLiteral(this: Printer, node: t.BooleanLiteral) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteral(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function NumericLiteral(this: Printer, node: t.NumericLiteral) {\n  const raw = this.getPossibleRaw(node);\n  const opts = this.format.jsescOption;\n  const value = node.value + \"\";\n  if (opts.numbers) {\n    this.number(jsesc(node.value, opts));\n  } else if (raw == null) {\n    this.number(value); // normalize\n  } else if (this.format.minified) {\n    this.number(raw.length < value.length ? raw : value);\n  } else {\n    this.number(raw);\n  }\n}\n\nexport function StringLiteral(this: Printer, node: t.StringLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const val = jsesc(\n    node.value,\n    process.env.BABEL_8_BREAKING\n      ? this.format.jsescOption\n      : Object.assign(\n          this.format.jsescOption,\n          this.format.jsonCompatibleStrings && { json: true },\n        ),\n  );\n\n  return this.token(val);\n}\n\nexport function BigIntLiteral(this: Printer, node: t.BigIntLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"n\");\n}\n\nexport function DecimalLiteral(this: Printer, node: t.DecimalLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.word(raw);\n    return;\n  }\n  this.word(node.value + \"m\");\n}\n\n// Hack pipe operator\nconst validTopicTokenSet = new Set([\"^^\", \"@@\", \"^\", \"%\", \"#\"]);\nexport function TopicReference(this: Printer) {\n  const { topicToken } = this.format;\n\n  if (validTopicTokenSet.has(topicToken)) {\n    this.token(topicToken);\n  } else {\n    const givenTopicTokenJSON = JSON.stringify(topicToken);\n    const validTopics = Array.from(validTopicTokenSet, v => JSON.stringify(v));\n    throw new Error(\n      `The \"topicToken\" generator option must be one of ` +\n        `${validTopics.join(\", \")} (${givenTopicTokenJSON} received instead).`,\n    );\n  }\n}\n\n// Smart-mix pipe operator\nexport function PipelineTopicExpression(\n  this: Printer,\n  node: t.PipelineTopicExpression,\n) {\n  this.print(node.expression, node);\n}\n\nexport function PipelineBareFunction(\n  this: Printer,\n  node: t.PipelineBareFunction,\n) {\n  this.print(node.callee, node);\n}\n\nexport function PipelinePrimaryTopicReference(this: Printer) {\n  this.token(\"#\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA;;;EAFSA,mB;EAAqBC;;;AAIvB,SAASC,UAAT,CAAmCC,IAAnC,EAAuD;EAC5D,KAAKC,WAAL,CAAiBD,IAAI,CAACE,GAAtB,EAA2B,MAAM;IAC/B,KAAKC,IAAL,CAAUH,IAAI,CAACI,IAAf;EACD,CAFD;AAGD;;AAEM,SAASC,mBAAT,GAA4C;EACjD,KAAKC,SAAL;AACD;;AAEM,SAASC,WAAT,CAAoCP,IAApC,EAAyD;EAC9D,KAAKM,KAAL,CAAW,KAAX;EACA,KAAKE,KAAL,CAAWR,IAAI,CAACS,QAAhB,EAA0BT,IAA1B;AACD;;AAIM,SAASU,gBAAT,CAAyCV,IAAzC,EAAmE;EACxE,MAAMW,KAAK,GAAGX,IAAI,CAACY,UAAnB;EAEA,KAAKN,SAAL;EACA,KAAKO,kBAAL,CAAwBb,IAAxB;;EAEA,IAAIW,KAAK,CAACG,MAAV,EAAkB;IAChB,KAAKC,KAAL;IACA,KAAKC,SAAL,CAAeL,KAAf,EAAsBX,IAAtB,EAA4B;MAAEiB,MAAM,EAAE,IAAV;MAAgBC,SAAS,EAAE;IAA3B,CAA5B;IACA,KAAKH,KAAL;EACD;;EAED,KAAKT,SAAL;AACD;;AAIM,SAASa,YAAT,CAAqCnB,IAArC,EAA2D;EAChE,KAAKoB,SAAL,CAAepB,IAAI,CAACqB,UAApB,EAAgCrB,IAAhC;;EACA,KAAKsB,WAAL,CAAiBtB,IAAjB;;EACA,KAAKe,KAAL;EACA,KAAKP,KAAL,CAAWR,IAAI,CAACuB,IAAhB,EAAsBvB,IAAtB;AACD;;AAEM,SAASwB,cAAT,CAAuCxB,IAAvC,EAA+D;EACpE,KAAKoB,SAAL,CAAepB,IAAI,CAACqB,UAApB,EAAgCrB,IAAhC;;EAEA,IAAIA,IAAI,CAACyB,QAAT,EAAmB;IACjB,KAAKnB,SAAL;IACA,KAAKE,KAAL,CAAWR,IAAI,CAAC0B,GAAhB,EAAqB1B,IAArB;IACA,KAAKM,SAAL;EACD,CAJD,MAIO;IAEL,IACET,mBAAmB,CAACG,IAAI,CAAC2B,KAAN,CAAnB,IACA7B,YAAY,CAACE,IAAI,CAAC0B,GAAN,CADZ,IAGA1B,IAAI,CAAC0B,GAAL,CAAStB,IAAT,KAAkBJ,IAAI,CAAC2B,KAAL,CAAWC,IAAX,CAAgBxB,IAJpC,EAKE;MACA,KAAKI,KAAL,CAAWR,IAAI,CAAC2B,KAAhB,EAAuB3B,IAAvB;MACA;IACD;;IAED,KAAKQ,KAAL,CAAWR,IAAI,CAAC0B,GAAhB,EAAqB1B,IAArB;;IAGA,IACEA,IAAI,CAAC6B,SAAL,IACA/B,YAAY,CAACE,IAAI,CAAC0B,GAAN,CADZ,IAEA5B,YAAY,CAACE,IAAI,CAAC2B,KAAN,CAFZ,IAGA3B,IAAI,CAAC0B,GAAL,CAAStB,IAAT,KAAkBJ,IAAI,CAAC2B,KAAL,CAAWvB,IAJ/B,EAKE;MACA;IACD;EACF;;EAED,KAAKE,SAAL;EACA,KAAKS,KAAL;EACA,KAAKP,KAAL,CAAWR,IAAI,CAAC2B,KAAhB,EAAuB3B,IAAvB;AACD;;AAEM,SAAS8B,eAAT,CAAwC9B,IAAxC,EAAiE;EACtE,MAAM+B,KAAK,GAAG/B,IAAI,CAACgC,QAAnB;EACA,MAAMC,GAAG,GAAGF,KAAK,CAACjB,MAAlB;EAEA,KAAKR,SAAL;EACA,KAAKO,kBAAL,CAAwBb,IAAxB;;EAEA,KAAK,IAAIkC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,KAAK,CAACjB,MAA1B,EAAkCoB,CAAC,EAAnC,EAAuC;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAD,CAAlB;;IACA,IAAIC,IAAJ,EAAU;MACR,IAAID,CAAC,GAAG,CAAR,EAAW,KAAKnB,KAAL;MACX,KAAKP,KAAL,CAAW2B,IAAX,EAAiBnC,IAAjB;MACA,IAAIkC,CAAC,GAAGD,GAAG,GAAG,CAAd,EAAiB,KAAK3B,SAAL;IAClB,CAJD,MAIO;MAML,KAAKA,SAAL;IACD;EACF;;EAED,KAAKA,SAAL;AACD;;AAIM,SAAS8B,gBAAT,CAAyCpC,IAAzC,EAAmE;EACxE,MAAMW,KAAK,GAAGX,IAAI,CAACY,UAAnB;EAEA,IAAIyB,UAAJ;EACA,IAAIC,QAAJ;;EACA,IAAI,KAAKC,MAAL,CAAYC,wBAAZ,KAAyC,KAA7C,EAAoD;IAClDH,UAAU,GAAG,IAAb;IACAC,QAAQ,GAAG,IAAX;EACD,CAHD,MAGO,IACL,KAAKC,MAAL,CAAYC,wBAAZ,KAAyC,MAAzC,IACA,KAAKD,MAAL,CAAYC,wBAAZ,IAAwC,IAFnC,EAGL;IACA,MAAM,IAAIC,KAAJ,CACH,4EAA2EC,IAAI,CAACC,SAAL,CAC1E,KAAKJ,MAAL,CAAYC,wBAD8D,CAE1E,aAHE,CAAN;EAKD,CATM,MASA;IACLH,UAAU,GAAG,IAAb;IACAC,QAAQ,GAAG,GAAX;EACD;;EAED,KAAKhC,KAAL,CAAW+B,UAAX;EACA,KAAKxB,kBAAL,CAAwBb,IAAxB;;EAEA,IAAIW,KAAK,CAACG,MAAV,EAAkB;IAChB,KAAKC,KAAL;IACA,KAAKC,SAAL,CAAeL,KAAf,EAAsBX,IAAtB,EAA4B;MAAEiB,MAAM,EAAE,IAAV;MAAgBC,SAAS,EAAE;IAA3B,CAA5B;IACA,KAAKH,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWgC,QAAX;AACD;;AAEM,SAASM,eAAT,CAAwC5C,IAAxC,EAAiE;EACtE,MAAM+B,KAAK,GAAG/B,IAAI,CAACgC,QAAnB;EACA,MAAMC,GAAG,GAAGF,KAAK,CAACjB,MAAlB;EAEA,IAAIuB,UAAJ;EACA,IAAIC,QAAJ;;EACA,IAAI,KAAKC,MAAL,CAAYC,wBAAZ,KAAyC,KAA7C,EAAoD;IAClDH,UAAU,GAAG,IAAb;IACAC,QAAQ,GAAG,IAAX;EACD,CAHD,MAGO,IAAI,KAAKC,MAAL,CAAYC,wBAAZ,KAAyC,MAA7C,EAAqD;IAC1DH,UAAU,GAAG,IAAb;IACAC,QAAQ,GAAG,GAAX;EACD,CAHM,MAGA;IACL,MAAM,IAAIG,KAAJ,CACH,GAAE,KAAKF,MAAL,CAAYC,wBAAyB,4CADpC,CAAN;EAGD;;EAED,KAAKlC,KAAL,CAAW+B,UAAX;EACA,KAAKxB,kBAAL,CAAwBb,IAAxB;;EAEA,KAAK,IAAIkC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,KAAK,CAACjB,MAA1B,EAAkCoB,CAAC,EAAnC,EAAuC;IACrC,MAAMC,IAAI,GAAGJ,KAAK,CAACG,CAAD,CAAlB;;IACA,IAAIC,IAAJ,EAAU;MACR,IAAID,CAAC,GAAG,CAAR,EAAW,KAAKnB,KAAL;MACX,KAAKP,KAAL,CAAW2B,IAAX,EAAiBnC,IAAjB;MACA,IAAIkC,CAAC,GAAGD,GAAG,GAAG,CAAd,EAAiB,KAAK3B,SAAL;IAClB;EACF;;EAED,KAAKA,KAAL,CAAWgC,QAAX;AACD;;AAEM,SAASO,aAAT,CAAsC7C,IAAtC,EAA6D;EAClE,KAAKG,IAAL,CAAW,IAAGH,IAAI,CAAC8C,OAAQ,IAAG9C,IAAI,CAAC+C,KAAM,EAAzC;AACD;;AAEM,SAASC,cAAT,CAAuChD,IAAvC,EAA+D;EACpE,KAAKG,IAAL,CAAUH,IAAI,CAAC2B,KAAL,GAAa,MAAb,GAAsB,OAAhC;AACD;;AAEM,SAASsB,WAAT,GAAoC;EACzC,KAAK9C,IAAL,CAAU,MAAV;AACD;;AAEM,SAAS+C,cAAT,CAAuClD,IAAvC,EAA+D;EACpE,MAAMmD,GAAG,GAAG,KAAKC,cAAL,CAAoBpD,IAApB,CAAZ;EACA,MAAMqD,IAAI,GAAG,KAAKd,MAAL,CAAYe,WAAzB;EACA,MAAM3B,KAAK,GAAG3B,IAAI,CAAC2B,KAAL,GAAa,EAA3B;;EACA,IAAI0B,IAAI,CAACE,OAAT,EAAkB;IAChB,KAAKC,MAAL,CAAYC,MAAK,CAACzD,IAAI,CAAC2B,KAAN,EAAa0B,IAAb,CAAjB;EACD,CAFD,MAEO,IAAIF,GAAG,IAAI,IAAX,EAAiB;IACtB,KAAKK,MAAL,CAAY7B,KAAZ;EACD,CAFM,MAEA,IAAI,KAAKY,MAAL,CAAYmB,QAAhB,EAA0B;IAC/B,KAAKF,MAAL,CAAYL,GAAG,CAACrC,MAAJ,GAAaa,KAAK,CAACb,MAAnB,GAA4BqC,GAA5B,GAAkCxB,KAA9C;EACD,CAFM,MAEA;IACL,KAAK6B,MAAL,CAAYL,GAAZ;EACD;AACF;;AAEM,SAASQ,aAAT,CAAsC3D,IAAtC,EAA6D;EAClE,MAAMmD,GAAG,GAAG,KAAKC,cAAL,CAAoBpD,IAApB,CAAZ;;EACA,IAAI,CAAC,KAAKuC,MAAL,CAAYmB,QAAb,IAAyBP,GAAG,KAAKS,SAArC,EAAgD;IAC9C,KAAKtD,KAAL,CAAW6C,GAAX;IACA;EACD;;EAED,MAAMU,GAAG,GAAGJ,MAAK,CACfzD,IAAI,CAAC2B,KADU,EAIXmC,MAAM,CAACC,MAAP,CACE,KAAKxB,MAAL,CAAYe,WADd,EAEE,KAAKf,MAAL,CAAYyB,qBAAZ,IAAqC;IAAEC,IAAI,EAAE;EAAR,CAFvC,CAJW,CAAjB;;EAUA,OAAO,KAAK3D,KAAL,CAAWuD,GAAX,CAAP;AACD;;AAEM,SAASK,aAAT,CAAsClE,IAAtC,EAA6D;EAClE,MAAMmD,GAAG,GAAG,KAAKC,cAAL,CAAoBpD,IAApB,CAAZ;;EACA,IAAI,CAAC,KAAKuC,MAAL,CAAYmB,QAAb,IAAyBP,GAAG,KAAKS,SAArC,EAAgD;IAC9C,KAAKzD,IAAL,CAAUgD,GAAV;IACA;EACD;;EACD,KAAKhD,IAAL,CAAUH,IAAI,CAAC2B,KAAL,GAAa,GAAvB;AACD;;AAEM,SAASwC,cAAT,CAAuCnE,IAAvC,EAA+D;EACpE,MAAMmD,GAAG,GAAG,KAAKC,cAAL,CAAoBpD,IAApB,CAAZ;;EACA,IAAI,CAAC,KAAKuC,MAAL,CAAYmB,QAAb,IAAyBP,GAAG,KAAKS,SAArC,EAAgD;IAC9C,KAAKzD,IAAL,CAAUgD,GAAV;IACA;EACD;;EACD,KAAKhD,IAAL,CAAUH,IAAI,CAAC2B,KAAL,GAAa,GAAvB;AACD;;AAGD,MAAMyC,kBAAkB,GAAG,IAAIC,GAAJ,CAAQ,CAAC,IAAD,EAAO,IAAP,EAAa,GAAb,EAAkB,GAAlB,EAAuB,GAAvB,CAAR,CAA3B;;AACO,SAASC,cAAT,GAAuC;EAC5C,MAAM;IAAEC;EAAF,IAAiB,KAAKhC,MAA5B;;EAEA,IAAI6B,kBAAkB,CAACI,GAAnB,CAAuBD,UAAvB,CAAJ,EAAwC;IACtC,KAAKjE,KAAL,CAAWiE,UAAX;EACD,CAFD,MAEO;IACL,MAAME,mBAAmB,GAAG/B,IAAI,CAACC,SAAL,CAAe4B,UAAf,CAA5B;IACA,MAAMG,WAAW,GAAGC,KAAK,CAACC,IAAN,CAAWR,kBAAX,EAA+BS,CAAC,IAAInC,IAAI,CAACC,SAAL,CAAekC,CAAf,CAApC,CAApB;IACA,MAAM,IAAIpC,KAAJ,CACH,mDAAD,GACG,GAAEiC,WAAW,CAACI,IAAZ,CAAiB,IAAjB,CAAuB,KAAIL,mBAAoB,qBAFhD,CAAN;EAID;AACF;;AAGM,SAASM,uBAAT,CAEL/E,IAFK,EAGL;EACA,KAAKQ,KAAL,CAAWR,IAAI,CAACgF,UAAhB,EAA4BhF,IAA5B;AACD;;AAEM,SAASiF,oBAAT,CAELjF,IAFK,EAGL;EACA,KAAKQ,KAAL,CAAWR,IAAI,CAACkF,MAAhB,EAAwBlF,IAAxB;AACD;;AAEM,SAASmF,6BAAT,GAAsD;EAC3D,KAAK7E,SAAL;AACD"}