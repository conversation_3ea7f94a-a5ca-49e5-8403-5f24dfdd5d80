{"version": 3, "sources": ["../../monaco-editor/esm/vs/language/typescript/monaco.contribution.js"], "sourcesContent": ["import '../../editor/editor.api.js';\n/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\n\n// src/language/typescript/lib/typescriptServicesMetadata.ts\nvar typescriptVersion = \"5.0.2\";\n\n// src/fillers/monaco-editor-core.ts\nvar monaco_editor_core_exports = {};\n__reExport(monaco_editor_core_exports, monaco_editor_core_star);\nimport * as monaco_editor_core_star from \"../../editor/editor.api.js\";\n\n// src/language/typescript/monaco.contribution.ts\nvar ModuleKind = /* @__PURE__ */ ((ModuleKind2) => {\n  ModuleKind2[ModuleKind2[\"None\"] = 0] = \"None\";\n  ModuleKind2[ModuleKind2[\"CommonJS\"] = 1] = \"CommonJS\";\n  ModuleKind2[ModuleKind2[\"AMD\"] = 2] = \"AMD\";\n  ModuleKind2[ModuleKind2[\"UMD\"] = 3] = \"UMD\";\n  ModuleKind2[ModuleKind2[\"System\"] = 4] = \"System\";\n  ModuleKind2[ModuleKind2[\"ES2015\"] = 5] = \"ES2015\";\n  ModuleKind2[ModuleKind2[\"ESNext\"] = 99] = \"ESNext\";\n  return ModuleKind2;\n})(ModuleKind || {});\nvar JsxEmit = /* @__PURE__ */ ((JsxEmit2) => {\n  JsxEmit2[JsxEmit2[\"None\"] = 0] = \"None\";\n  JsxEmit2[JsxEmit2[\"Preserve\"] = 1] = \"Preserve\";\n  JsxEmit2[JsxEmit2[\"React\"] = 2] = \"React\";\n  JsxEmit2[JsxEmit2[\"ReactNative\"] = 3] = \"ReactNative\";\n  JsxEmit2[JsxEmit2[\"ReactJSX\"] = 4] = \"ReactJSX\";\n  JsxEmit2[JsxEmit2[\"ReactJSXDev\"] = 5] = \"ReactJSXDev\";\n  return JsxEmit2;\n})(JsxEmit || {});\nvar NewLineKind = /* @__PURE__ */ ((NewLineKind2) => {\n  NewLineKind2[NewLineKind2[\"CarriageReturnLineFeed\"] = 0] = \"CarriageReturnLineFeed\";\n  NewLineKind2[NewLineKind2[\"LineFeed\"] = 1] = \"LineFeed\";\n  return NewLineKind2;\n})(NewLineKind || {});\nvar ScriptTarget = /* @__PURE__ */ ((ScriptTarget2) => {\n  ScriptTarget2[ScriptTarget2[\"ES3\"] = 0] = \"ES3\";\n  ScriptTarget2[ScriptTarget2[\"ES5\"] = 1] = \"ES5\";\n  ScriptTarget2[ScriptTarget2[\"ES2015\"] = 2] = \"ES2015\";\n  ScriptTarget2[ScriptTarget2[\"ES2016\"] = 3] = \"ES2016\";\n  ScriptTarget2[ScriptTarget2[\"ES2017\"] = 4] = \"ES2017\";\n  ScriptTarget2[ScriptTarget2[\"ES2018\"] = 5] = \"ES2018\";\n  ScriptTarget2[ScriptTarget2[\"ES2019\"] = 6] = \"ES2019\";\n  ScriptTarget2[ScriptTarget2[\"ES2020\"] = 7] = \"ES2020\";\n  ScriptTarget2[ScriptTarget2[\"ESNext\"] = 99] = \"ESNext\";\n  ScriptTarget2[ScriptTarget2[\"JSON\"] = 100] = \"JSON\";\n  ScriptTarget2[ScriptTarget2[\"Latest\"] = 99 /* ESNext */] = \"Latest\";\n  return ScriptTarget2;\n})(ScriptTarget || {});\nvar ModuleResolutionKind = /* @__PURE__ */ ((ModuleResolutionKind2) => {\n  ModuleResolutionKind2[ModuleResolutionKind2[\"Classic\"] = 1] = \"Classic\";\n  ModuleResolutionKind2[ModuleResolutionKind2[\"NodeJs\"] = 2] = \"NodeJs\";\n  return ModuleResolutionKind2;\n})(ModuleResolutionKind || {});\nvar LanguageServiceDefaultsImpl = class {\n  _onDidChange = new monaco_editor_core_exports.Emitter();\n  _onDidExtraLibsChange = new monaco_editor_core_exports.Emitter();\n  _extraLibs;\n  _removedExtraLibs;\n  _eagerModelSync;\n  _compilerOptions;\n  _diagnosticsOptions;\n  _workerOptions;\n  _onDidExtraLibsChangeTimeout;\n  _inlayHintsOptions;\n  _modeConfiguration;\n  constructor(compilerOptions, diagnosticsOptions, workerOptions, inlayHintsOptions, modeConfiguration) {\n    this._extraLibs = /* @__PURE__ */ Object.create(null);\n    this._removedExtraLibs = /* @__PURE__ */ Object.create(null);\n    this._eagerModelSync = false;\n    this.setCompilerOptions(compilerOptions);\n    this.setDiagnosticsOptions(diagnosticsOptions);\n    this.setWorkerOptions(workerOptions);\n    this.setInlayHintsOptions(inlayHintsOptions);\n    this.setModeConfiguration(modeConfiguration);\n    this._onDidExtraLibsChangeTimeout = -1;\n  }\n  get onDidChange() {\n    return this._onDidChange.event;\n  }\n  get onDidExtraLibsChange() {\n    return this._onDidExtraLibsChange.event;\n  }\n  get modeConfiguration() {\n    return this._modeConfiguration;\n  }\n  get workerOptions() {\n    return this._workerOptions;\n  }\n  get inlayHintsOptions() {\n    return this._inlayHintsOptions;\n  }\n  getExtraLibs() {\n    return this._extraLibs;\n  }\n  addExtraLib(content, _filePath) {\n    let filePath;\n    if (typeof _filePath === \"undefined\") {\n      filePath = `ts:extralib-${Math.random().toString(36).substring(2, 15)}`;\n    } else {\n      filePath = _filePath;\n    }\n    if (this._extraLibs[filePath] && this._extraLibs[filePath].content === content) {\n      return {\n        dispose: () => {\n        }\n      };\n    }\n    let myVersion = 1;\n    if (this._removedExtraLibs[filePath]) {\n      myVersion = this._removedExtraLibs[filePath] + 1;\n    }\n    if (this._extraLibs[filePath]) {\n      myVersion = this._extraLibs[filePath].version + 1;\n    }\n    this._extraLibs[filePath] = {\n      content,\n      version: myVersion\n    };\n    this._fireOnDidExtraLibsChangeSoon();\n    return {\n      dispose: () => {\n        let extraLib = this._extraLibs[filePath];\n        if (!extraLib) {\n          return;\n        }\n        if (extraLib.version !== myVersion) {\n          return;\n        }\n        delete this._extraLibs[filePath];\n        this._removedExtraLibs[filePath] = myVersion;\n        this._fireOnDidExtraLibsChangeSoon();\n      }\n    };\n  }\n  setExtraLibs(libs) {\n    for (const filePath in this._extraLibs) {\n      this._removedExtraLibs[filePath] = this._extraLibs[filePath].version;\n    }\n    this._extraLibs = /* @__PURE__ */ Object.create(null);\n    if (libs && libs.length > 0) {\n      for (const lib of libs) {\n        const filePath = lib.filePath || `ts:extralib-${Math.random().toString(36).substring(2, 15)}`;\n        const content = lib.content;\n        let myVersion = 1;\n        if (this._removedExtraLibs[filePath]) {\n          myVersion = this._removedExtraLibs[filePath] + 1;\n        }\n        this._extraLibs[filePath] = {\n          content,\n          version: myVersion\n        };\n      }\n    }\n    this._fireOnDidExtraLibsChangeSoon();\n  }\n  _fireOnDidExtraLibsChangeSoon() {\n    if (this._onDidExtraLibsChangeTimeout !== -1) {\n      return;\n    }\n    this._onDidExtraLibsChangeTimeout = window.setTimeout(() => {\n      this._onDidExtraLibsChangeTimeout = -1;\n      this._onDidExtraLibsChange.fire(void 0);\n    }, 0);\n  }\n  getCompilerOptions() {\n    return this._compilerOptions;\n  }\n  setCompilerOptions(options) {\n    this._compilerOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  getDiagnosticsOptions() {\n    return this._diagnosticsOptions;\n  }\n  setDiagnosticsOptions(options) {\n    this._diagnosticsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setWorkerOptions(options) {\n    this._workerOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setInlayHintsOptions(options) {\n    this._inlayHintsOptions = options || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n  setMaximumWorkerIdleTime(value) {\n  }\n  setEagerModelSync(value) {\n    this._eagerModelSync = value;\n  }\n  getEagerModelSync() {\n    return this._eagerModelSync;\n  }\n  setModeConfiguration(modeConfiguration) {\n    this._modeConfiguration = modeConfiguration || /* @__PURE__ */ Object.create(null);\n    this._onDidChange.fire(void 0);\n  }\n};\nvar typescriptVersion2 = typescriptVersion;\nvar modeConfigurationDefault = {\n  completionItems: true,\n  hovers: true,\n  documentSymbols: true,\n  definitions: true,\n  references: true,\n  documentHighlights: true,\n  rename: true,\n  diagnostics: true,\n  documentRangeFormattingEdits: true,\n  signatureHelp: true,\n  onTypeFormattingEdits: true,\n  codeActions: true,\n  inlayHints: true\n};\nvar typescriptDefaults = new LanguageServiceDefaultsImpl({ allowNonTsExtensions: true, target: 99 /* Latest */ }, { noSemanticValidation: false, noSyntaxValidation: false, onlyVisible: false }, {}, {}, modeConfigurationDefault);\nvar javascriptDefaults = new LanguageServiceDefaultsImpl({ allowNonTsExtensions: true, allowJs: true, target: 99 /* Latest */ }, { noSemanticValidation: true, noSyntaxValidation: false, onlyVisible: false }, {}, {}, modeConfigurationDefault);\nvar getTypeScriptWorker = () => {\n  return getMode().then((mode) => mode.getTypeScriptWorker());\n};\nvar getJavaScriptWorker = () => {\n  return getMode().then((mode) => mode.getJavaScriptWorker());\n};\nmonaco_editor_core_exports.languages.typescript = {\n  ModuleKind,\n  JsxEmit,\n  NewLineKind,\n  ScriptTarget,\n  ModuleResolutionKind,\n  typescriptVersion: typescriptVersion2,\n  typescriptDefaults,\n  javascriptDefaults,\n  getTypeScriptWorker,\n  getJavaScriptWorker\n};\nfunction getMode() {\n  if (false) {\n    return new Promise((resolve, reject) => {\n      __require([\"vs/language/typescript/tsMode\"], resolve, reject);\n    });\n  } else {\n    return import(\"./tsMode.js\");\n  }\n}\nmonaco_editor_core_exports.languages.onLanguage(\"typescript\", () => {\n  return getMode().then((mode) => mode.setupTypeScript(typescriptDefaults));\n});\nmonaco_editor_core_exports.languages.onLanguage(\"javascript\", () => {\n  return getMode().then((mode) => mode.setupJavaScript(javascriptDefaults));\n});\nexport {\n  JsxEmit,\n  ModuleKind,\n  ModuleResolutionKind,\n  NewLineKind,\n  ScriptTarget,\n  getJavaScriptWorker,\n  getTypeScriptWorker,\n  javascriptDefaults,\n  typescriptDefaults,\n  typescriptVersion2 as typescriptVersion\n};\n"], "mappings": ";;;;;;;;AAQA,IAAI,YAAY,OAAO;AACvB,IAAI,mBAAmB,OAAO;AAC9B,IAAI,oBAAoB,OAAO;AAC/B,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,MAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,aAAS,OAAO,kBAAkB,IAAI;AACpC,UAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,kBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,MAAM,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,EACvH;AACA,SAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,kBAAkB,YAAY,QAAQ,KAAK,SAAS,GAAG,gBAAgB,YAAY,cAAc,KAAK,SAAS;AAG9I,IAAI,oBAAoB;AAGxB,IAAI,6BAA6B,CAAC;AAClC,WAAW,4BAA4B,kBAAuB;AAI9D,IAAI,cAA8B,CAAC,gBAAgB;AACjD,cAAY,YAAY,UAAU,KAAK;AACvC,cAAY,YAAY,cAAc,KAAK;AAC3C,cAAY,YAAY,SAAS,KAAK;AACtC,cAAY,YAAY,SAAS,KAAK;AACtC,cAAY,YAAY,YAAY,KAAK;AACzC,cAAY,YAAY,YAAY,KAAK;AACzC,cAAY,YAAY,YAAY,MAAM;AAC1C,SAAO;AACT,GAAG,cAAc,CAAC,CAAC;AACnB,IAAI,WAA2B,CAAC,aAAa;AAC3C,WAAS,SAAS,UAAU,KAAK;AACjC,WAAS,SAAS,cAAc,KAAK;AACrC,WAAS,SAAS,WAAW,KAAK;AAClC,WAAS,SAAS,iBAAiB,KAAK;AACxC,WAAS,SAAS,cAAc,KAAK;AACrC,WAAS,SAAS,iBAAiB,KAAK;AACxC,SAAO;AACT,GAAG,WAAW,CAAC,CAAC;AAChB,IAAI,eAA+B,CAAC,iBAAiB;AACnD,eAAa,aAAa,4BAA4B,KAAK;AAC3D,eAAa,aAAa,cAAc,KAAK;AAC7C,SAAO;AACT,GAAG,eAAe,CAAC,CAAC;AACpB,IAAI,gBAAgC,CAAC,kBAAkB;AACrD,gBAAc,cAAc,SAAS,KAAK;AAC1C,gBAAc,cAAc,SAAS,KAAK;AAC1C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,KAAK;AAC7C,gBAAc,cAAc,YAAY,MAAM;AAC9C,gBAAc,cAAc,UAAU,OAAO;AAC7C,gBAAc,cAAc,YAAY,MAAmB;AAC3D,SAAO;AACT,GAAG,gBAAgB,CAAC,CAAC;AACrB,IAAI,wBAAwC,CAAC,0BAA0B;AACrE,wBAAsB,sBAAsB,aAAa,KAAK;AAC9D,wBAAsB,sBAAsB,YAAY,KAAK;AAC7D,SAAO;AACT,GAAG,wBAAwB,CAAC,CAAC;AAC7B,IAAI,8BAA8B,MAAM;AAAA,EAYtC,YAAY,iBAAiB,oBAAoB,eAAe,mBAAmB,mBAAmB;AAXtG,wCAAe,IAAI,2BAA2B,QAAQ;AACtD,iDAAwB,IAAI,2BAA2B,QAAQ;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEE,SAAK,aAA6B,uBAAO,OAAO,IAAI;AACpD,SAAK,oBAAoC,uBAAO,OAAO,IAAI;AAC3D,SAAK,kBAAkB;AACvB,SAAK,mBAAmB,eAAe;AACvC,SAAK,sBAAsB,kBAAkB;AAC7C,SAAK,iBAAiB,aAAa;AACnC,SAAK,qBAAqB,iBAAiB;AAC3C,SAAK,qBAAqB,iBAAiB;AAC3C,SAAK,+BAA+B;AAAA,EACtC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EACA,IAAI,uBAAuB;AACzB,WAAO,KAAK,sBAAsB;AAAA,EACpC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,SAAS,WAAW;AAC9B,QAAI;AACJ,QAAI,OAAO,cAAc,aAAa;AACpC,iBAAW,eAAe,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,IACtE,OAAO;AACL,iBAAW;AAAA,IACb;AACA,QAAI,KAAK,WAAW,aAAa,KAAK,WAAW,UAAU,YAAY,SAAS;AAC9E,aAAO;AAAA,QACL,SAAS,MAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY;AAChB,QAAI,KAAK,kBAAkB,WAAW;AACpC,kBAAY,KAAK,kBAAkB,YAAY;AAAA,IACjD;AACA,QAAI,KAAK,WAAW,WAAW;AAC7B,kBAAY,KAAK,WAAW,UAAU,UAAU;AAAA,IAClD;AACA,SAAK,WAAW,YAAY;AAAA,MAC1B;AAAA,MACA,SAAS;AAAA,IACX;AACA,SAAK,8BAA8B;AACnC,WAAO;AAAA,MACL,SAAS,MAAM;AACb,YAAI,WAAW,KAAK,WAAW;AAC/B,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AACA,YAAI,SAAS,YAAY,WAAW;AAClC;AAAA,QACF;AACA,eAAO,KAAK,WAAW;AACvB,aAAK,kBAAkB,YAAY;AACnC,aAAK,8BAA8B;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA,EACA,aAAa,MAAM;AACjB,eAAW,YAAY,KAAK,YAAY;AACtC,WAAK,kBAAkB,YAAY,KAAK,WAAW,UAAU;AAAA,IAC/D;AACA,SAAK,aAA6B,uBAAO,OAAO,IAAI;AACpD,QAAI,QAAQ,KAAK,SAAS,GAAG;AAC3B,iBAAW,OAAO,MAAM;AACtB,cAAM,WAAW,IAAI,YAAY,eAAe,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAC1F,cAAM,UAAU,IAAI;AACpB,YAAI,YAAY;AAChB,YAAI,KAAK,kBAAkB,WAAW;AACpC,sBAAY,KAAK,kBAAkB,YAAY;AAAA,QACjD;AACA,aAAK,WAAW,YAAY;AAAA,UAC1B;AAAA,UACA,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,SAAK,8BAA8B;AAAA,EACrC;AAAA,EACA,gCAAgC;AAC9B,QAAI,KAAK,iCAAiC,IAAI;AAC5C;AAAA,IACF;AACA,SAAK,+BAA+B,OAAO,WAAW,MAAM;AAC1D,WAAK,+BAA+B;AACpC,WAAK,sBAAsB,KAAK,MAAM;AAAA,IACxC,GAAG,CAAC;AAAA,EACN;AAAA,EACA,qBAAqB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,mBAAmB,SAAS;AAC1B,SAAK,mBAAmB,WAA2B,uBAAO,OAAO,IAAI;AACrE,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA,EACA,wBAAwB;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,sBAAsB,SAAS;AAC7B,SAAK,sBAAsB,WAA2B,uBAAO,OAAO,IAAI;AACxE,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA,EACA,iBAAiB,SAAS;AACxB,SAAK,iBAAiB,WAA2B,uBAAO,OAAO,IAAI;AACnE,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA,EACA,qBAAqB,SAAS;AAC5B,SAAK,qBAAqB,WAA2B,uBAAO,OAAO,IAAI;AACvE,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AAAA,EACA,yBAAyB,OAAO;AAAA,EAChC;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB,mBAAmB;AACtC,SAAK,qBAAqB,qBAAqC,uBAAO,OAAO,IAAI;AACjF,SAAK,aAAa,KAAK,MAAM;AAAA,EAC/B;AACF;AACA,IAAI,qBAAqB;AACzB,IAAI,2BAA2B;AAAA,EAC7B,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,oBAAoB;AAAA,EACpB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,8BAA8B;AAAA,EAC9B,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,aAAa;AAAA,EACb,YAAY;AACd;AACA,IAAI,qBAAqB,IAAI,4BAA4B,EAAE,sBAAsB,MAAM,QAAQ,GAAgB,GAAG,EAAE,sBAAsB,OAAO,oBAAoB,OAAO,aAAa,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,wBAAwB;AAClO,IAAI,qBAAqB,IAAI,4BAA4B,EAAE,sBAAsB,MAAM,SAAS,MAAM,QAAQ,GAAgB,GAAG,EAAE,sBAAsB,MAAM,oBAAoB,OAAO,aAAa,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,wBAAwB;AAChP,IAAI,sBAAsB,MAAM;AAC9B,SAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,oBAAoB,CAAC;AAC5D;AACA,IAAI,sBAAsB,MAAM;AAC9B,SAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,oBAAoB,CAAC;AAC5D;AACA,2BAA2B,UAAU,aAAa;AAAA,EAChD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,UAAU;AACjB,MAAI,OAAO;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,gBAAU,CAAC,+BAA+B,GAAG,SAAS,MAAM;AAAA,IAC9D,CAAC;AAAA,EACH,OAAO;AACL,WAAO,OAAO;AAAA,EAChB;AACF;AACA,2BAA2B,UAAU,WAAW,cAAc,MAAM;AAClE,SAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,gBAAgB,kBAAkB,CAAC;AAC1E,CAAC;AACD,2BAA2B,UAAU,WAAW,cAAc,MAAM;AAClE,SAAO,QAAQ,EAAE,KAAK,CAAC,SAAS,KAAK,gBAAgB,kBAAkB,CAAC;AAC1E,CAAC;", "names": []}