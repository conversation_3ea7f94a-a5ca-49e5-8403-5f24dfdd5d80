{"version": 3, "names": ["targetsSupported", "target", "support", "targetEnvironments", "Object", "keys", "length", "unsupportedEnvironments", "filter", "environment", "lowestImplementedVersion", "getLowestImplementedVersion", "lowestTargetedVersion", "isUnreleasedVersion", "semver", "valid", "toString", "Error", "gt", "semverify", "isRequired", "name", "targets", "compatData", "pluginsCompatData", "includes", "excludes", "has", "filterItems", "list", "defaultIncludes", "defaultExcludes", "pluginSyntaxMap", "result", "Set", "options", "item", "add", "shippedProposalsSyntax", "get", "for<PERSON>ach", "delete"], "sources": ["../src/filter-items.ts"], "sourcesContent": ["import semver from \"semver\";\n\nimport pluginsCompatData from \"@babel/compat-data/plugins\";\n\nimport type { Targets } from \"./types\";\nimport {\n  getLowestImplementedVersion,\n  isUnreleasedVersion,\n  semverify,\n} from \"./utils\";\n\nexport function targetsSupported(target: Targets, support: Targets) {\n  const targetEnvironments = Object.keys(target) as Array<keyof Targets>;\n\n  if (targetEnvironments.length === 0) {\n    return false;\n  }\n\n  const unsupportedEnvironments = targetEnvironments.filter(environment => {\n    const lowestImplementedVersion = getLowestImplementedVersion(\n      support,\n      environment,\n    );\n\n    // Feature is not implemented in that environment\n    if (!lowestImplementedVersion) {\n      return true;\n    }\n\n    const lowestTargetedVersion = target[environment];\n\n    // If targets has unreleased value as a lowest version, then don't require a plugin.\n    if (isUnreleasedVersion(lowestTargetedVersion, environment)) {\n      return false;\n    }\n\n    // Include plugin if it is supported in the unreleased environment, which wasn't specified in targets\n    if (isUnreleasedVersion(lowestImplementedVersion, environment)) {\n      return true;\n    }\n\n    if (!semver.valid(lowestTargetedVersion.toString())) {\n      throw new Error(\n        `Invalid version passed for target \"${environment}\": \"${lowestTargetedVersion}\". ` +\n          \"Versions must be in semver format (major.minor.patch)\",\n      );\n    }\n\n    return semver.gt(\n      semverify(lowestImplementedVersion),\n      lowestTargetedVersion.toString(),\n    );\n  });\n\n  return unsupportedEnvironments.length === 0;\n}\n\nexport function isRequired(\n  name: string,\n  targets: Targets,\n  {\n    compatData = pluginsCompatData,\n    includes,\n    excludes,\n  }: {\n    compatData?: { [feature: string]: Targets };\n    includes?: Set<string>;\n    excludes?: Set<string>;\n  } = {},\n) {\n  if (excludes?.has(name)) return false;\n  if (includes?.has(name)) return true;\n  return !targetsSupported(targets, compatData[name]);\n}\n\nexport default function filterItems(\n  list: { [feature: string]: Targets },\n  includes: Set<string>,\n  excludes: Set<string>,\n  targets: Targets,\n  defaultIncludes: Array<string> | null,\n  defaultExcludes?: Array<string> | null,\n  pluginSyntaxMap?: Map<string, string | null>,\n) {\n  const result = new Set<string>();\n  const options = { compatData: list, includes, excludes };\n\n  for (const item in list) {\n    if (isRequired(item, targets, options)) {\n      result.add(item);\n    } else if (pluginSyntaxMap) {\n      const shippedProposalsSyntax = pluginSyntaxMap.get(item);\n\n      if (shippedProposalsSyntax) {\n        result.add(shippedProposalsSyntax);\n      }\n    }\n  }\n\n  if (defaultIncludes) {\n    defaultIncludes.forEach(item => !excludes.has(item) && result.add(item));\n  }\n\n  if (defaultExcludes) {\n    defaultExcludes.forEach(item => !includes.has(item) && result.delete(item));\n  }\n\n  return result;\n}\n"], "mappings": ";;;;;;;;;AAAA;;AAEA;;AAGA;;AAMO,SAASA,gBAAT,CAA0BC,MAA1B,EAA2CC,OAA3C,EAA6D;EAClE,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,IAAP,CAAYJ,MAAZ,CAA3B;;EAEA,IAAIE,kBAAkB,CAACG,MAAnB,KAA8B,CAAlC,EAAqC;IACnC,OAAO,KAAP;EACD;;EAED,MAAMC,uBAAuB,GAAGJ,kBAAkB,CAACK,MAAnB,CAA0BC,WAAW,IAAI;IACvE,MAAMC,wBAAwB,GAAG,IAAAC,kCAAA,EAC/BT,OAD+B,EAE/BO,WAF+B,CAAjC;;IAMA,IAAI,CAACC,wBAAL,EAA+B;MAC7B,OAAO,IAAP;IACD;;IAED,MAAME,qBAAqB,GAAGX,MAAM,CAACQ,WAAD,CAApC;;IAGA,IAAI,IAAAI,0BAAA,EAAoBD,qBAApB,EAA2CH,WAA3C,CAAJ,EAA6D;MAC3D,OAAO,KAAP;IACD;;IAGD,IAAI,IAAAI,0BAAA,EAAoBH,wBAApB,EAA8CD,WAA9C,CAAJ,EAAgE;MAC9D,OAAO,IAAP;IACD;;IAED,IAAI,CAACK,OAAM,CAACC,KAAP,CAAaH,qBAAqB,CAACI,QAAtB,EAAb,CAAL,EAAqD;MACnD,MAAM,IAAIC,KAAJ,CACH,sCAAqCR,WAAY,OAAMG,qBAAsB,KAA9E,GACE,uDAFE,CAAN;IAID;;IAED,OAAOE,OAAM,CAACI,EAAP,CACL,IAAAC,gBAAA,EAAUT,wBAAV,CADK,EAELE,qBAAqB,CAACI,QAAtB,EAFK,CAAP;EAID,CAlC+B,CAAhC;EAoCA,OAAOT,uBAAuB,CAACD,MAAxB,KAAmC,CAA1C;AACD;;AAEM,SAASc,UAAT,CACLC,IADK,EAELC,OAFK,EAGL;EACEC,UAAU,GAAGC,QADf;EAEEC,QAFF;EAGEC;AAHF,IAQI,EAXC,EAYL;EACA,IAAIA,QAAJ,YAAIA,QAAQ,CAAEC,GAAV,CAAcN,IAAd,CAAJ,EAAyB,OAAO,KAAP;EACzB,IAAII,QAAJ,YAAIA,QAAQ,CAAEE,GAAV,CAAcN,IAAd,CAAJ,EAAyB,OAAO,IAAP;EACzB,OAAO,CAACrB,gBAAgB,CAACsB,OAAD,EAAUC,UAAU,CAACF,IAAD,CAApB,CAAxB;AACD;;AAEc,SAASO,WAAT,CACbC,IADa,EAEbJ,QAFa,EAGbC,QAHa,EAIbJ,OAJa,EAKbQ,eALa,EAMbC,eANa,EAObC,eAPa,EAQb;EACA,MAAMC,MAAM,GAAG,IAAIC,GAAJ,EAAf;EACA,MAAMC,OAAO,GAAG;IAAEZ,UAAU,EAAEM,IAAd;IAAoBJ,QAApB;IAA8BC;EAA9B,CAAhB;;EAEA,KAAK,MAAMU,IAAX,IAAmBP,IAAnB,EAAyB;IACvB,IAAIT,UAAU,CAACgB,IAAD,EAAOd,OAAP,EAAgBa,OAAhB,CAAd,EAAwC;MACtCF,MAAM,CAACI,GAAP,CAAWD,IAAX;IACD,CAFD,MAEO,IAAIJ,eAAJ,EAAqB;MAC1B,MAAMM,sBAAsB,GAAGN,eAAe,CAACO,GAAhB,CAAoBH,IAApB,CAA/B;;MAEA,IAAIE,sBAAJ,EAA4B;QAC1BL,MAAM,CAACI,GAAP,CAAWC,sBAAX;MACD;IACF;EACF;;EAED,IAAIR,eAAJ,EAAqB;IACnBA,eAAe,CAACU,OAAhB,CAAwBJ,IAAI,IAAI,CAACV,QAAQ,CAACC,GAAT,CAAaS,IAAb,CAAD,IAAuBH,MAAM,CAACI,GAAP,CAAWD,IAAX,CAAvD;EACD;;EAED,IAAIL,eAAJ,EAAqB;IACnBA,eAAe,CAACS,OAAhB,CAAwBJ,IAAI,IAAI,CAACX,QAAQ,CAACE,GAAT,CAAaS,IAAb,CAAD,IAAuBH,MAAM,CAACQ,MAAP,CAAcL,IAAd,CAAvD;EACD;;EAED,OAAOH,MAAP;AACD"}