{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/protobuf/protobuf.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/protobuf/protobuf.ts\nvar namedLiterals = [\"true\", \"false\"];\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\"] }\n  ],\n  autoCloseBefore: \".,=}])>' \\n\t\",\n  indentationRules: {\n    increaseIndentPattern: new RegExp(\"^((?!\\\\/\\\\/).)*(\\\\{[^}\\\"'`]*|\\\\([^)\\\"'`]*|\\\\[[^\\\\]\\\"'`]*)$\"),\n    decreaseIndentPattern: new RegExp(\"^((?!.*?\\\\/\\\\*).*\\\\*/)?\\\\s*[\\\\}\\\\]].*$\")\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".proto\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  symbols: /[=><!~?:&|+\\-*/^%]+/,\n  keywords: [\n    \"syntax\",\n    \"import\",\n    \"weak\",\n    \"public\",\n    \"package\",\n    \"option\",\n    \"repeated\",\n    \"oneof\",\n    \"map\",\n    \"reserved\",\n    \"to\",\n    \"max\",\n    \"enum\",\n    \"message\",\n    \"service\",\n    \"rpc\",\n    \"stream\",\n    \"returns\",\n    \"package\",\n    \"optional\",\n    \"true\",\n    \"false\"\n  ],\n  builtinTypes: [\n    \"double\",\n    \"float\",\n    \"int32\",\n    \"int64\",\n    \"uint32\",\n    \"uint64\",\n    \"sint32\",\n    \"sint64\",\n    \"fixed32\",\n    \"fixed64\",\n    \"sfixed32\",\n    \"sfixed64\",\n    \"bool\",\n    \"string\",\n    \"bytes\"\n  ],\n  operators: [\"=\", \"+\", \"-\"],\n  namedLiterals,\n  escapes: `\\\\\\\\(u{[0-9A-Fa-f]+}|n|r|t|\\\\\\\\|'|\\\\\\${)`,\n  identifier: /[a-zA-Z]\\w*/,\n  fullIdentifier: /@identifier(?:\\s*\\.\\s*@identifier)*/,\n  optionName: /(?:@identifier|\\(\\s*@fullIdentifier\\s*\\))(?:\\s*\\.\\s*@identifier)*/,\n  messageName: /@identifier/,\n  enumName: /@identifier/,\n  messageType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@messageName/,\n  enumType: /\\.?\\s*(?:@identifier\\s*\\.\\s*)*@enumName/,\n  floatLit: /[0-9]+\\s*\\.\\s*[0-9]*(?:@exponent)?|[0-9]+@exponent|\\.[0-9]+(?:@exponent)?/,\n  exponent: /[eE]\\s*[+-]?\\s*[0-9]+/,\n  boolLit: /true\\b|false\\b/,\n  decimalLit: /[1-9][0-9]*/,\n  octalLit: /0[0-7]*/,\n  hexLit: /0[xX][0-9a-fA-F]+/,\n  type: /double|float|int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string|bytes|@messageType|@enumType/,\n  keyType: /int32|int64|uint32|uint64|sint32|sint64|fixed32|fixed64|sfixed32|sfixed64|bool|string/,\n  tokenizer: {\n    root: [\n      { include: \"@whitespace\" },\n      [/syntax/, \"keyword\"],\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        /(\")(proto3)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto3\" }]\n      ],\n      [\n        /(\")(proto2)(\")/,\n        [\"string.quote\", \"string\", { token: \"string.quote\", switchTo: \"@topLevel.proto2\" }]\n      ],\n      [\n        /.*?/,\n        { token: \"\", switchTo: \"@topLevel.proto2\" }\n      ]\n    ],\n    topLevel: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/[;.]/, \"delimiter\"],\n      [\n        /@fullIdentifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            service: { token: \"keyword\", next: \"@serviceDecl.$S2\" },\n            extend: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@extendDecl.$S2\" }\n              }\n            },\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ]\n    ],\n    enumDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@enumBody.$S2\" }]\n    ],\n    enumBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/@identifier/, \"identifier\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    messageDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }]\n    ],\n    messageBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/=/, \"operators\"],\n      [/;/, \"delimiter\"],\n      [\n        \"(map)(s*)(<)\",\n        [\"keyword\", \"white\", { token: \"@brackets\", bracket: \"@open\", next: \"@map.$S2\" }]\n      ],\n      [\n        /@identifier/,\n        {\n          cases: {\n            option: { token: \"keyword\", next: \"@option.$S2\" },\n            enum: { token: \"keyword\", next: \"@enumDecl.$S2\" },\n            message: { token: \"keyword\", next: \"@messageDecl.$S2\" },\n            oneof: { token: \"keyword\", next: \"@oneofDecl.$S2\" },\n            extensions: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@reserved.$S2\" }\n              }\n            },\n            reserved: { token: \"keyword\", next: \"@reserved.$S2\" },\n            \"(?:repeated|optional)\": { token: \"keyword\", next: \"@field.$S2\" },\n            required: {\n              cases: {\n                \"$S2==proto2\": { token: \"keyword\", next: \"@field.$S2\" }\n              }\n            },\n            \"$S2==proto3\": { token: \"@rematch\", next: \"@field.$S2\" }\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    extendDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@extendBody.$S2\" }]\n    ],\n    extendBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(?:repeated|optional|required)/, \"keyword\", \"@field.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    options: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/\\]/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    option: [\n      { include: \"@whitespace\" },\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\", \"@pop\"]\n    ],\n    oneofDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@oneofBody.$S2\" }]\n    ],\n    oneofBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", \"delimiter\"]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    reserved: [\n      { include: \"@whitespace\" },\n      [/,/, \"delimiter\"],\n      [/;/, \"delimiter\", \"@pop\"],\n      { include: \"@constant\" },\n      [/to\\b|max\\b/, \"keyword\"]\n    ],\n    map: [\n      { include: \"@whitespace\" },\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/,/, \"delimiter\"],\n      [/>/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"identifier\" }]\n    ],\n    field: [\n      { include: \"@whitespace\" },\n      [\n        \"group\",\n        {\n          cases: {\n            \"$S2==proto2\": { token: \"keyword\", switchTo: \"@groupDecl.$S2\" }\n          }\n        }\n      ],\n      [/(@identifier)(\\s*)(=)/, [\"identifier\", \"white\", { token: \"delimiter\", next: \"@pop\" }]],\n      [\n        /@fullIdentifier|\\./,\n        {\n          cases: {\n            \"@builtinTypes\": \"keyword\",\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ]\n    ],\n    groupDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [\"=\", \"operator\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@messageBody.$S2\" }],\n      { include: \"@constant\" }\n    ],\n    type: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"type.identifier\", \"@pop\"],\n      [/./, \"delimiter\"]\n    ],\n    identifier: [{ include: \"@whitespace\" }, [/@identifier/, \"identifier\", \"@pop\"]],\n    serviceDecl: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@serviceBody.$S2\" }]\n    ],\n    serviceBody: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [/option\\b/, \"keyword\", \"@option.$S2\"],\n      [/rpc\\b/, \"keyword\", \"@rpc.$S2\"],\n      [/\\[/, { token: \"@brackets\", bracket: \"@open\", next: \"@options.$S2\" }],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    rpc: [\n      { include: \"@whitespace\" },\n      [/@identifier/, \"identifier\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@request.$S2\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@methodOptions.$S2\" }],\n      [/;/, \"delimiter\", \"@pop\"]\n    ],\n    request: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@returns.$S2\" }]\n    ],\n    returns: [\n      { include: \"@whitespace\" },\n      [/returns\\b/, \"keyword\"],\n      [/\\(/, { token: \"@brackets\", bracket: \"@open\", switchTo: \"@response.$S2\" }]\n    ],\n    response: [\n      { include: \"@whitespace\" },\n      [\n        /@messageType/,\n        {\n          cases: {\n            stream: { token: \"keyword\", next: \"@type.$S2\" },\n            \"@default\": \"type.identifier\"\n          }\n        }\n      ],\n      [/\\)/, { token: \"@brackets\", bracket: \"@close\", switchTo: \"@rpc.$S2\" }]\n    ],\n    methodOptions: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/;/, \"delimiter\"],\n      [\"option\", \"keyword\"],\n      [/@optionName/, \"annotation\"],\n      [/[()]/, \"annotation.brackets\"],\n      [/=/, \"operator\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    constant: [\n      [\"@boolLit\", \"keyword.constant\"],\n      [\"@hexLit\", \"number.hex\"],\n      [\"@octalLit\", \"number.octal\"],\n      [\"@decimalLit\", \"number\"],\n      [\"@floatLit\", \"number.float\"],\n      [/(\"([^\"\\\\]|\\\\.)*|'([^'\\\\]|\\\\.)*)$/, \"string.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringSingle\" }],\n      [/{/, { token: \"@brackets\", bracket: \"@open\", next: \"@prototext\" }],\n      [/identifier/, \"identifier\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    prototext: [\n      { include: \"@whitespace\" },\n      { include: \"@constant\" },\n      [/@identifier/, \"identifier\"],\n      [/[:;]/, \"delimiter\"],\n      [/}/, { token: \"@brackets\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,gBAAgB,CAAC,QAAQ,OAAO;AACpC,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AAAA,EACA,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,IAChB,uBAAuB,IAAI,OAAO,4DAA4D;AAAA,IAC9F,uBAAuB,IAAI,OAAO,wCAAwC;AAAA,EAC5E;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,KAAK,KAAK,GAAG;AAAA,EACzB;AAAA,EACA,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,UAAU;AAAA,EACV,aAAa;AAAA,EACb,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA,CAAC,gBAAgB,UAAU,EAAE,OAAO,gBAAgB,UAAU,mBAAmB,CAAC;AAAA,MACpF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,gBAAgB,UAAU,EAAE,OAAO,gBAAgB,UAAU,mBAAmB,CAAC;AAAA,MACpF;AAAA,MACA;AAAA,QACE;AAAA,QACA,EAAE,OAAO,IAAI,UAAU,mBAAmB;AAAA,MAC5C;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,QAAQ,WAAW;AAAA,MACpB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,WAAW,MAAM,cAAc;AAAA,YAChD,MAAM,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,YAChD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,YACtD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,YACtD,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,eAAe,EAAE,OAAO,WAAW,MAAM,kBAAkB;AAAA,cAC7D;AAAA,YACF;AAAA,YACA,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,UAAU;AAAA,MACR,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,iBAAiB;AAAA,MACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,gBAAgB,CAAC;AAAA,IAC3E;AAAA,IACA,UAAU;AAAA,MACR,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY,WAAW,aAAa;AAAA,MACrC,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,aAAa;AAAA,MACX,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,iBAAiB;AAAA,MACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,IAC9E;AAAA,IACA,aAAa;AAAA,MACX,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,WAAW;AAAA,MACjB;AAAA,QACE;AAAA,QACA,CAAC,WAAW,SAAS,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,WAAW,MAAM,cAAc;AAAA,YAChD,MAAM,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,YAChD,SAAS,EAAE,OAAO,WAAW,MAAM,mBAAmB;AAAA,YACtD,OAAO,EAAE,OAAO,WAAW,MAAM,iBAAiB;AAAA,YAClD,YAAY;AAAA,cACV,OAAO;AAAA,gBACL,eAAe,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,cAC3D;AAAA,YACF;AAAA,YACA,UAAU,EAAE,OAAO,WAAW,MAAM,gBAAgB;AAAA,YACpD,yBAAyB,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,YAChE,UAAU;AAAA,cACR,OAAO;AAAA,gBACL,eAAe,EAAE,OAAO,WAAW,MAAM,aAAa;AAAA,cACxD;AAAA,YACF;AAAA,YACA,eAAe,EAAE,OAAO,YAAY,MAAM,aAAa;AAAA,UACzD;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,YAAY;AAAA,MACV,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,iBAAiB;AAAA,MACjC,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,kBAAkB,CAAC;AAAA,IAC7E;AAAA,IACA,YAAY;AAAA,MACV,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,kCAAkC,WAAW,YAAY;AAAA,MAC1D,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,KAAK,UAAU;AAAA,MAChB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAChE;AAAA,IACA,QAAQ;AAAA,MACN,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,KAAK,YAAY,MAAM;AAAA,IAC1B;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,iBAAiB,CAAC;AAAA,IAC5E;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,yBAAyB,CAAC,cAAc,SAAS,WAAW,CAAC;AAAA,MAC9D;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,UAAU;AAAA,MACR,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,cAAc,SAAS;AAAA,IAC1B;AAAA,IACA,KAAK;AAAA,MACH,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,aAAa,CAAC;AAAA,IACzE;AAAA,IACA,OAAO;AAAA,MACL,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,eAAe,EAAE,OAAO,WAAW,UAAU,iBAAiB;AAAA,UAChE;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,yBAAyB,CAAC,cAAc,SAAS,EAAE,OAAO,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,MACvF;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,iBAAiB;AAAA,YACjB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,KAAK,UAAU;AAAA,MAChB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,MAC5E,EAAE,SAAS,YAAY;AAAA,IACzB;AAAA,IACA,MAAM;AAAA,MACJ,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,mBAAmB,MAAM;AAAA,MACzC,CAAC,KAAK,WAAW;AAAA,IACnB;AAAA,IACA,YAAY,CAAC,EAAE,SAAS,cAAc,GAAG,CAAC,eAAe,cAAc,MAAM,CAAC;AAAA,IAC9E,aAAa;AAAA,MACX,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,mBAAmB,CAAC;AAAA,IAC9E;AAAA,IACA,aAAa;AAAA,MACX,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,YAAY,WAAW,aAAa;AAAA,MACrC,CAAC,SAAS,WAAW,UAAU;AAAA,MAC/B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,eAAe,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,KAAK;AAAA,MACH,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,eAAe,CAAC;AAAA,MACzE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,qBAAqB,CAAC;AAAA,MAC1E,CAAC,KAAK,aAAa,MAAM;AAAA,IAC3B;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,WAAW,MAAM,YAAY;AAAA,YAC9C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,eAAe,CAAC;AAAA,IAC5E;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,aAAa,SAAS;AAAA,MACvB,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,SAAS,UAAU,gBAAgB,CAAC;AAAA,IAC5E;AAAA,IACA,UAAU;AAAA,MACR,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,WAAW,MAAM,YAAY;AAAA,YAC9C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,EAAE,OAAO,aAAa,SAAS,UAAU,UAAU,WAAW,CAAC;AAAA,IACxE;AAAA,IACA,eAAe;AAAA,MACb,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,KAAK,WAAW;AAAA,MACjB,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,KAAK,UAAU;AAAA,MAChB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,OAAO;AAAA,MAC3B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,UAAU;AAAA,MACR,CAAC,YAAY,kBAAkB;AAAA,MAC/B,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,oCAAoC,gBAAgB;AAAA,MACrD,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,gBAAgB,CAAC;AAAA,MACxE,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,MAClE,CAAC,cAAc,YAAY;AAAA,IAC7B;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,KAAK,EAAE,OAAO,aAAa,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAC/D;AAAA,EACF;AACF;", "names": []}