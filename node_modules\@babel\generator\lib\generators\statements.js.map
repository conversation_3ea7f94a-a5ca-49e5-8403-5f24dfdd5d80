{"version": 3, "names": ["isFor", "isForStatement", "isIfStatement", "isStatement", "WithStatement", "node", "word", "space", "token", "print", "object", "printBlock", "IfStatement", "test", "needsBlock", "alternate", "getLastStatement", "consequent", "newline", "indent", "printAndIndentOnComments", "dedent", "endsWith", "statement", "body", "ForStatement", "inForStatementInitCounter", "init", "update", "WhileStatement", "ForXStatement", "isForOf", "type", "await", "left", "right", "ForInStatement", "ForOfStatement", "DoWhileStatement", "semicolon", "printStatementAfterKeyword", "printer", "parent", "isLabel", "printTerminatorless", "BreakStatement", "label", "ContinueStatement", "ReturnStatement", "argument", "ThrowStatement", "LabeledStatement", "TryStatement", "block", "handlers", "handler", "finalizer", "CatchClause", "param", "typeAnnotation", "SwitchStatement", "discriminant", "printSequence", "cases", "addNewlines", "leading", "cas", "length", "SwitchCase", "DebuggerStatement", "variableDeclarationIndent", "i", "constDeclarationIndent", "VariableDeclaration", "declare", "kind", "hasInits", "declar", "declarations", "separator", "printList", "VariableDeclarator", "id", "definite"], "sources": ["../../src/generators/statements.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isFor,\n  isForStatement,\n  isIfStatement,\n  isStatement,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function WithStatement(this: Printer, node: t.WithStatement) {\n  this.word(\"with\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.object, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function IfStatement(this: Printer, node: t.IfStatement) {\n  this.word(\"if\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.space();\n\n  const needsBlock =\n    node.alternate && isIfStatement(getLastStatement(node.consequent));\n  if (needsBlock) {\n    this.token(\"{\");\n    this.newline();\n    this.indent();\n  }\n\n  this.printAndIndentOnComments(node.consequent, node);\n\n  if (needsBlock) {\n    this.dedent();\n    this.newline();\n    this.token(\"}\");\n  }\n\n  if (node.alternate) {\n    if (this.endsWith(charCodes.rightCurlyBrace)) this.space();\n    this.word(\"else\");\n    this.space();\n    this.printAndIndentOnComments(node.alternate, node);\n  }\n}\n\n// Recursively get the last statement.\nfunction getLastStatement(statement: t.Statement): t.Statement {\n  // @ts-expect-error: If statement.body is empty or not a Node, isStatement will return false\n  const { body } = statement;\n  if (isStatement(body) === false) {\n    return statement;\n  }\n\n  return getLastStatement(body);\n}\n\nexport function ForStatement(this: Printer, node: t.ForStatement) {\n  this.word(\"for\");\n  this.space();\n  this.token(\"(\");\n\n  this.inForStatementInitCounter++;\n  this.print(node.init, node);\n  this.inForStatementInitCounter--;\n  this.token(\";\");\n\n  if (node.test) {\n    this.space();\n    this.print(node.test, node);\n  }\n  this.token(\";\");\n\n  if (node.update) {\n    this.space();\n    this.print(node.update, node);\n  }\n\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport function WhileStatement(this: Printer, node: t.WhileStatement) {\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nfunction ForXStatement(this: Printer, node: t.ForXStatement) {\n  this.word(\"for\");\n  this.space();\n  const isForOf = node.type === \"ForOfStatement\";\n  if (isForOf && node.await) {\n    this.word(\"await\");\n    this.space();\n  }\n  this.token(\"(\");\n  this.print(node.left, node);\n  this.space();\n  this.word(isForOf ? \"of\" : \"in\");\n  this.space();\n  this.print(node.right, node);\n  this.token(\")\");\n  this.printBlock(node);\n}\n\nexport const ForInStatement = ForXStatement;\nexport const ForOfStatement = ForXStatement;\n\nexport function DoWhileStatement(this: Printer, node: t.DoWhileStatement) {\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n  this.space();\n  this.word(\"while\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.test, node);\n  this.token(\")\");\n  this.semicolon();\n}\n\nfunction printStatementAfterKeyword(\n  printer: Printer,\n  node: t.Node,\n  parent: t.Node,\n  isLabel: boolean,\n) {\n  if (node) {\n    printer.space();\n    printer.printTerminatorless(node, parent, isLabel);\n  }\n\n  printer.semicolon();\n}\n\nexport function BreakStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"break\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ContinueStatement(this: Printer, node: t.ContinueStatement) {\n  this.word(\"continue\");\n  printStatementAfterKeyword(this, node.label, node, true);\n}\n\nexport function ReturnStatement(this: Printer, node: t.ReturnStatement) {\n  this.word(\"return\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function ThrowStatement(this: Printer, node: t.ThrowStatement) {\n  this.word(\"throw\");\n  printStatementAfterKeyword(this, node.argument, node, false);\n}\n\nexport function LabeledStatement(this: Printer, node: t.LabeledStatement) {\n  this.print(node.label, node);\n  this.token(\":\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function TryStatement(this: Printer, node: t.TryStatement) {\n  this.word(\"try\");\n  this.space();\n  this.print(node.block, node);\n  this.space();\n\n  // Esprima bug puts the catch clause in a `handlers` array.\n  // see https://code.google.com/p/esprima/issues/detail?id=433\n  // We run into this from regenerator generated ast.\n  // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n  if (node.handlers) {\n    // @ts-expect-error todo(flow->ts) should ast node type be updated to support this?\n    this.print(node.handlers[0], node);\n  } else {\n    this.print(node.handler, node);\n  }\n\n  if (node.finalizer) {\n    this.space();\n    this.word(\"finally\");\n    this.space();\n    this.print(node.finalizer, node);\n  }\n}\n\nexport function CatchClause(this: Printer, node: t.CatchClause) {\n  this.word(\"catch\");\n  this.space();\n  if (node.param) {\n    this.token(\"(\");\n    this.print(node.param, node);\n    this.print(node.param.typeAnnotation, node);\n    this.token(\")\");\n    this.space();\n  }\n  this.print(node.body, node);\n}\n\nexport function SwitchStatement(this: Printer, node: t.SwitchStatement) {\n  this.word(\"switch\");\n  this.space();\n  this.token(\"(\");\n  this.print(node.discriminant, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"{\");\n\n  this.printSequence(node.cases, node, {\n    indent: true,\n    addNewlines(leading, cas) {\n      if (!leading && node.cases[node.cases.length - 1] === cas) return -1;\n    },\n  });\n\n  this.token(\"}\");\n}\n\nexport function SwitchCase(this: Printer, node: t.SwitchCase) {\n  if (node.test) {\n    this.word(\"case\");\n    this.space();\n    this.print(node.test, node);\n    this.token(\":\");\n  } else {\n    this.word(\"default\");\n    this.token(\":\");\n  }\n\n  if (node.consequent.length) {\n    this.newline();\n    this.printSequence(node.consequent, node, { indent: true });\n  }\n}\n\nexport function DebuggerStatement(this: Printer) {\n  this.word(\"debugger\");\n  this.semicolon();\n}\n\nfunction variableDeclarationIndent(this: Printer) {\n  // \"let \" or \"var \" indentation.\n  this.token(\",\");\n  this.newline();\n  if (this.endsWith(charCodes.lineFeed)) {\n    for (let i = 0; i < 4; i++) this.space(true);\n  }\n}\n\nfunction constDeclarationIndent(this: Printer) {\n  // \"const \" indentation.\n  this.token(\",\");\n  this.newline();\n  if (this.endsWith(charCodes.lineFeed)) {\n    for (let i = 0; i < 6; i++) this.space(true);\n  }\n}\n\nexport function VariableDeclaration(\n  this: Printer,\n  node: t.VariableDeclaration,\n  parent: t.Node,\n) {\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  this.word(node.kind);\n  this.space();\n\n  let hasInits = false;\n  // don't add whitespace to loop heads\n  if (!isFor(parent)) {\n    for (const declar of node.declarations) {\n      if (declar.init) {\n        // has an init so let's split it up over multiple lines\n        hasInits = true;\n      }\n    }\n  }\n\n  //\n  // use a pretty separator when we aren't in compact mode, have initializers and don't have retainLines on\n  // this will format declarations like:\n  //\n  //   let foo = \"bar\", bar = \"foo\";\n  //\n  // into\n  //\n  //   let foo = \"bar\",\n  //       bar = \"foo\";\n  //\n\n  let separator;\n  if (hasInits) {\n    separator =\n      node.kind === \"const\"\n        ? constDeclarationIndent\n        : variableDeclarationIndent;\n  }\n\n  //\n\n  this.printList(node.declarations, node, { separator });\n\n  if (isFor(parent)) {\n    // don't give semicolons to these nodes since they'll be inserted in the parent generator\n    if (isForStatement(parent)) {\n      if (parent.init === node) return;\n    } else {\n      if (parent.left === node) return;\n    }\n  }\n\n  this.semicolon();\n}\n\nexport function VariableDeclarator(this: Printer, node: t.VariableDeclarator) {\n  this.print(node.id, node);\n  if (node.definite) this.token(\"!\"); // TS\n  // @ts-expect-error todo(flow-ts) Property 'typeAnnotation' does not exist on type 'MemberExpression'.\n  this.print(node.id.typeAnnotation, node);\n  if (node.init) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.init, node);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AACA;;;EACEA,K;EACAC,c;EACAC,a;EACAC;;;AAKK,SAASC,aAAT,CAAsCC,IAAtC,EAA6D;EAClE,KAAKC,IAAL,CAAU,MAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAACK,MAAhB,EAAwBL,IAAxB;EACA,KAAKG,SAAL;EACA,KAAKG,UAAL,CAAgBN,IAAhB;AACD;;AAEM,SAASO,WAAT,CAAoCP,IAApC,EAAyD;EAC9D,KAAKC,IAAL,CAAU,IAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAACQ,IAAhB,EAAsBR,IAAtB;EACA,KAAKG,SAAL;EACA,KAAKD,KAAL;EAEA,MAAMO,UAAU,GACdT,IAAI,CAACU,SAAL,IAAkBb,aAAa,CAACc,gBAAgB,CAACX,IAAI,CAACY,UAAN,CAAjB,CADjC;;EAEA,IAAIH,UAAJ,EAAgB;IACd,KAAKN,SAAL;IACA,KAAKU,OAAL;IACA,KAAKC,MAAL;EACD;;EAED,KAAKC,wBAAL,CAA8Bf,IAAI,CAACY,UAAnC,EAA+CZ,IAA/C;;EAEA,IAAIS,UAAJ,EAAgB;IACd,KAAKO,MAAL;IACA,KAAKH,OAAL;IACA,KAAKV,SAAL;EACD;;EAED,IAAIH,IAAI,CAACU,SAAT,EAAoB;IAClB,IAAI,KAAKO,QAAL,KAAJ,EAA8C,KAAKf,KAAL;IAC9C,KAAKD,IAAL,CAAU,MAAV;IACA,KAAKC,KAAL;IACA,KAAKa,wBAAL,CAA8Bf,IAAI,CAACU,SAAnC,EAA8CV,IAA9C;EACD;AACF;;AAGD,SAASW,gBAAT,CAA0BO,SAA1B,EAA+D;EAE7D,MAAM;IAAEC;EAAF,IAAWD,SAAjB;;EACA,IAAIpB,WAAW,CAACqB,IAAD,CAAX,KAAsB,KAA1B,EAAiC;IAC/B,OAAOD,SAAP;EACD;;EAED,OAAOP,gBAAgB,CAACQ,IAAD,CAAvB;AACD;;AAEM,SAASC,YAAT,CAAqCpB,IAArC,EAA2D;EAChE,KAAKC,IAAL,CAAU,KAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EAEA,KAAKkB,yBAAL;EACA,KAAKjB,KAAL,CAAWJ,IAAI,CAACsB,IAAhB,EAAsBtB,IAAtB;EACA,KAAKqB,yBAAL;EACA,KAAKlB,SAAL;;EAEA,IAAIH,IAAI,CAACQ,IAAT,EAAe;IACb,KAAKN,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACQ,IAAhB,EAAsBR,IAAtB;EACD;;EACD,KAAKG,SAAL;;EAEA,IAAIH,IAAI,CAACuB,MAAT,EAAiB;IACf,KAAKrB,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACuB,MAAhB,EAAwBvB,IAAxB;EACD;;EAED,KAAKG,SAAL;EACA,KAAKG,UAAL,CAAgBN,IAAhB;AACD;;AAEM,SAASwB,cAAT,CAAuCxB,IAAvC,EAA+D;EACpE,KAAKC,IAAL,CAAU,OAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAACQ,IAAhB,EAAsBR,IAAtB;EACA,KAAKG,SAAL;EACA,KAAKG,UAAL,CAAgBN,IAAhB;AACD;;AAED,SAASyB,aAAT,CAAsCzB,IAAtC,EAA6D;EAC3D,KAAKC,IAAL,CAAU,KAAV;EACA,KAAKC,KAAL;EACA,MAAMwB,OAAO,GAAG1B,IAAI,CAAC2B,IAAL,KAAc,gBAA9B;;EACA,IAAID,OAAO,IAAI1B,IAAI,CAAC4B,KAApB,EAA2B;IACzB,KAAK3B,IAAL,CAAU,OAAV;IACA,KAAKC,KAAL;EACD;;EACD,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAAC6B,IAAhB,EAAsB7B,IAAtB;EACA,KAAKE,KAAL;EACA,KAAKD,IAAL,CAAUyB,OAAO,GAAG,IAAH,GAAU,IAA3B;EACA,KAAKxB,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAAC8B,KAAhB,EAAuB9B,IAAvB;EACA,KAAKG,SAAL;EACA,KAAKG,UAAL,CAAgBN,IAAhB;AACD;;AAEM,MAAM+B,cAAc,GAAGN,aAAvB;;AACA,MAAMO,cAAc,GAAGP,aAAvB;;;AAEA,SAASQ,gBAAT,CAAyCjC,IAAzC,EAAmE;EACxE,KAAKC,IAAL,CAAU,IAAV;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACmB,IAAhB,EAAsBnB,IAAtB;EACA,KAAKE,KAAL;EACA,KAAKD,IAAL,CAAU,OAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAACQ,IAAhB,EAAsBR,IAAtB;EACA,KAAKG,SAAL;EACA,KAAK+B,SAAL;AACD;;AAED,SAASC,0BAAT,CACEC,OADF,EAEEpC,IAFF,EAGEqC,MAHF,EAIEC,OAJF,EAKE;EACA,IAAItC,IAAJ,EAAU;IACRoC,OAAO,CAAClC,KAAR;IACAkC,OAAO,CAACG,mBAAR,CAA4BvC,IAA5B,EAAkCqC,MAAlC,EAA0CC,OAA1C;EACD;;EAEDF,OAAO,CAACF,SAAR;AACD;;AAEM,SAASM,cAAT,CAAuCxC,IAAvC,EAAkE;EACvE,KAAKC,IAAL,CAAU,OAAV;EACAkC,0BAA0B,CAAC,IAAD,EAAOnC,IAAI,CAACyC,KAAZ,EAAmBzC,IAAnB,EAAyB,IAAzB,CAA1B;AACD;;AAEM,SAAS0C,iBAAT,CAA0C1C,IAA1C,EAAqE;EAC1E,KAAKC,IAAL,CAAU,UAAV;EACAkC,0BAA0B,CAAC,IAAD,EAAOnC,IAAI,CAACyC,KAAZ,EAAmBzC,IAAnB,EAAyB,IAAzB,CAA1B;AACD;;AAEM,SAAS2C,eAAT,CAAwC3C,IAAxC,EAAiE;EACtE,KAAKC,IAAL,CAAU,QAAV;EACAkC,0BAA0B,CAAC,IAAD,EAAOnC,IAAI,CAAC4C,QAAZ,EAAsB5C,IAAtB,EAA4B,KAA5B,CAA1B;AACD;;AAEM,SAAS6C,cAAT,CAAuC7C,IAAvC,EAA+D;EACpE,KAAKC,IAAL,CAAU,OAAV;EACAkC,0BAA0B,CAAC,IAAD,EAAOnC,IAAI,CAAC4C,QAAZ,EAAsB5C,IAAtB,EAA4B,KAA5B,CAA1B;AACD;;AAEM,SAAS8C,gBAAT,CAAyC9C,IAAzC,EAAmE;EACxE,KAAKI,KAAL,CAAWJ,IAAI,CAACyC,KAAhB,EAAuBzC,IAAvB;EACA,KAAKG,SAAL;EACA,KAAKD,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACmB,IAAhB,EAAsBnB,IAAtB;AACD;;AAEM,SAAS+C,YAAT,CAAqC/C,IAArC,EAA2D;EAChE,KAAKC,IAAL,CAAU,KAAV;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACgD,KAAhB,EAAuBhD,IAAvB;EACA,KAAKE,KAAL;;EAMA,IAAIF,IAAI,CAACiD,QAAT,EAAmB;IAEjB,KAAK7C,KAAL,CAAWJ,IAAI,CAACiD,QAAL,CAAc,CAAd,CAAX,EAA6BjD,IAA7B;EACD,CAHD,MAGO;IACL,KAAKI,KAAL,CAAWJ,IAAI,CAACkD,OAAhB,EAAyBlD,IAAzB;EACD;;EAED,IAAIA,IAAI,CAACmD,SAAT,EAAoB;IAClB,KAAKjD,KAAL;IACA,KAAKD,IAAL,CAAU,SAAV;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACmD,SAAhB,EAA2BnD,IAA3B;EACD;AACF;;AAEM,SAASoD,WAAT,CAAoCpD,IAApC,EAAyD;EAC9D,KAAKC,IAAL,CAAU,OAAV;EACA,KAAKC,KAAL;;EACA,IAAIF,IAAI,CAACqD,KAAT,EAAgB;IACd,KAAKlD,SAAL;IACA,KAAKC,KAAL,CAAWJ,IAAI,CAACqD,KAAhB,EAAuBrD,IAAvB;IACA,KAAKI,KAAL,CAAWJ,IAAI,CAACqD,KAAL,CAAWC,cAAtB,EAAsCtD,IAAtC;IACA,KAAKG,SAAL;IACA,KAAKD,KAAL;EACD;;EACD,KAAKE,KAAL,CAAWJ,IAAI,CAACmB,IAAhB,EAAsBnB,IAAtB;AACD;;AAEM,SAASuD,eAAT,CAAwCvD,IAAxC,EAAiE;EACtE,KAAKC,IAAL,CAAU,QAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL,CAAWJ,IAAI,CAACwD,YAAhB,EAA8BxD,IAA9B;EACA,KAAKG,SAAL;EACA,KAAKD,KAAL;EACA,KAAKC,SAAL;EAEA,KAAKsD,aAAL,CAAmBzD,IAAI,CAAC0D,KAAxB,EAA+B1D,IAA/B,EAAqC;IACnCc,MAAM,EAAE,IAD2B;;IAEnC6C,WAAW,CAACC,OAAD,EAAUC,GAAV,EAAe;MACxB,IAAI,CAACD,OAAD,IAAY5D,IAAI,CAAC0D,KAAL,CAAW1D,IAAI,CAAC0D,KAAL,CAAWI,MAAX,GAAoB,CAA/B,MAAsCD,GAAtD,EAA2D,OAAO,CAAC,CAAR;IAC5D;;EAJkC,CAArC;EAOA,KAAK1D,SAAL;AACD;;AAEM,SAAS4D,UAAT,CAAmC/D,IAAnC,EAAuD;EAC5D,IAAIA,IAAI,CAACQ,IAAT,EAAe;IACb,KAAKP,IAAL,CAAU,MAAV;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACQ,IAAhB,EAAsBR,IAAtB;IACA,KAAKG,SAAL;EACD,CALD,MAKO;IACL,KAAKF,IAAL,CAAU,SAAV;IACA,KAAKE,SAAL;EACD;;EAED,IAAIH,IAAI,CAACY,UAAL,CAAgBkD,MAApB,EAA4B;IAC1B,KAAKjD,OAAL;IACA,KAAK4C,aAAL,CAAmBzD,IAAI,CAACY,UAAxB,EAAoCZ,IAApC,EAA0C;MAAEc,MAAM,EAAE;IAAV,CAA1C;EACD;AACF;;AAEM,SAASkD,iBAAT,GAA0C;EAC/C,KAAK/D,IAAL,CAAU,UAAV;EACA,KAAKiC,SAAL;AACD;;AAED,SAAS+B,yBAAT,GAAkD;EAEhD,KAAK9D,SAAL;EACA,KAAKU,OAAL;;EACA,IAAI,KAAKI,QAAL,IAAJ,EAAuC;IACrC,KAAK,IAAIiD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B,KAAKhE,KAAL,CAAW,IAAX;EAC7B;AACF;;AAED,SAASiE,sBAAT,GAA+C;EAE7C,KAAKhE,SAAL;EACA,KAAKU,OAAL;;EACA,IAAI,KAAKI,QAAL,IAAJ,EAAuC;IACrC,KAAK,IAAIiD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B,KAAKhE,KAAL,CAAW,IAAX;EAC7B;AACF;;AAEM,SAASkE,mBAAT,CAELpE,IAFK,EAGLqC,MAHK,EAIL;EACA,IAAIrC,IAAI,CAACqE,OAAT,EAAkB;IAEhB,KAAKpE,IAAL,CAAU,SAAV;IACA,KAAKC,KAAL;EACD;;EAED,KAAKD,IAAL,CAAUD,IAAI,CAACsE,IAAf;EACA,KAAKpE,KAAL;EAEA,IAAIqE,QAAQ,GAAG,KAAf;;EAEA,IAAI,CAAC5E,KAAK,CAAC0C,MAAD,CAAV,EAAoB;IAClB,KAAK,MAAMmC,MAAX,IAAqBxE,IAAI,CAACyE,YAA1B,EAAwC;MACtC,IAAID,MAAM,CAAClD,IAAX,EAAiB;QAEfiD,QAAQ,GAAG,IAAX;MACD;IACF;EACF;;EAcD,IAAIG,SAAJ;;EACA,IAAIH,QAAJ,EAAc;IACZG,SAAS,GACP1E,IAAI,CAACsE,IAAL,KAAc,OAAd,GACIH,sBADJ,GAEIF,yBAHN;EAID;;EAID,KAAKU,SAAL,CAAe3E,IAAI,CAACyE,YAApB,EAAkCzE,IAAlC,EAAwC;IAAE0E;EAAF,CAAxC;;EAEA,IAAI/E,KAAK,CAAC0C,MAAD,CAAT,EAAmB;IAEjB,IAAIzC,cAAc,CAACyC,MAAD,CAAlB,EAA4B;MAC1B,IAAIA,MAAM,CAACf,IAAP,KAAgBtB,IAApB,EAA0B;IAC3B,CAFD,MAEO;MACL,IAAIqC,MAAM,CAACR,IAAP,KAAgB7B,IAApB,EAA0B;IAC3B;EACF;;EAED,KAAKkC,SAAL;AACD;;AAEM,SAAS0C,kBAAT,CAA2C5E,IAA3C,EAAuE;EAC5E,KAAKI,KAAL,CAAWJ,IAAI,CAAC6E,EAAhB,EAAoB7E,IAApB;EACA,IAAIA,IAAI,CAAC8E,QAAT,EAAmB,KAAK3E,SAAL;EAEnB,KAAKC,KAAL,CAAWJ,IAAI,CAAC6E,EAAL,CAAQvB,cAAnB,EAAmCtD,IAAnC;;EACA,IAAIA,IAAI,CAACsB,IAAT,EAAe;IACb,KAAKpB,KAAL;IACA,KAAKC,SAAL;IACA,KAAKD,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACsB,IAAhB,EAAsBtB,IAAtB;EACD;AACF"}