{"version": 3, "names": ["isEqualDescriptor", "a", "b", "name", "value", "options", "dirname", "alias", "ownPass", "file", "request", "resolved", "handlerOf", "optionsWithResolvedBrowserslistConfigFile", "browserslistConfigFile", "resolveBrowserslistConfigFile", "createCachedDescriptors", "plugins", "presets", "passPerPreset", "createCachedPluginDescriptors", "createCachedPresetDescriptors", "createUncachedDescriptors", "once", "createPluginDescriptors", "createPresetDescriptors", "PRESET_DESCRIPTOR_CACHE", "WeakMap", "makeWeakCacheSync", "items", "cache", "using", "dir", "makeStrongCacheSync", "makeStrongCache", "descriptors", "map", "desc", "loadCachedDescriptor", "PLUGIN_DESCRIPTOR_CACHE", "DEFAULT_OPTIONS", "cacheByOptions", "get", "set", "possibilities", "indexOf", "matches", "filter", "possibility", "length", "push", "createDescriptors", "type", "gens<PERSON>", "all", "item", "index", "createDescriptor", "assertNoDuplicates", "pair", "getItemDescriptor", "Array", "isArray", "undefined", "filepath", "Error", "resolver", "loadPlugin", "loadPreset", "String", "__esModule", "default", "Map", "nameMap", "Set", "has", "conflicts", "i", "JSON", "stringify", "join", "add"], "sources": ["../../src/config/config-descriptors.ts"], "sourcesContent": ["import gensync, { type Hand<PERSON> } from \"gensync\";\nimport { once } from \"../gensync-utils/functional\";\n\nimport { loadPlugin, loadPreset } from \"./files\";\n\nimport { getItemDescriptor } from \"./item\";\n\nimport {\n  makeWeakCacheSync,\n  makeStrongCacheSync,\n  makeStrongCache,\n} from \"./caching\";\nimport type { CacheConfigurator } from \"./caching\";\n\nimport type {\n  ValidatedOptions,\n  PluginList,\n  PluginItem,\n} from \"./validation/options\";\n\nimport { resolveBrowserslistConfigFile } from \"./resolve-targets\";\n\n// Represents a config object and functions to lazily load the descriptors\n// for the plugins and presets so we don't load the plugins/presets unless\n// the options object actually ends up being applicable.\nexport type OptionsAndDescriptors = {\n  options: ValidatedOptions;\n  plugins: () => Handler<Array<UnloadedDescriptor>>;\n  presets: () => Handler<Array<UnloadedDescriptor>>;\n};\n\n// Represents a plugin or presets at a given location in a config object.\n// At this point these have been resolved to a specific object or function,\n// but have not yet been executed to call functions with options.\nexport type UnloadedDescriptor = {\n  name: string | undefined;\n  value: any | Function;\n  options: {} | undefined | false;\n  dirname: string;\n  alias: string;\n  ownPass?: boolean;\n  file?: {\n    request: string;\n    resolved: string;\n  };\n};\n\nfunction isEqualDescriptor(\n  a: UnloadedDescriptor,\n  b: UnloadedDescriptor,\n): boolean {\n  return (\n    a.name === b.name &&\n    a.value === b.value &&\n    a.options === b.options &&\n    a.dirname === b.dirname &&\n    a.alias === b.alias &&\n    a.ownPass === b.ownPass &&\n    (a.file && a.file.request) === (b.file && b.file.request) &&\n    (a.file && a.file.resolved) === (b.file && b.file.resolved)\n  );\n}\n\nexport type ValidatedFile = {\n  filepath: string;\n  dirname: string;\n  options: ValidatedOptions;\n};\n\n// eslint-disable-next-line require-yield\nfunction* handlerOf<T>(value: T): Handler<T> {\n  return value;\n}\n\nfunction optionsWithResolvedBrowserslistConfigFile(\n  options: ValidatedOptions,\n  dirname: string,\n): ValidatedOptions {\n  if (typeof options.browserslistConfigFile === \"string\") {\n    options.browserslistConfigFile = resolveBrowserslistConfigFile(\n      options.browserslistConfigFile,\n      dirname,\n    );\n  }\n  return options;\n}\n\n/**\n * Create a set of descriptors from a given options object, preserving\n * descriptor identity based on the identity of the plugin/preset arrays\n * themselves, and potentially on the identity of the plugins/presets + options.\n */\nexport function createCachedDescriptors(\n  dirname: string,\n  options: ValidatedOptions,\n  alias: string,\n): OptionsAndDescriptors {\n  const { plugins, presets, passPerPreset } = options;\n  return {\n    options: optionsWithResolvedBrowserslistConfigFile(options, dirname),\n    plugins: plugins\n      ? () =>\n          // @ts-expect-error todo(flow->ts) ts complains about incorrect arguments\n          createCachedPluginDescriptors(plugins, dirname)(alias)\n      : () => handlerOf([]),\n    presets: presets\n      ? () =>\n          // @ts-expect-error todo(flow->ts) ts complains about incorrect arguments\n          createCachedPresetDescriptors(presets, dirname)(alias)(\n            !!passPerPreset,\n          )\n      : () => handlerOf([]),\n  };\n}\n\n/**\n * Create a set of descriptors from a given options object, with consistent\n * identity for the descriptors, but not caching based on any specific identity.\n */\nexport function createUncachedDescriptors(\n  dirname: string,\n  options: ValidatedOptions,\n  alias: string,\n): OptionsAndDescriptors {\n  return {\n    options: optionsWithResolvedBrowserslistConfigFile(options, dirname),\n    // The returned result here is cached to represent a config object in\n    // memory, so we build and memoize the descriptors to ensure the same\n    // values are returned consistently.\n    plugins: once(() =>\n      createPluginDescriptors(options.plugins || [], dirname, alias),\n    ),\n    presets: once(() =>\n      createPresetDescriptors(\n        options.presets || [],\n        dirname,\n        alias,\n        !!options.passPerPreset,\n      ),\n    ),\n  };\n}\n\nconst PRESET_DESCRIPTOR_CACHE = new WeakMap();\nconst createCachedPresetDescriptors = makeWeakCacheSync(\n  (items: PluginList, cache: CacheConfigurator<string>) => {\n    const dirname = cache.using(dir => dir);\n    return makeStrongCacheSync((alias: string) =>\n      makeStrongCache(function* (\n        passPerPreset: boolean,\n      ): Handler<Array<UnloadedDescriptor>> {\n        const descriptors = yield* createPresetDescriptors(\n          items,\n          dirname,\n          alias,\n          passPerPreset,\n        );\n        return descriptors.map(\n          // Items are cached using the overall preset array identity when\n          // possibly, but individual descriptors are also cached if a match\n          // can be found in the previously-used descriptor lists.\n          desc => loadCachedDescriptor(PRESET_DESCRIPTOR_CACHE, desc),\n        );\n      }),\n    );\n  },\n);\n\nconst PLUGIN_DESCRIPTOR_CACHE = new WeakMap();\nconst createCachedPluginDescriptors = makeWeakCacheSync(\n  (items: PluginList, cache: CacheConfigurator<string>) => {\n    const dirname = cache.using(dir => dir);\n    return makeStrongCache(function* (\n      alias: string,\n    ): Handler<Array<UnloadedDescriptor>> {\n      const descriptors = yield* createPluginDescriptors(items, dirname, alias);\n      return descriptors.map(\n        // Items are cached using the overall plugin array identity when\n        // possibly, but individual descriptors are also cached if a match\n        // can be found in the previously-used descriptor lists.\n        desc => loadCachedDescriptor(PLUGIN_DESCRIPTOR_CACHE, desc),\n      );\n    });\n  },\n);\n\n/**\n * When no options object is given in a descriptor, this object is used\n * as a WeakMap key in order to have consistent identity.\n */\nconst DEFAULT_OPTIONS = {};\n\n/**\n * Given the cache and a descriptor, returns a matching descriptor from the\n * cache, or else returns the input descriptor and adds it to the cache for\n * next time.\n */\nfunction loadCachedDescriptor(\n  cache: WeakMap<{} | Function, WeakMap<{}, Array<UnloadedDescriptor>>>,\n  desc: UnloadedDescriptor,\n) {\n  const { value, options = DEFAULT_OPTIONS } = desc;\n  if (options === false) return desc;\n\n  let cacheByOptions = cache.get(value);\n  if (!cacheByOptions) {\n    cacheByOptions = new WeakMap();\n    cache.set(value, cacheByOptions);\n  }\n\n  let possibilities = cacheByOptions.get(options);\n  if (!possibilities) {\n    possibilities = [];\n    cacheByOptions.set(options, possibilities);\n  }\n\n  if (possibilities.indexOf(desc) === -1) {\n    const matches = possibilities.filter(possibility =>\n      isEqualDescriptor(possibility, desc),\n    );\n    if (matches.length > 0) {\n      return matches[0];\n    }\n\n    possibilities.push(desc);\n  }\n\n  return desc;\n}\n\nfunction* createPresetDescriptors(\n  items: PluginList,\n  dirname: string,\n  alias: string,\n  passPerPreset: boolean,\n): Handler<Array<UnloadedDescriptor>> {\n  return yield* createDescriptors(\n    \"preset\",\n    items,\n    dirname,\n    alias,\n    passPerPreset,\n  );\n}\n\nfunction* createPluginDescriptors(\n  items: PluginList,\n  dirname: string,\n  alias: string,\n): Handler<Array<UnloadedDescriptor>> {\n  return yield* createDescriptors(\"plugin\", items, dirname, alias);\n}\n\nfunction* createDescriptors(\n  type: \"plugin\" | \"preset\",\n  items: PluginList,\n  dirname: string,\n  alias: string,\n  ownPass?: boolean,\n): Handler<Array<UnloadedDescriptor>> {\n  const descriptors = yield* gensync.all(\n    items.map((item, index) =>\n      createDescriptor(item, dirname, {\n        type,\n        alias: `${alias}$${index}`,\n        ownPass: !!ownPass,\n      }),\n    ),\n  );\n\n  assertNoDuplicates(descriptors);\n\n  return descriptors;\n}\n\n/**\n * Given a plugin/preset item, resolve it into a standard format.\n */\nexport function* createDescriptor(\n  pair: PluginItem,\n  dirname: string,\n  {\n    type,\n    alias,\n    ownPass,\n  }: {\n    type?: \"plugin\" | \"preset\";\n    alias: string;\n    ownPass?: boolean;\n  },\n): Handler<UnloadedDescriptor> {\n  const desc = getItemDescriptor(pair);\n  if (desc) {\n    return desc;\n  }\n\n  let name;\n  let options;\n  // todo(flow->ts) better type annotation\n  let value: any = pair;\n  if (Array.isArray(value)) {\n    if (value.length === 3) {\n      [value, options, name] = value;\n    } else {\n      [value, options] = value;\n    }\n  }\n\n  let file = undefined;\n  let filepath = null;\n  if (typeof value === \"string\") {\n    if (typeof type !== \"string\") {\n      throw new Error(\n        \"To resolve a string-based item, the type of item must be given\",\n      );\n    }\n    const resolver = type === \"plugin\" ? loadPlugin : loadPreset;\n    const request = value;\n\n    ({ filepath, value } = yield* resolver(value, dirname));\n\n    file = {\n      request,\n      resolved: filepath,\n    };\n  }\n\n  if (!value) {\n    throw new Error(`Unexpected falsy value: ${String(value)}`);\n  }\n\n  if (typeof value === \"object\" && value.__esModule) {\n    if (value.default) {\n      value = value.default;\n    } else {\n      throw new Error(\"Must export a default export when using ES6 modules.\");\n    }\n  }\n\n  if (typeof value !== \"object\" && typeof value !== \"function\") {\n    throw new Error(\n      `Unsupported format: ${typeof value}. Expected an object or a function.`,\n    );\n  }\n\n  if (filepath !== null && typeof value === \"object\" && value) {\n    // We allow object values for plugins/presets nested directly within a\n    // config object, because it can be useful to define them in nested\n    // configuration contexts.\n    throw new Error(\n      `Plugin/Preset files are not allowed to export objects, only functions. In ${filepath}`,\n    );\n  }\n\n  return {\n    name,\n    alias: filepath || alias,\n    value,\n    options,\n    dirname,\n    ownPass,\n    file,\n  };\n}\n\nfunction assertNoDuplicates(items: Array<UnloadedDescriptor>): void {\n  const map = new Map();\n\n  for (const item of items) {\n    if (typeof item.value !== \"function\") continue;\n\n    let nameMap = map.get(item.value);\n    if (!nameMap) {\n      nameMap = new Set();\n      map.set(item.value, nameMap);\n    }\n\n    if (nameMap.has(item.name)) {\n      const conflicts = items.filter(i => i.value === item.value);\n      throw new Error(\n        [\n          `Duplicate plugin/preset detected.`,\n          `If you'd like to use two separate instances of a plugin,`,\n          `they need separate names, e.g.`,\n          ``,\n          `  plugins: [`,\n          `    ['some-plugin', {}],`,\n          `    ['some-plugin', {}, 'some unique name'],`,\n          `  ]`,\n          ``,\n          `Duplicates detected are:`,\n          `${JSON.stringify(conflicts, null, 2)}`,\n        ].join(\"\\n\"),\n      );\n    }\n\n    nameMap.add(item.name);\n  }\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAEA;;AAEA;;AAEA;;AAaA;;AA2BA,SAASA,iBAAT,CACEC,CADF,EAEEC,CAFF,EAGW;EACT,OACED,CAAC,CAACE,IAAF,KAAWD,CAAC,CAACC,IAAb,IACAF,CAAC,CAACG,KAAF,KAAYF,CAAC,CAACE,KADd,IAEAH,CAAC,CAACI,OAAF,KAAcH,CAAC,CAACG,OAFhB,IAGAJ,CAAC,CAACK,OAAF,KAAcJ,CAAC,CAACI,OAHhB,IAIAL,CAAC,CAACM,KAAF,KAAYL,CAAC,CAACK,KAJd,IAKAN,CAAC,CAACO,OAAF,KAAcN,CAAC,CAACM,OALhB,IAMA,CAACP,CAAC,CAACQ,IAAF,IAAUR,CAAC,CAACQ,IAAF,CAAOC,OAAlB,OAAgCR,CAAC,CAACO,IAAF,IAAUP,CAAC,CAACO,IAAF,CAAOC,OAAjD,CANA,IAOA,CAACT,CAAC,CAACQ,IAAF,IAAUR,CAAC,CAACQ,IAAF,CAAOE,QAAlB,OAAiCT,CAAC,CAACO,IAAF,IAAUP,CAAC,CAACO,IAAF,CAAOE,QAAlD,CARF;AAUD;;AASD,UAAUC,SAAV,CAAuBR,KAAvB,EAA6C;EAC3C,OAAOA,KAAP;AACD;;AAED,SAASS,yCAAT,CACER,OADF,EAEEC,OAFF,EAGoB;EAClB,IAAI,OAAOD,OAAO,CAACS,sBAAf,KAA0C,QAA9C,EAAwD;IACtDT,OAAO,CAACS,sBAAR,GAAiC,IAAAC,6CAAA,EAC/BV,OAAO,CAACS,sBADuB,EAE/BR,OAF+B,CAAjC;EAID;;EACD,OAAOD,OAAP;AACD;;AAOM,SAASW,uBAAT,CACLV,OADK,EAELD,OAFK,EAGLE,KAHK,EAIkB;EACvB,MAAM;IAAEU,OAAF;IAAWC,OAAX;IAAoBC;EAApB,IAAsCd,OAA5C;EACA,OAAO;IACLA,OAAO,EAAEQ,yCAAyC,CAACR,OAAD,EAAUC,OAAV,CAD7C;IAELW,OAAO,EAAEA,OAAO,GACZ,MAEEG,6BAA6B,CAACH,OAAD,EAAUX,OAAV,CAA7B,CAAgDC,KAAhD,CAHU,GAIZ,MAAMK,SAAS,CAAC,EAAD,CANd;IAOLM,OAAO,EAAEA,OAAO,GACZ,MAEEG,6BAA6B,CAACH,OAAD,EAAUZ,OAAV,CAA7B,CAAgDC,KAAhD,EACE,CAAC,CAACY,aADJ,CAHU,GAMZ,MAAMP,SAAS,CAAC,EAAD;EAbd,CAAP;AAeD;;AAMM,SAASU,yBAAT,CACLhB,OADK,EAELD,OAFK,EAGLE,KAHK,EAIkB;EACvB,OAAO;IACLF,OAAO,EAAEQ,yCAAyC,CAACR,OAAD,EAAUC,OAAV,CAD7C;IAKLW,OAAO,EAAE,IAAAM,gBAAA,EAAK,MACZC,uBAAuB,CAACnB,OAAO,CAACY,OAAR,IAAmB,EAApB,EAAwBX,OAAxB,EAAiCC,KAAjC,CADhB,CALJ;IAQLW,OAAO,EAAE,IAAAK,gBAAA,EAAK,MACZE,uBAAuB,CACrBpB,OAAO,CAACa,OAAR,IAAmB,EADE,EAErBZ,OAFqB,EAGrBC,KAHqB,EAIrB,CAAC,CAACF,OAAO,CAACc,aAJW,CADhB;EARJ,CAAP;AAiBD;;AAED,MAAMO,uBAAuB,GAAG,IAAIC,OAAJ,EAAhC;AACA,MAAMN,6BAA6B,GAAG,IAAAO,0BAAA,EACpC,CAACC,KAAD,EAAoBC,KAApB,KAAyD;EACvD,MAAMxB,OAAO,GAAGwB,KAAK,CAACC,KAAN,CAAYC,GAAG,IAAIA,GAAnB,CAAhB;EACA,OAAO,IAAAC,4BAAA,EAAqB1B,KAAD,IACzB,IAAA2B,wBAAA,EAAgB,WACdf,aADc,EAEsB;IACpC,MAAMgB,WAAW,GAAG,OAAOV,uBAAuB,CAChDI,KADgD,EAEhDvB,OAFgD,EAGhDC,KAHgD,EAIhDY,aAJgD,CAAlD;IAMA,OAAOgB,WAAW,CAACC,GAAZ,CAILC,IAAI,IAAIC,oBAAoB,CAACZ,uBAAD,EAA0BW,IAA1B,CAJvB,CAAP;EAMD,CAfD,CADK,CAAP;AAkBD,CArBmC,CAAtC;AAwBA,MAAME,uBAAuB,GAAG,IAAIZ,OAAJ,EAAhC;AACA,MAAMP,6BAA6B,GAAG,IAAAQ,0BAAA,EACpC,CAACC,KAAD,EAAoBC,KAApB,KAAyD;EACvD,MAAMxB,OAAO,GAAGwB,KAAK,CAACC,KAAN,CAAYC,GAAG,IAAIA,GAAnB,CAAhB;EACA,OAAO,IAAAE,wBAAA,EAAgB,WACrB3B,KADqB,EAEe;IACpC,MAAM4B,WAAW,GAAG,OAAOX,uBAAuB,CAACK,KAAD,EAAQvB,OAAR,EAAiBC,KAAjB,CAAlD;IACA,OAAO4B,WAAW,CAACC,GAAZ,CAILC,IAAI,IAAIC,oBAAoB,CAACC,uBAAD,EAA0BF,IAA1B,CAJvB,CAAP;EAMD,CAVM,CAAP;AAWD,CAdmC,CAAtC;AAqBA,MAAMG,eAAe,GAAG,EAAxB;;AAOA,SAASF,oBAAT,CACER,KADF,EAEEO,IAFF,EAGE;EACA,MAAM;IAAEjC,KAAF;IAASC,OAAO,GAAGmC;EAAnB,IAAuCH,IAA7C;EACA,IAAIhC,OAAO,KAAK,KAAhB,EAAuB,OAAOgC,IAAP;EAEvB,IAAII,cAAc,GAAGX,KAAK,CAACY,GAAN,CAAUtC,KAAV,CAArB;;EACA,IAAI,CAACqC,cAAL,EAAqB;IACnBA,cAAc,GAAG,IAAId,OAAJ,EAAjB;IACAG,KAAK,CAACa,GAAN,CAAUvC,KAAV,EAAiBqC,cAAjB;EACD;;EAED,IAAIG,aAAa,GAAGH,cAAc,CAACC,GAAf,CAAmBrC,OAAnB,CAApB;;EACA,IAAI,CAACuC,aAAL,EAAoB;IAClBA,aAAa,GAAG,EAAhB;IACAH,cAAc,CAACE,GAAf,CAAmBtC,OAAnB,EAA4BuC,aAA5B;EACD;;EAED,IAAIA,aAAa,CAACC,OAAd,CAAsBR,IAAtB,MAAgC,CAAC,CAArC,EAAwC;IACtC,MAAMS,OAAO,GAAGF,aAAa,CAACG,MAAd,CAAqBC,WAAW,IAC9ChD,iBAAiB,CAACgD,WAAD,EAAcX,IAAd,CADH,CAAhB;;IAGA,IAAIS,OAAO,CAACG,MAAR,GAAiB,CAArB,EAAwB;MACtB,OAAOH,OAAO,CAAC,CAAD,CAAd;IACD;;IAEDF,aAAa,CAACM,IAAd,CAAmBb,IAAnB;EACD;;EAED,OAAOA,IAAP;AACD;;AAED,UAAUZ,uBAAV,CACEI,KADF,EAEEvB,OAFF,EAGEC,KAHF,EAIEY,aAJF,EAKsC;EACpC,OAAO,OAAOgC,iBAAiB,CAC7B,QAD6B,EAE7BtB,KAF6B,EAG7BvB,OAH6B,EAI7BC,KAJ6B,EAK7BY,aAL6B,CAA/B;AAOD;;AAED,UAAUK,uBAAV,CACEK,KADF,EAEEvB,OAFF,EAGEC,KAHF,EAIsC;EACpC,OAAO,OAAO4C,iBAAiB,CAAC,QAAD,EAAWtB,KAAX,EAAkBvB,OAAlB,EAA2BC,KAA3B,CAA/B;AACD;;AAED,UAAU4C,iBAAV,CACEC,IADF,EAEEvB,KAFF,EAGEvB,OAHF,EAIEC,KAJF,EAKEC,OALF,EAMsC;EACpC,MAAM2B,WAAW,GAAG,OAAOkB,UAAA,CAAQC,GAAR,CACzBzB,KAAK,CAACO,GAAN,CAAU,CAACmB,IAAD,EAAOC,KAAP,KACRC,gBAAgB,CAACF,IAAD,EAAOjD,OAAP,EAAgB;IAC9B8C,IAD8B;IAE9B7C,KAAK,EAAG,GAAEA,KAAM,IAAGiD,KAAM,EAFK;IAG9BhD,OAAO,EAAE,CAAC,CAACA;EAHmB,CAAhB,CADlB,CADyB,CAA3B;EAUAkD,kBAAkB,CAACvB,WAAD,CAAlB;EAEA,OAAOA,WAAP;AACD;;AAKM,UAAUsB,gBAAV,CACLE,IADK,EAELrD,OAFK,EAGL;EACE8C,IADF;EAEE7C,KAFF;EAGEC;AAHF,CAHK,EAYwB;EAC7B,MAAM6B,IAAI,GAAG,IAAAuB,uBAAA,EAAkBD,IAAlB,CAAb;;EACA,IAAItB,IAAJ,EAAU;IACR,OAAOA,IAAP;EACD;;EAED,IAAIlC,IAAJ;EACA,IAAIE,OAAJ;EAEA,IAAID,KAAU,GAAGuD,IAAjB;;EACA,IAAIE,KAAK,CAACC,OAAN,CAAc1D,KAAd,CAAJ,EAA0B;IACxB,IAAIA,KAAK,CAAC6C,MAAN,KAAiB,CAArB,EAAwB;MACtB,CAAC7C,KAAD,EAAQC,OAAR,EAAiBF,IAAjB,IAAyBC,KAAzB;IACD,CAFD,MAEO;MACL,CAACA,KAAD,EAAQC,OAAR,IAAmBD,KAAnB;IACD;EACF;;EAED,IAAIK,IAAI,GAAGsD,SAAX;EACA,IAAIC,QAAQ,GAAG,IAAf;;EACA,IAAI,OAAO5D,KAAP,KAAiB,QAArB,EAA+B;IAC7B,IAAI,OAAOgD,IAAP,KAAgB,QAApB,EAA8B;MAC5B,MAAM,IAAIa,KAAJ,CACJ,gEADI,CAAN;IAGD;;IACD,MAAMC,QAAQ,GAAGd,IAAI,KAAK,QAAT,GAAoBe,iBAApB,GAAiCC,iBAAlD;IACA,MAAM1D,OAAO,GAAGN,KAAhB;IAEA,CAAC;MAAE4D,QAAF;MAAY5D;IAAZ,IAAsB,OAAO8D,QAAQ,CAAC9D,KAAD,EAAQE,OAAR,CAAtC;IAEAG,IAAI,GAAG;MACLC,OADK;MAELC,QAAQ,EAAEqD;IAFL,CAAP;EAID;;EAED,IAAI,CAAC5D,KAAL,EAAY;IACV,MAAM,IAAI6D,KAAJ,CAAW,2BAA0BI,MAAM,CAACjE,KAAD,CAAQ,EAAnD,CAAN;EACD;;EAED,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACkE,UAAvC,EAAmD;IACjD,IAAIlE,KAAK,CAACmE,OAAV,EAAmB;MACjBnE,KAAK,GAAGA,KAAK,CAACmE,OAAd;IACD,CAFD,MAEO;MACL,MAAM,IAAIN,KAAJ,CAAU,sDAAV,CAAN;IACD;EACF;;EAED,IAAI,OAAO7D,KAAP,KAAiB,QAAjB,IAA6B,OAAOA,KAAP,KAAiB,UAAlD,EAA8D;IAC5D,MAAM,IAAI6D,KAAJ,CACH,uBAAsB,OAAO7D,KAAM,qCADhC,CAAN;EAGD;;EAED,IAAI4D,QAAQ,KAAK,IAAb,IAAqB,OAAO5D,KAAP,KAAiB,QAAtC,IAAkDA,KAAtD,EAA6D;IAI3D,MAAM,IAAI6D,KAAJ,CACH,6EAA4ED,QAAS,EADlF,CAAN;EAGD;;EAED,OAAO;IACL7D,IADK;IAELI,KAAK,EAAEyD,QAAQ,IAAIzD,KAFd;IAGLH,KAHK;IAILC,OAJK;IAKLC,OALK;IAMLE,OANK;IAOLC;EAPK,CAAP;AASD;;AAED,SAASiD,kBAAT,CAA4B7B,KAA5B,EAAoE;EAClE,MAAMO,GAAG,GAAG,IAAIoC,GAAJ,EAAZ;;EAEA,KAAK,MAAMjB,IAAX,IAAmB1B,KAAnB,EAA0B;IACxB,IAAI,OAAO0B,IAAI,CAACnD,KAAZ,KAAsB,UAA1B,EAAsC;IAEtC,IAAIqE,OAAO,GAAGrC,GAAG,CAACM,GAAJ,CAAQa,IAAI,CAACnD,KAAb,CAAd;;IACA,IAAI,CAACqE,OAAL,EAAc;MACZA,OAAO,GAAG,IAAIC,GAAJ,EAAV;MACAtC,GAAG,CAACO,GAAJ,CAAQY,IAAI,CAACnD,KAAb,EAAoBqE,OAApB;IACD;;IAED,IAAIA,OAAO,CAACE,GAAR,CAAYpB,IAAI,CAACpD,IAAjB,CAAJ,EAA4B;MAC1B,MAAMyE,SAAS,GAAG/C,KAAK,CAACkB,MAAN,CAAa8B,CAAC,IAAIA,CAAC,CAACzE,KAAF,KAAYmD,IAAI,CAACnD,KAAnC,CAAlB;MACA,MAAM,IAAI6D,KAAJ,CACJ,CACG,mCADH,EAEG,0DAFH,EAGG,gCAHH,EAIG,EAJH,EAKG,cALH,EAMG,0BANH,EAOG,8CAPH,EAQG,KARH,EASG,EATH,EAUG,0BAVH,EAWG,GAAEa,IAAI,CAACC,SAAL,CAAeH,SAAf,EAA0B,IAA1B,EAAgC,CAAhC,CAAmC,EAXxC,EAYEI,IAZF,CAYO,IAZP,CADI,CAAN;IAeD;;IAEDP,OAAO,CAACQ,GAAR,CAAY1B,IAAI,CAACpD,IAAjB;EACD;AACF"}