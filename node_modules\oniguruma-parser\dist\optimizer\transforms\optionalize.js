"use strict";import{isAlternativeContainer as A,isQuantifiable as u}from"../../parser/node-utils.js";import{createQuantifier as m}from"../../parser/parse.js";import{throwIfNullish as c}from"../../utils.js";import{isAllowedSimpleNode as d,isNodeEqual as v}from"./extract-prefix.js";const K={"*"({node:i}){if(!A(i)||i.body.length<2)return;const{body:n}=i,r=[n[0]];let f=n[0];for(let o=1;o<n.length;o++){const s=n[o],l=s.body,t=f.body,p=Math.abs(l.length-t.length);if(p){if(p===1){const a=t.length>l.length,y=a?l:l.slice(0,-1),g=a?t.slice(0,-1):t;if(h(y,g)){if(a){const e=c(t.at(-1));if(u(e))if(e.type==="Quantifier")if(e.min){if(e.min===1&&e.kind!=="lazy"){e.min=0;continue}}else continue;else{t.pop(),t.push(m("greedy",0,1,e));continue}}else if(t.length>0||n.length===2){const e=c(l.at(-1));if(u(e))if(e.type==="Quantifier"){if(e.kind!=="possessive"){if(e.min<=1&&e.kind==="lazy"){e.min=0,t.push(e);continue}else if(!e.min&&e.max===1){e.kind="lazy",t.push(e);continue}}}else{t.push(m("lazy",0,1,e));continue}}}}}else if(h(l,t))continue;r.push(s),f=s}i.body=r}};function h(i,n){if(i.length!==n.length)return!1;for(let r=0;r<i.length;r++)if(!d(i[r])||!d(n[r])||!v(i[r],n[r]))return!1;return!0}export{K as optionalize};
//# sourceMappingURL=optionalize.js.map
