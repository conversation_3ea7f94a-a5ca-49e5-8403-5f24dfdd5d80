"use strict";import{createCharacter as u,createCharacterClassRange as h}from"../../parser/parse.js";const m={CharacterClass({node:t}){if(t.kind!=="union"||!t.body.length)return;const l=[];for(const e of t.body)e.type==="CharacterSet"&&l.some(a=>a.type===e.type&&a.kind===e.kind&&a.negate===e.negate&&a.value===e.value)||l.push(e);t.body=l;const o=[],n=[];for(const e of t.body)e.type==="Character"||e.type==="CharacterClassRange"?n.push(e):o.push(e);if(!n.length)return;n.sort((e,a)=>{const r=e.type==="Character"?e.value:e.min.value,c=a.type==="Character"?a.value:a.min.value;return r-c});const s=[n[0]];for(let e=1;e<n.length;e++){const a=n[e],r=s.at(-1),c=a.type==="Character"?a.value:a.min.value,p=r.type==="Character"?r.value:r.max.value;if(c<=p+1)if(r.type==="Character"&&a.type==="Character")r.value!==a.value&&(s[s.length-1]=h(r,a));else if(r.type==="Character"&&a.type==="CharacterClassRange")s[s.length-1]=h(u(r.value),a.max);else if(r.type==="CharacterClassRange"&&a.type==="Character")r.max.value=Math.max(a.value,r.max.value);else if(r.type==="CharacterClassRange"&&a.type==="CharacterClassRange")r.max.value=Math.max(a.max.value,r.max.value);else throw new Error("Unexpected merge case");else s.push(a)}const i=s.flatMap(e=>{if(e.type==="CharacterClassRange"){const a=e.max.value-e.min.value;if(e.min.value>262143&&a>1)return e;if(a){if(a===1)return[e.min,e.max];if(a===2)return[e.min,u(e.min.value+1),e.max]}else return e.min}return e});t.body=[...i.filter(e=>C(e)),...i.filter(e=>!C(e)),...o]}};function C(t){return t.type==="Character"&&(t.value===45||t.value===93)}export{m as mergeRanges};
//# sourceMappingURL=merge-ranges.js.map
