{"version": 3, "names": ["msg", "loc", "type", "parent", "name", "index", "JSON", "stringify", "Error", "access", "assertRootMode", "value", "undefined", "assertSourceMaps", "assertCompact", "assertSourceType", "assertCallerMetadata", "obj", "assertObject", "prop", "Object", "keys", "propLoc", "assertInputSourceMap", "assertString", "assertFunction", "assertBoolean", "Array", "isArray", "assertArray", "assertIgnoreList", "arr", "for<PERSON>ach", "item", "i", "assertIgnoreItem", "RegExp", "assertConfigApplicableTest", "checkValidTest", "assertConfigFileSearch", "assertBabelrcSearch", "assertPluginList", "assertPluginItem", "length", "assertPlugin<PERSON>arget", "opts", "assertTargets", "isBrowsersQueryValid", "browsersLoc", "esmodulesLoc", "assertBrowsersList", "browsers", "<PERSON><PERSON><PERSON><PERSON>", "key", "val", "subLoc", "hasOwnProperty", "call", "TargetNames", "validTargets", "join", "assertBrowserVersion", "Math", "round", "assertAssumptions", "root", "inPreset", "source", "assumptionsNames", "has"], "sources": ["../../../src/config/validation/option-assertions.ts"], "sourcesContent": ["import {\n  isBrowsers<PERSON>ueryValid,\n  TargetNames,\n} from \"@babel/helper-compilation-targets\";\n\nimport type {\n  ConfigFileSearch,\n  BabelrcSearch,\n  IgnoreList,\n  IgnoreItem,\n  PluginList,\n  PluginItem,\n  PluginTarget,\n  ConfigApplicableTest,\n  SourceMapsOption,\n  SourceTypeOption,\n  CompactOption,\n  RootInputSourceMapOption,\n  NestingPath,\n  CallerMetadata,\n  RootMode,\n  TargetsListOrObject,\n  AssumptionName,\n} from \"./options\";\n\nimport { assumptionsNames } from \"./options\";\n\nexport type { RootPath } from \"./options\";\n\nexport type ValidatorSet = {\n  [name: string]: Validator<any>;\n};\n\nexport type Validator<T> = (loc: OptionPath, value: unknown) => T;\n\nexport function msg(loc: NestingPath | GeneralPath): string {\n  switch (loc.type) {\n    case \"root\":\n      return ``;\n    case \"env\":\n      return `${msg(loc.parent)}.env[\"${loc.name}\"]`;\n    case \"overrides\":\n      return `${msg(loc.parent)}.overrides[${loc.index}]`;\n    case \"option\":\n      return `${msg(loc.parent)}.${loc.name}`;\n    case \"access\":\n      return `${msg(loc.parent)}[${JSON.stringify(loc.name)}]`;\n    default:\n      // @ts-expect-error should not happen when code is type checked\n      throw new Error(`Assertion failure: Unknown type ${loc.type}`);\n  }\n}\n\nexport function access(loc: GeneralPath, name: string | number): AccessPath {\n  return {\n    type: \"access\",\n    name,\n    parent: loc,\n  };\n}\n\nexport type OptionPath = Readonly<{\n  type: \"option\";\n  name: string;\n  parent: NestingPath;\n}>;\ntype AccessPath = Readonly<{\n  type: \"access\";\n  name: string | number;\n  parent: GeneralPath;\n}>;\ntype GeneralPath = OptionPath | AccessPath;\n\nexport function assertRootMode(\n  loc: OptionPath,\n  value: unknown,\n): RootMode | void {\n  if (\n    value !== undefined &&\n    value !== \"root\" &&\n    value !== \"upward\" &&\n    value !== \"upward-optional\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a \"root\", \"upward\", \"upward-optional\" or undefined`,\n    );\n  }\n  return value;\n}\n\nexport function assertSourceMaps(\n  loc: OptionPath,\n  value: unknown,\n): SourceMapsOption | void {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    value !== \"inline\" &&\n    value !== \"both\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a boolean, \"inline\", \"both\", or undefined`,\n    );\n  }\n  return value;\n}\n\nexport function assertCompact(\n  loc: OptionPath,\n  value: unknown,\n): CompactOption | void {\n  if (value !== undefined && typeof value !== \"boolean\" && value !== \"auto\") {\n    throw new Error(`${msg(loc)} must be a boolean, \"auto\", or undefined`);\n  }\n  return value;\n}\n\nexport function assertSourceType(\n  loc: OptionPath,\n  value: unknown,\n): SourceTypeOption | void {\n  if (\n    value !== undefined &&\n    value !== \"module\" &&\n    value !== \"script\" &&\n    value !== \"unambiguous\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be \"module\", \"script\", \"unambiguous\", or undefined`,\n    );\n  }\n  return value;\n}\n\nexport function assertCallerMetadata(\n  loc: OptionPath,\n  value: unknown,\n): CallerMetadata | undefined {\n  const obj = assertObject(loc, value);\n  if (obj) {\n    if (typeof obj.name !== \"string\") {\n      throw new Error(\n        `${msg(loc)} set but does not contain \"name\" property string`,\n      );\n    }\n\n    for (const prop of Object.keys(obj)) {\n      const propLoc = access(loc, prop);\n      const value = obj[prop];\n      if (\n        value != null &&\n        typeof value !== \"boolean\" &&\n        typeof value !== \"string\" &&\n        typeof value !== \"number\"\n      ) {\n        // NOTE(logan): I'm limiting the type here so that we can guarantee that\n        // the \"caller\" value will serialize to JSON nicely. We can always\n        // allow more complex structures later though.\n        throw new Error(\n          `${msg(\n            propLoc,\n          )} must be null, undefined, a boolean, a string, or a number.`,\n        );\n      }\n    }\n  }\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\n\nexport function assertInputSourceMap(\n  loc: OptionPath,\n  value: unknown,\n): RootInputSourceMapOption | void {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    (typeof value !== \"object\" || !value)\n  ) {\n    throw new Error(`${msg(loc)} must be a boolean, object, or undefined`);\n  }\n  return value;\n}\n\nexport function assertString(loc: GeneralPath, value: unknown): string | void {\n  if (value !== undefined && typeof value !== \"string\") {\n    throw new Error(`${msg(loc)} must be a string, or undefined`);\n  }\n  return value;\n}\n\nexport function assertFunction(\n  loc: GeneralPath,\n  value: unknown,\n): Function | void {\n  if (value !== undefined && typeof value !== \"function\") {\n    throw new Error(`${msg(loc)} must be a function, or undefined`);\n  }\n  return value;\n}\n\nexport function assertBoolean(\n  loc: GeneralPath,\n  value: unknown,\n): boolean | void {\n  if (value !== undefined && typeof value !== \"boolean\") {\n    throw new Error(`${msg(loc)} must be a boolean, or undefined`);\n  }\n  return value;\n}\n\nexport function assertObject(\n  loc: GeneralPath,\n  value: unknown,\n): { readonly [key: string]: unknown } | void {\n  if (\n    value !== undefined &&\n    (typeof value !== \"object\" || Array.isArray(value) || !value)\n  ) {\n    throw new Error(`${msg(loc)} must be an object, or undefined`);\n  }\n  // @ts-expect-error todo(flow->ts) value is still typed as unknown, also assert function typically should not return a value\n  return value;\n}\n\nexport function assertArray<T>(\n  loc: GeneralPath,\n  value: Array<T> | undefined | null,\n): ReadonlyArray<T> | undefined | null {\n  if (value != null && !Array.isArray(value)) {\n    throw new Error(`${msg(loc)} must be an array, or undefined`);\n  }\n  return value;\n}\n\nexport function assertIgnoreList(\n  loc: OptionPath,\n  value: unknown[] | undefined,\n): IgnoreList | void {\n  const arr = assertArray(loc, value);\n  if (arr) {\n    arr.forEach((item, i) => assertIgnoreItem(access(loc, i), item));\n  }\n  // @ts-expect-error todo(flow->ts)\n  return arr;\n}\nfunction assertIgnoreItem(loc: GeneralPath, value: unknown): IgnoreItem {\n  if (\n    typeof value !== \"string\" &&\n    typeof value !== \"function\" &&\n    !(value instanceof RegExp)\n  ) {\n    throw new Error(\n      `${msg(\n        loc,\n      )} must be an array of string/Function/RegExp values, or undefined`,\n    );\n  }\n  return value as IgnoreItem;\n}\n\nexport function assertConfigApplicableTest(\n  loc: OptionPath,\n  value: unknown,\n): ConfigApplicableTest | void {\n  if (value === undefined) return value;\n\n  if (Array.isArray(value)) {\n    value.forEach((item, i) => {\n      if (!checkValidTest(item)) {\n        throw new Error(\n          `${msg(access(loc, i))} must be a string/Function/RegExp.`,\n        );\n      }\n    });\n  } else if (!checkValidTest(value)) {\n    throw new Error(\n      `${msg(loc)} must be a string/Function/RegExp, or an array of those`,\n    );\n  }\n  return value as ConfigApplicableTest;\n}\n\nfunction checkValidTest(value: unknown): value is string | Function | RegExp {\n  return (\n    typeof value === \"string\" ||\n    typeof value === \"function\" ||\n    value instanceof RegExp\n  );\n}\n\nexport function assertConfigFileSearch(\n  loc: OptionPath,\n  value: unknown,\n): ConfigFileSearch | void {\n  if (\n    value !== undefined &&\n    typeof value !== \"boolean\" &&\n    typeof value !== \"string\"\n  ) {\n    throw new Error(\n      `${msg(loc)} must be a undefined, a boolean, a string, ` +\n        `got ${JSON.stringify(value)}`,\n    );\n  }\n\n  return value;\n}\n\nexport function assertBabelrcSearch(\n  loc: OptionPath,\n  value: unknown,\n): BabelrcSearch | void {\n  if (value === undefined || typeof value === \"boolean\") return value;\n\n  if (Array.isArray(value)) {\n    value.forEach((item, i) => {\n      if (!checkValidTest(item)) {\n        throw new Error(\n          `${msg(access(loc, i))} must be a string/Function/RegExp.`,\n        );\n      }\n    });\n  } else if (!checkValidTest(value)) {\n    throw new Error(\n      `${msg(loc)} must be a undefined, a boolean, a string/Function/RegExp ` +\n        `or an array of those, got ${JSON.stringify(value as any)}`,\n    );\n  }\n  return value as BabelrcSearch;\n}\n\nexport function assertPluginList(\n  loc: OptionPath,\n  value: unknown[] | null | undefined,\n): PluginList | void {\n  const arr = assertArray(loc, value);\n  if (arr) {\n    // Loop instead of using `.map` in order to preserve object identity\n    // for plugin array for use during config chain processing.\n    arr.forEach((item, i) => assertPluginItem(access(loc, i), item));\n  }\n  return arr as any;\n}\nfunction assertPluginItem(loc: GeneralPath, value: unknown): PluginItem {\n  if (Array.isArray(value)) {\n    if (value.length === 0) {\n      throw new Error(`${msg(loc)} must include an object`);\n    }\n\n    if (value.length > 3) {\n      throw new Error(`${msg(loc)} may only be a two-tuple or three-tuple`);\n    }\n\n    assertPluginTarget(access(loc, 0), value[0]);\n\n    if (value.length > 1) {\n      const opts = value[1];\n      if (\n        opts !== undefined &&\n        opts !== false &&\n        (typeof opts !== \"object\" || Array.isArray(opts) || opts === null)\n      ) {\n        throw new Error(\n          `${msg(access(loc, 1))} must be an object, false, or undefined`,\n        );\n      }\n    }\n    if (value.length === 3) {\n      const name = value[2];\n      if (name !== undefined && typeof name !== \"string\") {\n        throw new Error(\n          `${msg(access(loc, 2))} must be a string, or undefined`,\n        );\n      }\n    }\n  } else {\n    assertPluginTarget(loc, value);\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\nfunction assertPluginTarget(loc: GeneralPath, value: unknown): PluginTarget {\n  if (\n    (typeof value !== \"object\" || !value) &&\n    typeof value !== \"string\" &&\n    typeof value !== \"function\"\n  ) {\n    throw new Error(`${msg(loc)} must be a string, object, function`);\n  }\n  return value;\n}\n\nexport function assertTargets(\n  loc: GeneralPath,\n  value: any,\n): TargetsListOrObject {\n  if (isBrowsersQueryValid(value)) return value;\n\n  if (typeof value !== \"object\" || !value || Array.isArray(value)) {\n    throw new Error(\n      `${msg(loc)} must be a string, an array of strings or an object`,\n    );\n  }\n\n  const browsersLoc = access(loc, \"browsers\");\n  const esmodulesLoc = access(loc, \"esmodules\");\n\n  assertBrowsersList(browsersLoc, value.browsers);\n  assertBoolean(esmodulesLoc, value.esmodules);\n\n  for (const key of Object.keys(value)) {\n    const val = value[key];\n    const subLoc = access(loc, key);\n\n    if (key === \"esmodules\") assertBoolean(subLoc, val);\n    else if (key === \"browsers\") assertBrowsersList(subLoc, val);\n    else if (!Object.hasOwnProperty.call(TargetNames, key)) {\n      const validTargets = Object.keys(TargetNames).join(\", \");\n      throw new Error(\n        `${msg(\n          subLoc,\n        )} is not a valid target. Supported targets are ${validTargets}`,\n      );\n    } else assertBrowserVersion(subLoc, val);\n  }\n\n  return value;\n}\n\nfunction assertBrowsersList(loc: GeneralPath, value: unknown) {\n  if (value !== undefined && !isBrowsersQueryValid(value)) {\n    throw new Error(\n      `${msg(loc)} must be undefined, a string or an array of strings`,\n    );\n  }\n}\n\nfunction assertBrowserVersion(loc: GeneralPath, value: unknown) {\n  if (typeof value === \"number\" && Math.round(value) === value) return;\n  if (typeof value === \"string\") return;\n\n  throw new Error(`${msg(loc)} must be a string or an integer number`);\n}\n\nexport function assertAssumptions(\n  loc: GeneralPath,\n  value: { [key: string]: unknown },\n): { [name: string]: boolean } | void {\n  if (value === undefined) return;\n\n  if (typeof value !== \"object\" || value === null) {\n    throw new Error(`${msg(loc)} must be an object or undefined.`);\n  }\n\n  // todo(flow->ts): remove any\n  let root: any = loc;\n  do {\n    root = root.parent;\n  } while (root.type !== \"root\");\n  const inPreset = root.source === \"preset\";\n\n  for (const name of Object.keys(value)) {\n    const subLoc = access(loc, name);\n    if (!assumptionsNames.has(name as AssumptionName)) {\n      throw new Error(`${msg(subLoc)} is not a supported assumption.`);\n    }\n    if (typeof value[name] !== \"boolean\") {\n      throw new Error(`${msg(subLoc)} must be a boolean.`);\n    }\n    if (inPreset && value[name] === false) {\n      throw new Error(\n        `${msg(subLoc)} cannot be set to 'false' inside presets.`,\n      );\n    }\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  return value;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAyBA;;AAUO,SAASA,GAAT,CAAaC,GAAb,EAAqD;EAC1D,QAAQA,GAAG,CAACC,IAAZ;IACE,KAAK,MAAL;MACE,OAAQ,EAAR;;IACF,KAAK,KAAL;MACE,OAAQ,GAAEF,GAAG,CAACC,GAAG,CAACE,MAAL,CAAa,SAAQF,GAAG,CAACG,IAAK,IAA3C;;IACF,KAAK,WAAL;MACE,OAAQ,GAAEJ,GAAG,CAACC,GAAG,CAACE,MAAL,CAAa,cAAaF,GAAG,CAACI,KAAM,GAAjD;;IACF,KAAK,QAAL;MACE,OAAQ,GAAEL,GAAG,CAACC,GAAG,CAACE,MAAL,CAAa,IAAGF,GAAG,CAACG,IAAK,EAAtC;;IACF,KAAK,QAAL;MACE,OAAQ,GAAEJ,GAAG,CAACC,GAAG,CAACE,MAAL,CAAa,IAAGG,IAAI,CAACC,SAAL,CAAeN,GAAG,CAACG,IAAnB,CAAyB,GAAtD;;IACF;MAEE,MAAM,IAAII,KAAJ,CAAW,mCAAkCP,GAAG,CAACC,IAAK,EAAtD,CAAN;EAbJ;AAeD;;AAEM,SAASO,MAAT,CAAgBR,GAAhB,EAAkCG,IAAlC,EAAqE;EAC1E,OAAO;IACLF,IAAI,EAAE,QADD;IAELE,IAFK;IAGLD,MAAM,EAAEF;EAHH,CAAP;AAKD;;AAcM,SAASS,cAAT,CACLT,GADK,EAELU,KAFK,EAGY;EACjB,IACEA,KAAK,KAAKC,SAAV,IACAD,KAAK,KAAK,MADV,IAEAA,KAAK,KAAK,QAFV,IAGAA,KAAK,KAAK,iBAJZ,EAKE;IACA,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,6DADR,CAAN;EAGD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASE,gBAAT,CACLZ,GADK,EAELU,KAFK,EAGoB;EACzB,IACEA,KAAK,KAAKC,SAAV,IACA,OAAOD,KAAP,KAAiB,SADjB,IAEAA,KAAK,KAAK,QAFV,IAGAA,KAAK,KAAK,MAJZ,EAKE;IACA,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,oDADR,CAAN;EAGD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASG,aAAT,CACLb,GADK,EAELU,KAFK,EAGiB;EACtB,IAAIA,KAAK,KAAKC,SAAV,IAAuB,OAAOD,KAAP,KAAiB,SAAxC,IAAqDA,KAAK,KAAK,MAAnE,EAA2E;IACzE,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,0CAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASI,gBAAT,CACLd,GADK,EAELU,KAFK,EAGoB;EACzB,IACEA,KAAK,KAAKC,SAAV,IACAD,KAAK,KAAK,QADV,IAEAA,KAAK,KAAK,QAFV,IAGAA,KAAK,KAAK,aAJZ,EAKE;IACA,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,0DADR,CAAN;EAGD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASK,oBAAT,CACLf,GADK,EAELU,KAFK,EAGuB;EAC5B,MAAMM,GAAG,GAAGC,YAAY,CAACjB,GAAD,EAAMU,KAAN,CAAxB;;EACA,IAAIM,GAAJ,EAAS;IACP,IAAI,OAAOA,GAAG,CAACb,IAAX,KAAoB,QAAxB,EAAkC;MAChC,MAAM,IAAII,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,kDADR,CAAN;IAGD;;IAED,KAAK,MAAMkB,IAAX,IAAmBC,MAAM,CAACC,IAAP,CAAYJ,GAAZ,CAAnB,EAAqC;MACnC,MAAMK,OAAO,GAAGb,MAAM,CAACR,GAAD,EAAMkB,IAAN,CAAtB;MACA,MAAMR,KAAK,GAAGM,GAAG,CAACE,IAAD,CAAjB;;MACA,IACER,KAAK,IAAI,IAAT,IACA,OAAOA,KAAP,KAAiB,SADjB,IAEA,OAAOA,KAAP,KAAiB,QAFjB,IAGA,OAAOA,KAAP,KAAiB,QAJnB,EAKE;QAIA,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CACJsB,OADI,CAEJ,6DAHE,CAAN;MAKD;IACF;EACF;;EAED,OAAOX,KAAP;AACD;;AAEM,SAASY,oBAAT,CACLtB,GADK,EAELU,KAFK,EAG4B;EACjC,IACEA,KAAK,KAAKC,SAAV,IACA,OAAOD,KAAP,KAAiB,SADjB,KAEC,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACA,KAF/B,CADF,EAIE;IACA,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,0CAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASa,YAAT,CAAsBvB,GAAtB,EAAwCU,KAAxC,EAAuE;EAC5E,IAAIA,KAAK,KAAKC,SAAV,IAAuB,OAAOD,KAAP,KAAiB,QAA5C,EAAsD;IACpD,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,iCAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASc,cAAT,CACLxB,GADK,EAELU,KAFK,EAGY;EACjB,IAAIA,KAAK,KAAKC,SAAV,IAAuB,OAAOD,KAAP,KAAiB,UAA5C,EAAwD;IACtD,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,mCAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASe,aAAT,CACLzB,GADK,EAELU,KAFK,EAGW;EAChB,IAAIA,KAAK,KAAKC,SAAV,IAAuB,OAAOD,KAAP,KAAiB,SAA5C,EAAuD;IACrD,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,kCAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASO,YAAT,CACLjB,GADK,EAELU,KAFK,EAGuC;EAC5C,IACEA,KAAK,KAAKC,SAAV,KACC,OAAOD,KAAP,KAAiB,QAAjB,IAA6BgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAA7B,IAAqD,CAACA,KADvD,CADF,EAGE;IACA,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,kCAAtB,CAAN;EACD;;EAED,OAAOU,KAAP;AACD;;AAEM,SAASkB,WAAT,CACL5B,GADK,EAELU,KAFK,EAGgC;EACrC,IAAIA,KAAK,IAAI,IAAT,IAAiB,CAACgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAAtB,EAA4C;IAC1C,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,iCAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASmB,gBAAT,CACL7B,GADK,EAELU,KAFK,EAGc;EACnB,MAAMoB,GAAG,GAAGF,WAAW,CAAC5B,GAAD,EAAMU,KAAN,CAAvB;;EACA,IAAIoB,GAAJ,EAAS;IACPA,GAAG,CAACC,OAAJ,CAAY,CAACC,IAAD,EAAOC,CAAP,KAAaC,gBAAgB,CAAC1B,MAAM,CAACR,GAAD,EAAMiC,CAAN,CAAP,EAAiBD,IAAjB,CAAzC;EACD;;EAED,OAAOF,GAAP;AACD;;AACD,SAASI,gBAAT,CAA0BlC,GAA1B,EAA4CU,KAA5C,EAAwE;EACtE,IACE,OAAOA,KAAP,KAAiB,QAAjB,IACA,OAAOA,KAAP,KAAiB,UADjB,IAEA,EAAEA,KAAK,YAAYyB,MAAnB,CAHF,EAIE;IACA,MAAM,IAAI5B,KAAJ,CACH,GAAER,GAAG,CACJC,GADI,CAEJ,kEAHE,CAAN;EAKD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAAS0B,0BAAT,CACLpC,GADK,EAELU,KAFK,EAGwB;EAC7B,IAAIA,KAAK,KAAKC,SAAd,EAAyB,OAAOD,KAAP;;EAEzB,IAAIgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAAJ,EAA0B;IACxBA,KAAK,CAACqB,OAAN,CAAc,CAACC,IAAD,EAAOC,CAAP,KAAa;MACzB,IAAI,CAACI,cAAc,CAACL,IAAD,CAAnB,EAA2B;QACzB,MAAM,IAAIzB,KAAJ,CACH,GAAER,GAAG,CAACS,MAAM,CAACR,GAAD,EAAMiC,CAAN,CAAP,CAAiB,oCADnB,CAAN;MAGD;IACF,CAND;EAOD,CARD,MAQO,IAAI,CAACI,cAAc,CAAC3B,KAAD,CAAnB,EAA4B;IACjC,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,yDADR,CAAN;EAGD;;EACD,OAAOU,KAAP;AACD;;AAED,SAAS2B,cAAT,CAAwB3B,KAAxB,EAA6E;EAC3E,OACE,OAAOA,KAAP,KAAiB,QAAjB,IACA,OAAOA,KAAP,KAAiB,UADjB,IAEAA,KAAK,YAAYyB,MAHnB;AAKD;;AAEM,SAASG,sBAAT,CACLtC,GADK,EAELU,KAFK,EAGoB;EACzB,IACEA,KAAK,KAAKC,SAAV,IACA,OAAOD,KAAP,KAAiB,SADjB,IAEA,OAAOA,KAAP,KAAiB,QAHnB,EAIE;IACA,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,6CAAZ,GACG,OAAMK,IAAI,CAACC,SAAL,CAAeI,KAAf,CAAsB,EAF3B,CAAN;EAID;;EAED,OAAOA,KAAP;AACD;;AAEM,SAAS6B,mBAAT,CACLvC,GADK,EAELU,KAFK,EAGiB;EACtB,IAAIA,KAAK,KAAKC,SAAV,IAAuB,OAAOD,KAAP,KAAiB,SAA5C,EAAuD,OAAOA,KAAP;;EAEvD,IAAIgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAAJ,EAA0B;IACxBA,KAAK,CAACqB,OAAN,CAAc,CAACC,IAAD,EAAOC,CAAP,KAAa;MACzB,IAAI,CAACI,cAAc,CAACL,IAAD,CAAnB,EAA2B;QACzB,MAAM,IAAIzB,KAAJ,CACH,GAAER,GAAG,CAACS,MAAM,CAACR,GAAD,EAAMiC,CAAN,CAAP,CAAiB,oCADnB,CAAN;MAGD;IACF,CAND;EAOD,CARD,MAQO,IAAI,CAACI,cAAc,CAAC3B,KAAD,CAAnB,EAA4B;IACjC,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,4DAAZ,GACG,6BAA4BK,IAAI,CAACC,SAAL,CAAeI,KAAf,CAA6B,EAFxD,CAAN;EAID;;EACD,OAAOA,KAAP;AACD;;AAEM,SAAS8B,gBAAT,CACLxC,GADK,EAELU,KAFK,EAGc;EACnB,MAAMoB,GAAG,GAAGF,WAAW,CAAC5B,GAAD,EAAMU,KAAN,CAAvB;;EACA,IAAIoB,GAAJ,EAAS;IAGPA,GAAG,CAACC,OAAJ,CAAY,CAACC,IAAD,EAAOC,CAAP,KAAaQ,gBAAgB,CAACjC,MAAM,CAACR,GAAD,EAAMiC,CAAN,CAAP,EAAiBD,IAAjB,CAAzC;EACD;;EACD,OAAOF,GAAP;AACD;;AACD,SAASW,gBAAT,CAA0BzC,GAA1B,EAA4CU,KAA5C,EAAwE;EACtE,IAAIgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAAJ,EAA0B;IACxB,IAAIA,KAAK,CAACgC,MAAN,KAAiB,CAArB,EAAwB;MACtB,MAAM,IAAInC,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,yBAAtB,CAAN;IACD;;IAED,IAAIU,KAAK,CAACgC,MAAN,GAAe,CAAnB,EAAsB;MACpB,MAAM,IAAInC,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,yCAAtB,CAAN;IACD;;IAED2C,kBAAkB,CAACnC,MAAM,CAACR,GAAD,EAAM,CAAN,CAAP,EAAiBU,KAAK,CAAC,CAAD,CAAtB,CAAlB;;IAEA,IAAIA,KAAK,CAACgC,MAAN,GAAe,CAAnB,EAAsB;MACpB,MAAME,IAAI,GAAGlC,KAAK,CAAC,CAAD,CAAlB;;MACA,IACEkC,IAAI,KAAKjC,SAAT,IACAiC,IAAI,KAAK,KADT,KAEC,OAAOA,IAAP,KAAgB,QAAhB,IAA4BlB,KAAK,CAACC,OAAN,CAAciB,IAAd,CAA5B,IAAmDA,IAAI,KAAK,IAF7D,CADF,EAIE;QACA,MAAM,IAAIrC,KAAJ,CACH,GAAER,GAAG,CAACS,MAAM,CAACR,GAAD,EAAM,CAAN,CAAP,CAAiB,yCADnB,CAAN;MAGD;IACF;;IACD,IAAIU,KAAK,CAACgC,MAAN,KAAiB,CAArB,EAAwB;MACtB,MAAMvC,IAAI,GAAGO,KAAK,CAAC,CAAD,CAAlB;;MACA,IAAIP,IAAI,KAAKQ,SAAT,IAAsB,OAAOR,IAAP,KAAgB,QAA1C,EAAoD;QAClD,MAAM,IAAII,KAAJ,CACH,GAAER,GAAG,CAACS,MAAM,CAACR,GAAD,EAAM,CAAN,CAAP,CAAiB,iCADnB,CAAN;MAGD;IACF;EACF,CA/BD,MA+BO;IACL2C,kBAAkB,CAAC3C,GAAD,EAAMU,KAAN,CAAlB;EACD;;EAGD,OAAOA,KAAP;AACD;;AACD,SAASiC,kBAAT,CAA4B3C,GAA5B,EAA8CU,KAA9C,EAA4E;EAC1E,IACE,CAAC,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACA,KAA/B,KACA,OAAOA,KAAP,KAAiB,QADjB,IAEA,OAAOA,KAAP,KAAiB,UAHnB,EAIE;IACA,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,qCAAtB,CAAN;EACD;;EACD,OAAOU,KAAP;AACD;;AAEM,SAASmC,aAAT,CACL7C,GADK,EAELU,KAFK,EAGgB;EACrB,IAAI,IAAAoC,gDAAA,EAAqBpC,KAArB,CAAJ,EAAiC,OAAOA,KAAP;;EAEjC,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6B,CAACA,KAA9B,IAAuCgB,KAAK,CAACC,OAAN,CAAcjB,KAAd,CAA3C,EAAiE;IAC/D,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,qDADR,CAAN;EAGD;;EAED,MAAM+C,WAAW,GAAGvC,MAAM,CAACR,GAAD,EAAM,UAAN,CAA1B;EACA,MAAMgD,YAAY,GAAGxC,MAAM,CAACR,GAAD,EAAM,WAAN,CAA3B;EAEAiD,kBAAkB,CAACF,WAAD,EAAcrC,KAAK,CAACwC,QAApB,CAAlB;EACAzB,aAAa,CAACuB,YAAD,EAAetC,KAAK,CAACyC,SAArB,CAAb;;EAEA,KAAK,MAAMC,GAAX,IAAkBjC,MAAM,CAACC,IAAP,CAAYV,KAAZ,CAAlB,EAAsC;IACpC,MAAM2C,GAAG,GAAG3C,KAAK,CAAC0C,GAAD,CAAjB;IACA,MAAME,MAAM,GAAG9C,MAAM,CAACR,GAAD,EAAMoD,GAAN,CAArB;IAEA,IAAIA,GAAG,KAAK,WAAZ,EAAyB3B,aAAa,CAAC6B,MAAD,EAASD,GAAT,CAAb,CAAzB,KACK,IAAID,GAAG,KAAK,UAAZ,EAAwBH,kBAAkB,CAACK,MAAD,EAASD,GAAT,CAAlB,CAAxB,KACA,IAAI,CAAClC,MAAM,CAACoC,cAAP,CAAsBC,IAAtB,CAA2BC,uCAA3B,EAAwCL,GAAxC,CAAL,EAAmD;MACtD,MAAMM,YAAY,GAAGvC,MAAM,CAACC,IAAP,CAAYqC,uCAAZ,EAAyBE,IAAzB,CAA8B,IAA9B,CAArB;MACA,MAAM,IAAIpD,KAAJ,CACH,GAAER,GAAG,CACJuD,MADI,CAEJ,iDAAgDI,YAAa,EAH3D,CAAN;IAKD,CAPI,MAOEE,oBAAoB,CAACN,MAAD,EAASD,GAAT,CAApB;EACR;;EAED,OAAO3C,KAAP;AACD;;AAED,SAASuC,kBAAT,CAA4BjD,GAA5B,EAA8CU,KAA9C,EAA8D;EAC5D,IAAIA,KAAK,KAAKC,SAAV,IAAuB,CAAC,IAAAmC,gDAAA,EAAqBpC,KAArB,CAA5B,EAAyD;IACvD,MAAM,IAAIH,KAAJ,CACH,GAAER,GAAG,CAACC,GAAD,CAAM,qDADR,CAAN;EAGD;AACF;;AAED,SAAS4D,oBAAT,CAA8B5D,GAA9B,EAAgDU,KAAhD,EAAgE;EAC9D,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BmD,IAAI,CAACC,KAAL,CAAWpD,KAAX,MAAsBA,KAAvD,EAA8D;EAC9D,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;EAE/B,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,wCAAtB,CAAN;AACD;;AAEM,SAAS+D,iBAAT,CACL/D,GADK,EAELU,KAFK,EAG+B;EACpC,IAAIA,KAAK,KAAKC,SAAd,EAAyB;;EAEzB,IAAI,OAAOD,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAA3C,EAAiD;IAC/C,MAAM,IAAIH,KAAJ,CAAW,GAAER,GAAG,CAACC,GAAD,CAAM,kCAAtB,CAAN;EACD;;EAGD,IAAIgE,IAAS,GAAGhE,GAAhB;;EACA,GAAG;IACDgE,IAAI,GAAGA,IAAI,CAAC9D,MAAZ;EACD,CAFD,QAES8D,IAAI,CAAC/D,IAAL,KAAc,MAFvB;;EAGA,MAAMgE,QAAQ,GAAGD,IAAI,CAACE,MAAL,KAAgB,QAAjC;;EAEA,KAAK,MAAM/D,IAAX,IAAmBgB,MAAM,CAACC,IAAP,CAAYV,KAAZ,CAAnB,EAAuC;IACrC,MAAM4C,MAAM,GAAG9C,MAAM,CAACR,GAAD,EAAMG,IAAN,CAArB;;IACA,IAAI,CAACgE,yBAAA,CAAiBC,GAAjB,CAAqBjE,IAArB,CAAL,EAAmD;MACjD,MAAM,IAAII,KAAJ,CAAW,GAAER,GAAG,CAACuD,MAAD,CAAS,iCAAzB,CAAN;IACD;;IACD,IAAI,OAAO5C,KAAK,CAACP,IAAD,CAAZ,KAAuB,SAA3B,EAAsC;MACpC,MAAM,IAAII,KAAJ,CAAW,GAAER,GAAG,CAACuD,MAAD,CAAS,qBAAzB,CAAN;IACD;;IACD,IAAIW,QAAQ,IAAIvD,KAAK,CAACP,IAAD,CAAL,KAAgB,KAAhC,EAAuC;MACrC,MAAM,IAAII,KAAJ,CACH,GAAER,GAAG,CAACuD,MAAD,CAAS,2CADX,CAAN;IAGD;EACF;;EAGD,OAAO5C,KAAP;AACD"}