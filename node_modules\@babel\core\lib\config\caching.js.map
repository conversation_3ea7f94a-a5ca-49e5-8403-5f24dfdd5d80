{"version": 3, "names": ["synchronize", "gen", "gens<PERSON>", "sync", "gen<PERSON><PERSON>", "makeWeakCache", "handler", "makeCachedFunction", "WeakMap", "makeWeakCacheSync", "makeStrongCache", "Map", "makeStrongCacheSync", "CallCache", "callCacheSync", "callCacheAsync", "futureCache", "cachedFunction", "arg", "data", "asyncContext", "isAsync", "callCache", "cached", "getCachedValueOrWait", "valid", "value", "cache", "CacheConfigurator", "handlerResult", "finishLock", "isIterableIterator", "onFirstPause", "setupAsyncLocks", "updateFunctionCache", "delete", "release", "getCachedValue", "cachedValue", "get", "waitFor", "promise", "config", "Lock", "configured", "forever", "deactivate", "mode", "set", "validator", "push", "constructor", "_active", "_never", "_forever", "_invalidate", "_configured", "_pairs", "_data", "simple", "makeSimpleConfigurator", "Error", "never", "using", "key", "fn", "maybe<PERSON><PERSON>", "isThenable", "then", "invalidate", "pairs", "cacheFn", "val", "assertSimpleType", "cb", "released", "_resolve", "Promise", "resolve"], "sources": ["../../src/config/caching.ts"], "sourcesContent": ["import gensync from \"gensync\";\nimport type { Handler } from \"gensync\";\nimport {\n  maybeAsync,\n  isAsync,\n  onFirstPause,\n  waitFor,\n  isThenable,\n} from \"../gensync-utils/async\";\nimport { isIterableIterator } from \"./util\";\n\nexport type { CacheConfigurator };\n\nexport type SimpleCacheConfigurator = {\n  (forever: boolean): void;\n  <T>(handler: () => T): T;\n\n  forever: () => void;\n  never: () => void;\n  using: <T>(handler: () => T) => T;\n  invalidate: <T>(handler: () => T) => T;\n};\n\nexport type CacheEntry<ResultT, SideChannel> = Array<{\n  value: ResultT;\n  valid: (channel: SideChannel) => Handler<boolean>;\n}>;\n\nconst synchronize = <ArgsT extends unknown[], ResultT>(\n  gen: (...args: ArgsT) => Handler<ResultT>,\n): ((...args: ArgsT) => ResultT) => {\n  return gensync(gen).sync;\n};\n\n// eslint-disable-next-line require-yield\nfunction* genTrue() {\n  return true;\n}\n\nexport function makeWeakCache<ArgT extends object, ResultT, SideChannel>(\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  return makeCachedFunction<ArgT, ResultT, SideChannel>(WeakMap, handler);\n}\n\nexport function makeWeakCacheSync<ArgT extends object, ResultT, SideChannel>(\n  handler: (arg: ArgT, cache?: CacheConfigurator<SideChannel>) => ResultT,\n): (arg: ArgT, data?: SideChannel) => ResultT {\n  return synchronize<[ArgT, SideChannel], ResultT>(\n    makeWeakCache<ArgT, ResultT, SideChannel>(handler),\n  );\n}\n\nexport function makeStrongCache<ArgT, ResultT, SideChannel>(\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  return makeCachedFunction<ArgT, ResultT, SideChannel>(Map, handler);\n}\n\nexport function makeStrongCacheSync<ArgT, ResultT, SideChannel>(\n  handler: (arg: ArgT, cache?: CacheConfigurator<SideChannel>) => ResultT,\n): (arg: ArgT, data?: SideChannel) => ResultT {\n  return synchronize<[ArgT, SideChannel], ResultT>(\n    makeStrongCache<ArgT, ResultT, SideChannel>(handler),\n  );\n}\n\n/* NOTE: Part of the logic explained in this comment is explained in the\n *       getCachedValueOrWait and setupAsyncLocks functions.\n *\n * > There are only two hard things in Computer Science: cache invalidation and naming things.\n * > -- Phil Karlton\n *\n * I don't know if Phil was also thinking about handling a cache whose invalidation function is\n * defined asynchronously is considered, but it is REALLY hard to do correctly.\n *\n * The implemented logic (only when gensync is run asynchronously) is the following:\n *   1. If there is a valid cache associated to the current \"arg\" parameter,\n *       a. RETURN the cached value\n *   3. If there is a FinishLock associated to the current \"arg\" parameter representing a valid cache,\n *       a. Wait for that lock to be released\n *       b. RETURN the value associated with that lock\n *   5. Start executing the function to be cached\n *       a. If it pauses on a promise, then\n *           i. Let FinishLock be a new lock\n *          ii. Store FinishLock as associated to the current \"arg\" parameter\n *         iii. Wait for the function to finish executing\n *          iv. Release FinishLock\n *           v. Send the function result to anyone waiting on FinishLock\n *   6. Store the result in the cache\n *   7. RETURN the result\n */\nfunction makeCachedFunction<ArgT, ResultT, SideChannel>(\n  CallCache: new <Cached>() => CacheMap<ArgT, Cached, SideChannel>,\n  handler: (\n    arg: ArgT,\n    cache: CacheConfigurator<SideChannel>,\n  ) => Handler<ResultT> | ResultT,\n): (arg: ArgT, data: SideChannel) => Handler<ResultT> {\n  const callCacheSync = new CallCache<ResultT>();\n  const callCacheAsync = new CallCache<ResultT>();\n  const futureCache = new CallCache<Lock<ResultT>>();\n\n  return function* cachedFunction(arg: ArgT, data: SideChannel) {\n    const asyncContext = yield* isAsync();\n    const callCache = asyncContext ? callCacheAsync : callCacheSync;\n\n    const cached = yield* getCachedValueOrWait<ArgT, ResultT, SideChannel>(\n      asyncContext,\n      callCache,\n      futureCache,\n      arg,\n      data,\n    );\n    if (cached.valid) return cached.value;\n\n    const cache = new CacheConfigurator(data);\n\n    const handlerResult: Handler<ResultT> | ResultT = handler(arg, cache);\n\n    let finishLock: Lock<ResultT>;\n    let value: ResultT;\n\n    if (isIterableIterator(handlerResult)) {\n      value = yield* onFirstPause(handlerResult, () => {\n        finishLock = setupAsyncLocks(cache, futureCache, arg);\n      });\n    } else {\n      value = handlerResult;\n    }\n\n    updateFunctionCache(callCache, cache, arg, value);\n\n    if (finishLock) {\n      futureCache.delete(arg);\n      finishLock.release(value);\n    }\n\n    return value;\n  };\n}\n\ntype CacheMap<ArgT, ResultT, SideChannel> =\n  | Map<ArgT, CacheEntry<ResultT, SideChannel>>\n  // @ts-expect-error todo(flow->ts): add `extends object` constraint to ArgT\n  | WeakMap<ArgT, CacheEntry<ResultT, SideChannel>>;\n\nfunction* getCachedValue<ArgT, ResultT, SideChannel>(\n  cache: CacheMap<ArgT, ResultT, SideChannel>,\n  arg: ArgT,\n  data: SideChannel,\n): Handler<{ valid: true; value: ResultT } | { valid: false; value: null }> {\n  const cachedValue: CacheEntry<ResultT, SideChannel> | void = cache.get(arg);\n\n  if (cachedValue) {\n    for (const { value, valid } of cachedValue) {\n      if (yield* valid(data)) return { valid: true, value };\n    }\n  }\n\n  return { valid: false, value: null };\n}\n\nfunction* getCachedValueOrWait<ArgT, ResultT, SideChannel>(\n  asyncContext: boolean,\n  callCache: CacheMap<ArgT, ResultT, SideChannel>,\n  futureCache: CacheMap<ArgT, Lock<ResultT>, SideChannel>,\n  arg: ArgT,\n  data: SideChannel,\n): Handler<{ valid: true; value: ResultT } | { valid: false; value: null }> {\n  const cached = yield* getCachedValue(callCache, arg, data);\n  if (cached.valid) {\n    return cached;\n  }\n\n  if (asyncContext) {\n    const cached = yield* getCachedValue(futureCache, arg, data);\n    if (cached.valid) {\n      const value = yield* waitFor<ResultT>(cached.value.promise);\n      return { valid: true, value };\n    }\n  }\n\n  return { valid: false, value: null };\n}\n\nfunction setupAsyncLocks<ArgT, ResultT, SideChannel>(\n  config: CacheConfigurator<SideChannel>,\n  futureCache: CacheMap<ArgT, Lock<ResultT>, SideChannel>,\n  arg: ArgT,\n): Lock<ResultT> {\n  const finishLock = new Lock<ResultT>();\n\n  updateFunctionCache(futureCache, config, arg, finishLock);\n\n  return finishLock;\n}\n\nfunction updateFunctionCache<\n  ArgT,\n  ResultT,\n  SideChannel,\n  Cache extends CacheMap<ArgT, ResultT, SideChannel>,\n>(\n  cache: Cache,\n  config: CacheConfigurator<SideChannel>,\n  arg: ArgT,\n  value: ResultT,\n) {\n  if (!config.configured()) config.forever();\n\n  let cachedValue: CacheEntry<ResultT, SideChannel> | void = cache.get(arg);\n\n  config.deactivate();\n\n  switch (config.mode()) {\n    case \"forever\":\n      cachedValue = [{ value, valid: genTrue }];\n      cache.set(arg, cachedValue);\n      break;\n    case \"invalidate\":\n      cachedValue = [{ value, valid: config.validator() }];\n      cache.set(arg, cachedValue);\n      break;\n    case \"valid\":\n      if (cachedValue) {\n        cachedValue.push({ value, valid: config.validator() });\n      } else {\n        cachedValue = [{ value, valid: config.validator() }];\n        cache.set(arg, cachedValue);\n      }\n  }\n}\n\nclass CacheConfigurator<SideChannel = void> {\n  _active: boolean = true;\n  _never: boolean = false;\n  _forever: boolean = false;\n  _invalidate: boolean = false;\n\n  _configured: boolean = false;\n\n  _pairs: Array<\n    [cachedValue: unknown, handler: (data: SideChannel) => Handler<unknown>]\n  > = [];\n\n  _data: SideChannel;\n\n  constructor(data: SideChannel) {\n    this._data = data;\n  }\n\n  simple() {\n    return makeSimpleConfigurator(this);\n  }\n\n  mode() {\n    if (this._never) return \"never\";\n    if (this._forever) return \"forever\";\n    if (this._invalidate) return \"invalidate\";\n    return \"valid\";\n  }\n\n  forever() {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._never) {\n      throw new Error(\"Caching has already been configured with .never()\");\n    }\n    this._forever = true;\n    this._configured = true;\n  }\n\n  never() {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._forever) {\n      throw new Error(\"Caching has already been configured with .forever()\");\n    }\n    this._never = true;\n    this._configured = true;\n  }\n\n  using<T>(handler: (data: SideChannel) => T): T {\n    if (!this._active) {\n      throw new Error(\"Cannot change caching after evaluation has completed.\");\n    }\n    if (this._never || this._forever) {\n      throw new Error(\n        \"Caching has already been configured with .never or .forever()\",\n      );\n    }\n    this._configured = true;\n\n    const key = handler(this._data);\n\n    const fn = maybeAsync(\n      handler,\n      `You appear to be using an async cache handler, but Babel has been called synchronously`,\n    );\n\n    if (isThenable(key)) {\n      // @ts-expect-error todo(flow->ts): improve function return type annotation\n      return key.then((key: unknown) => {\n        this._pairs.push([key, fn]);\n        return key;\n      });\n    }\n\n    this._pairs.push([key, fn]);\n    return key;\n  }\n\n  invalidate<T>(handler: (data: SideChannel) => T): T {\n    this._invalidate = true;\n    return this.using(handler);\n  }\n\n  validator(): (data: SideChannel) => Handler<boolean> {\n    const pairs = this._pairs;\n    return function* (data: SideChannel) {\n      for (const [key, fn] of pairs) {\n        if (key !== (yield* fn(data))) return false;\n      }\n      return true;\n    };\n  }\n\n  deactivate() {\n    this._active = false;\n  }\n\n  configured() {\n    return this._configured;\n  }\n}\n\nfunction makeSimpleConfigurator(\n  cache: CacheConfigurator<any>,\n): SimpleCacheConfigurator {\n  function cacheFn(val: any) {\n    if (typeof val === \"boolean\") {\n      if (val) cache.forever();\n      else cache.never();\n      return;\n    }\n\n    return cache.using(() => assertSimpleType(val()));\n  }\n  cacheFn.forever = () => cache.forever();\n  cacheFn.never = () => cache.never();\n  cacheFn.using = (cb: { (): SimpleType }) =>\n    cache.using(() => assertSimpleType(cb()));\n  cacheFn.invalidate = (cb: { (): SimpleType }) =>\n    cache.invalidate(() => assertSimpleType(cb()));\n\n  return cacheFn as any;\n}\n\n// Types are limited here so that in the future these values can be used\n// as part of Babel's caching logic.\nexport type SimpleType =\n  | string\n  | boolean\n  | number\n  | null\n  | void\n  | Promise<SimpleType>;\nexport function assertSimpleType(value: unknown): SimpleType {\n  if (isThenable(value)) {\n    throw new Error(\n      `You appear to be using an async cache handler, ` +\n        `which your current version of Babel does not support. ` +\n        `We may add support for this in the future, ` +\n        `but if you're on the most recent version of @babel/core and still ` +\n        `seeing this error, then you'll need to synchronously handle your caching logic.`,\n    );\n  }\n\n  if (\n    value != null &&\n    typeof value !== \"string\" &&\n    typeof value !== \"boolean\" &&\n    typeof value !== \"number\"\n  ) {\n    throw new Error(\n      \"Cache keys must be either string, boolean, number, null, or undefined.\",\n    );\n  }\n  // @ts-expect-error Type 'unknown' is not assignable to type 'SimpleType'. This can be removed\n  // when strictNullCheck is enabled\n  return value;\n}\n\nclass Lock<T> {\n  released: boolean = false;\n  promise: Promise<T>;\n  _resolve: (value: T) => void;\n\n  constructor() {\n    this.promise = new Promise(resolve => {\n      this._resolve = resolve;\n    });\n  }\n\n  release(value: T) {\n    this.released = true;\n    this._resolve(value);\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAOA;;AAmBA,MAAMA,WAAW,GACfC,GADkB,IAEgB;EAClC,OAAOC,UAAA,CAAQD,GAAR,EAAaE,IAApB;AACD,CAJD;;AAOA,UAAUC,OAAV,GAAoB;EAClB,OAAO,IAAP;AACD;;AAEM,SAASC,aAAT,CACLC,OADK,EAK+C;EACpD,OAAOC,kBAAkB,CAA6BC,OAA7B,EAAsCF,OAAtC,CAAzB;AACD;;AAEM,SAASG,iBAAT,CACLH,OADK,EAEuC;EAC5C,OAAON,WAAW,CAChBK,aAAa,CAA6BC,OAA7B,CADG,CAAlB;AAGD;;AAEM,SAASI,eAAT,CACLJ,OADK,EAK+C;EACpD,OAAOC,kBAAkB,CAA6BI,GAA7B,EAAkCL,OAAlC,CAAzB;AACD;;AAEM,SAASM,mBAAT,CACLN,OADK,EAEuC;EAC5C,OAAON,WAAW,CAChBU,eAAe,CAA6BJ,OAA7B,CADC,CAAlB;AAGD;;AA2BD,SAASC,kBAAT,CACEM,SADF,EAEEP,OAFF,EAMsD;EACpD,MAAMQ,aAAa,GAAG,IAAID,SAAJ,EAAtB;EACA,MAAME,cAAc,GAAG,IAAIF,SAAJ,EAAvB;EACA,MAAMG,WAAW,GAAG,IAAIH,SAAJ,EAApB;EAEA,OAAO,UAAUI,cAAV,CAAyBC,GAAzB,EAAoCC,IAApC,EAAuD;IAC5D,MAAMC,YAAY,GAAG,OAAO,IAAAC,cAAA,GAA5B;IACA,MAAMC,SAAS,GAAGF,YAAY,GAAGL,cAAH,GAAoBD,aAAlD;IAEA,MAAMS,MAAM,GAAG,OAAOC,oBAAoB,CACxCJ,YADwC,EAExCE,SAFwC,EAGxCN,WAHwC,EAIxCE,GAJwC,EAKxCC,IALwC,CAA1C;IAOA,IAAII,MAAM,CAACE,KAAX,EAAkB,OAAOF,MAAM,CAACG,KAAd;IAElB,MAAMC,KAAK,GAAG,IAAIC,iBAAJ,CAAsBT,IAAtB,CAAd;IAEA,MAAMU,aAAyC,GAAGvB,OAAO,CAACY,GAAD,EAAMS,KAAN,CAAzD;IAEA,IAAIG,UAAJ;IACA,IAAIJ,KAAJ;;IAEA,IAAI,IAAAK,wBAAA,EAAmBF,aAAnB,CAAJ,EAAuC;MACrCH,KAAK,GAAG,OAAO,IAAAM,mBAAA,EAAaH,aAAb,EAA4B,MAAM;QAC/CC,UAAU,GAAGG,eAAe,CAACN,KAAD,EAAQX,WAAR,EAAqBE,GAArB,CAA5B;MACD,CAFc,CAAf;IAGD,CAJD,MAIO;MACLQ,KAAK,GAAGG,aAAR;IACD;;IAEDK,mBAAmB,CAACZ,SAAD,EAAYK,KAAZ,EAAmBT,GAAnB,EAAwBQ,KAAxB,CAAnB;;IAEA,IAAII,UAAJ,EAAgB;MACdd,WAAW,CAACmB,MAAZ,CAAmBjB,GAAnB;MACAY,UAAU,CAACM,OAAX,CAAmBV,KAAnB;IACD;;IAED,OAAOA,KAAP;EACD,CApCD;AAqCD;;AAOD,UAAUW,cAAV,CACEV,KADF,EAEET,GAFF,EAGEC,IAHF,EAI4E;EAC1E,MAAMmB,WAAoD,GAAGX,KAAK,CAACY,GAAN,CAAUrB,GAAV,CAA7D;;EAEA,IAAIoB,WAAJ,EAAiB;IACf,KAAK,MAAM;MAAEZ,KAAF;MAASD;IAAT,CAAX,IAA+Ba,WAA/B,EAA4C;MAC1C,IAAI,OAAOb,KAAK,CAACN,IAAD,CAAhB,EAAwB,OAAO;QAAEM,KAAK,EAAE,IAAT;QAAeC;MAAf,CAAP;IACzB;EACF;;EAED,OAAO;IAAED,KAAK,EAAE,KAAT;IAAgBC,KAAK,EAAE;EAAvB,CAAP;AACD;;AAED,UAAUF,oBAAV,CACEJ,YADF,EAEEE,SAFF,EAGEN,WAHF,EAIEE,GAJF,EAKEC,IALF,EAM4E;EAC1E,MAAMI,MAAM,GAAG,OAAOc,cAAc,CAACf,SAAD,EAAYJ,GAAZ,EAAiBC,IAAjB,CAApC;;EACA,IAAII,MAAM,CAACE,KAAX,EAAkB;IAChB,OAAOF,MAAP;EACD;;EAED,IAAIH,YAAJ,EAAkB;IAChB,MAAMG,MAAM,GAAG,OAAOc,cAAc,CAACrB,WAAD,EAAcE,GAAd,EAAmBC,IAAnB,CAApC;;IACA,IAAII,MAAM,CAACE,KAAX,EAAkB;MAChB,MAAMC,KAAK,GAAG,OAAO,IAAAc,cAAA,EAAiBjB,MAAM,CAACG,KAAP,CAAae,OAA9B,CAArB;MACA,OAAO;QAAEhB,KAAK,EAAE,IAAT;QAAeC;MAAf,CAAP;IACD;EACF;;EAED,OAAO;IAAED,KAAK,EAAE,KAAT;IAAgBC,KAAK,EAAE;EAAvB,CAAP;AACD;;AAED,SAASO,eAAT,CACES,MADF,EAEE1B,WAFF,EAGEE,GAHF,EAIiB;EACf,MAAMY,UAAU,GAAG,IAAIa,IAAJ,EAAnB;EAEAT,mBAAmB,CAAClB,WAAD,EAAc0B,MAAd,EAAsBxB,GAAtB,EAA2BY,UAA3B,CAAnB;EAEA,OAAOA,UAAP;AACD;;AAED,SAASI,mBAAT,CAMEP,KANF,EAOEe,MAPF,EAQExB,GARF,EASEQ,KATF,EAUE;EACA,IAAI,CAACgB,MAAM,CAACE,UAAP,EAAL,EAA0BF,MAAM,CAACG,OAAP;EAE1B,IAAIP,WAAoD,GAAGX,KAAK,CAACY,GAAN,CAAUrB,GAAV,CAA3D;EAEAwB,MAAM,CAACI,UAAP;;EAEA,QAAQJ,MAAM,CAACK,IAAP,EAAR;IACE,KAAK,SAAL;MACET,WAAW,GAAG,CAAC;QAAEZ,KAAF;QAASD,KAAK,EAAErB;MAAhB,CAAD,CAAd;MACAuB,KAAK,CAACqB,GAAN,CAAU9B,GAAV,EAAeoB,WAAf;MACA;;IACF,KAAK,YAAL;MACEA,WAAW,GAAG,CAAC;QAAEZ,KAAF;QAASD,KAAK,EAAEiB,MAAM,CAACO,SAAP;MAAhB,CAAD,CAAd;MACAtB,KAAK,CAACqB,GAAN,CAAU9B,GAAV,EAAeoB,WAAf;MACA;;IACF,KAAK,OAAL;MACE,IAAIA,WAAJ,EAAiB;QACfA,WAAW,CAACY,IAAZ,CAAiB;UAAExB,KAAF;UAASD,KAAK,EAAEiB,MAAM,CAACO,SAAP;QAAhB,CAAjB;MACD,CAFD,MAEO;QACLX,WAAW,GAAG,CAAC;UAAEZ,KAAF;UAASD,KAAK,EAAEiB,MAAM,CAACO,SAAP;QAAhB,CAAD,CAAd;QACAtB,KAAK,CAACqB,GAAN,CAAU9B,GAAV,EAAeoB,WAAf;MACD;;EAfL;AAiBD;;AAED,MAAMV,iBAAN,CAA4C;EAc1CuB,WAAW,CAAChC,IAAD,EAAoB;IAAA,KAb/BiC,OAa+B,GAbZ,IAaY;IAAA,KAZ/BC,MAY+B,GAZb,KAYa;IAAA,KAX/BC,QAW+B,GAXX,KAWW;IAAA,KAV/BC,WAU+B,GAVR,KAUQ;IAAA,KAR/BC,WAQ+B,GARR,KAQQ;IAAA,KAN/BC,MAM+B,GAJ3B,EAI2B;IAAA,KAF/BC,KAE+B;IAC7B,KAAKA,KAAL,GAAavC,IAAb;EACD;;EAEDwC,MAAM,GAAG;IACP,OAAOC,sBAAsB,CAAC,IAAD,CAA7B;EACD;;EAEDb,IAAI,GAAG;IACL,IAAI,KAAKM,MAAT,EAAiB,OAAO,OAAP;IACjB,IAAI,KAAKC,QAAT,EAAmB,OAAO,SAAP;IACnB,IAAI,KAAKC,WAAT,EAAsB,OAAO,YAAP;IACtB,OAAO,OAAP;EACD;;EAEDV,OAAO,GAAG;IACR,IAAI,CAAC,KAAKO,OAAV,EAAmB;MACjB,MAAM,IAAIS,KAAJ,CAAU,uDAAV,CAAN;IACD;;IACD,IAAI,KAAKR,MAAT,EAAiB;MACf,MAAM,IAAIQ,KAAJ,CAAU,mDAAV,CAAN;IACD;;IACD,KAAKP,QAAL,GAAgB,IAAhB;IACA,KAAKE,WAAL,GAAmB,IAAnB;EACD;;EAEDM,KAAK,GAAG;IACN,IAAI,CAAC,KAAKV,OAAV,EAAmB;MACjB,MAAM,IAAIS,KAAJ,CAAU,uDAAV,CAAN;IACD;;IACD,IAAI,KAAKP,QAAT,EAAmB;MACjB,MAAM,IAAIO,KAAJ,CAAU,qDAAV,CAAN;IACD;;IACD,KAAKR,MAAL,GAAc,IAAd;IACA,KAAKG,WAAL,GAAmB,IAAnB;EACD;;EAEDO,KAAK,CAAIzD,OAAJ,EAA0C;IAC7C,IAAI,CAAC,KAAK8C,OAAV,EAAmB;MACjB,MAAM,IAAIS,KAAJ,CAAU,uDAAV,CAAN;IACD;;IACD,IAAI,KAAKR,MAAL,IAAe,KAAKC,QAAxB,EAAkC;MAChC,MAAM,IAAIO,KAAJ,CACJ,+DADI,CAAN;IAGD;;IACD,KAAKL,WAAL,GAAmB,IAAnB;IAEA,MAAMQ,GAAG,GAAG1D,OAAO,CAAC,KAAKoD,KAAN,CAAnB;IAEA,MAAMO,EAAE,GAAG,IAAAC,iBAAA,EACT5D,OADS,EAER,wFAFQ,CAAX;;IAKA,IAAI,IAAA6D,iBAAA,EAAWH,GAAX,CAAJ,EAAqB;MAEnB,OAAOA,GAAG,CAACI,IAAJ,CAAUJ,GAAD,IAAkB;QAChC,KAAKP,MAAL,CAAYP,IAAZ,CAAiB,CAACc,GAAD,EAAMC,EAAN,CAAjB;;QACA,OAAOD,GAAP;MACD,CAHM,CAAP;IAID;;IAED,KAAKP,MAAL,CAAYP,IAAZ,CAAiB,CAACc,GAAD,EAAMC,EAAN,CAAjB;;IACA,OAAOD,GAAP;EACD;;EAEDK,UAAU,CAAI/D,OAAJ,EAA0C;IAClD,KAAKiD,WAAL,GAAmB,IAAnB;IACA,OAAO,KAAKQ,KAAL,CAAWzD,OAAX,CAAP;EACD;;EAED2C,SAAS,GAA4C;IACnD,MAAMqB,KAAK,GAAG,KAAKb,MAAnB;IACA,OAAO,WAAWtC,IAAX,EAA8B;MACnC,KAAK,MAAM,CAAC6C,GAAD,EAAMC,EAAN,CAAX,IAAwBK,KAAxB,EAA+B;QAC7B,IAAIN,GAAG,MAAM,OAAOC,EAAE,CAAC9C,IAAD,CAAf,CAAP,EAA+B,OAAO,KAAP;MAChC;;MACD,OAAO,IAAP;IACD,CALD;EAMD;;EAED2B,UAAU,GAAG;IACX,KAAKM,OAAL,GAAe,KAAf;EACD;;EAEDR,UAAU,GAAG;IACX,OAAO,KAAKY,WAAZ;EACD;;AAtGyC;;AAyG5C,SAASI,sBAAT,CACEjC,KADF,EAE2B;EACzB,SAAS4C,OAAT,CAAiBC,GAAjB,EAA2B;IACzB,IAAI,OAAOA,GAAP,KAAe,SAAnB,EAA8B;MAC5B,IAAIA,GAAJ,EAAS7C,KAAK,CAACkB,OAAN,GAAT,KACKlB,KAAK,CAACmC,KAAN;MACL;IACD;;IAED,OAAOnC,KAAK,CAACoC,KAAN,CAAY,MAAMU,gBAAgB,CAACD,GAAG,EAAJ,CAAlC,CAAP;EACD;;EACDD,OAAO,CAAC1B,OAAR,GAAkB,MAAMlB,KAAK,CAACkB,OAAN,EAAxB;;EACA0B,OAAO,CAACT,KAAR,GAAgB,MAAMnC,KAAK,CAACmC,KAAN,EAAtB;;EACAS,OAAO,CAACR,KAAR,GAAiBW,EAAD,IACd/C,KAAK,CAACoC,KAAN,CAAY,MAAMU,gBAAgB,CAACC,EAAE,EAAH,CAAlC,CADF;;EAEAH,OAAO,CAACF,UAAR,GAAsBK,EAAD,IACnB/C,KAAK,CAAC0C,UAAN,CAAiB,MAAMI,gBAAgB,CAACC,EAAE,EAAH,CAAvC,CADF;;EAGA,OAAOH,OAAP;AACD;;AAWM,SAASE,gBAAT,CAA0B/C,KAA1B,EAAsD;EAC3D,IAAI,IAAAyC,iBAAA,EAAWzC,KAAX,CAAJ,EAAuB;IACrB,MAAM,IAAImC,KAAJ,CACH,iDAAD,GACG,wDADH,GAEG,6CAFH,GAGG,oEAHH,GAIG,iFALC,CAAN;EAOD;;EAED,IACEnC,KAAK,IAAI,IAAT,IACA,OAAOA,KAAP,KAAiB,QADjB,IAEA,OAAOA,KAAP,KAAiB,SAFjB,IAGA,OAAOA,KAAP,KAAiB,QAJnB,EAKE;IACA,MAAM,IAAImC,KAAJ,CACJ,wEADI,CAAN;EAGD;;EAGD,OAAOnC,KAAP;AACD;;AAED,MAAMiB,IAAN,CAAc;EAKZQ,WAAW,GAAG;IAAA,KAJdwB,QAIc,GAJM,KAIN;IAAA,KAHdlC,OAGc;IAAA,KAFdmC,QAEc;IACZ,KAAKnC,OAAL,GAAe,IAAIoC,OAAJ,CAAYC,OAAO,IAAI;MACpC,KAAKF,QAAL,GAAgBE,OAAhB;IACD,CAFc,CAAf;EAGD;;EAED1C,OAAO,CAACV,KAAD,EAAW;IAChB,KAAKiD,QAAL,GAAgB,IAAhB;;IACA,KAAKC,QAAL,CAAclD,KAAd;EACD;;AAdW"}