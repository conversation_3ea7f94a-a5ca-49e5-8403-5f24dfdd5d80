import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Terminal as TerminalIcon, 
  AlertCircle, 
  FileOutput, 
  Bug,
  X,
  Maximize2,
  Minimize2
} from 'lucide-react';
import Terminal from '../Terminal'; // Import existing terminal

interface EnhancedPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  height: number;
}

const EnhancedPanel: React.FC<EnhancedPanelProps> = ({ 
  isOpen, 
  onToggle, 
  height 
}) => {
  const [activeTab, setActiveTab] = useState('terminal');
  const [isMaximized, setIsMaximized] = useState(false);

  const problems = [
    {
      type: 'error',
      message: 'Cannot find module \'@/components/ui/button\'',
      file: 'App.tsx',
      line: 5,
      column: 23
    },
    {
      type: 'warning',
      message: 'Unused variable \'useState\'',
      file: 'Component.tsx',
      line: 1,
      column: 10
    }
  ];

  const outputs = [
    { timestamp: '14:30:25', level: 'info', message: 'Development server started' },
    { timestamp: '14:30:26', level: 'info', message: 'Compiled successfully' },
    { timestamp: '14:30:30', level: 'warn', message: 'Warning: React Hook useEffect has a missing dependency' }
  ];

  if (!isOpen) return null;

  return (
    <div 
      className={`enhanced-panel bg-surface-primary border-t border-surface-tertiary ${
        isMaximized ? 'h-full' : ''
      }`}
      style={{ height: isMaximized ? '100%' : `${height}px` }}
    >
      <div className="panel-header flex items-center justify-between px-4 py-1 border-b border-surface-tertiary">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
          <TabsList className="bg-transparent h-8">
            <TabsTrigger value="terminal" className="text-xs px-3 py-1">
              <TerminalIcon size={14} className="mr-1" />
              Terminal
            </TabsTrigger>
            <TabsTrigger value="problems" className="text-xs px-3 py-1">
              <AlertCircle size={14} className="mr-1" />
              Problems
              <Badge variant="destructive" className="ml-1 text-xs">
                {problems.filter(p => p.type === 'error').length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="output" className="text-xs px-3 py-1">
              <FileOutput size={14} className="mr-1" />
              Output
            </TabsTrigger>
            <TabsTrigger value="debug" className="text-xs px-3 py-1">
              <Bug size={14} className="mr-1" />
              Debug Console
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setIsMaximized(!isMaximized)}
          >
            {isMaximized ? <Minimize2 size={12} /> : <Maximize2 size={12} />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={onToggle}
          >
            <X size={12} />
          </Button>
        </div>
      </div>

      <div className="panel-content flex-1 overflow-hidden">
        <Tabs value={activeTab} className="h-full">
          <TabsContent value="terminal" className="h-full m-0">
            <Terminal />
          </TabsContent>
          
          <TabsContent value="problems" className="h-full m-0">
            <ScrollArea className="h-full">
              <div className="p-4">
                <div className="space-y-2">
                  {problems.map((problem, index) => (
                    <div
                      key={index}
                      className={`flex items-start space-x-3 p-2 rounded hover:bg-surface-secondary cursor-pointer ${
                        problem.type === 'error' ? 'border-l-2 border-red-500' : 'border-l-2 border-yellow-500'
                      }`}
                    >
                      <AlertCircle 
                        size={16} 
                        className={problem.type === 'error' ? 'text-red-500' : 'text-yellow-500'} 
                      />
                      <div className="flex-1">
                        <p className="text-sm text-gray-200">{problem.message}</p>
                        <p className="text-xs text-gray-400">
                          {problem.file}:{problem.line}:{problem.column}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="output" className="h-full m-0">
            <ScrollArea className="h-full">
              <div className="p-4 code-font text-sm">
                {outputs.map((output, index) => (
                  <div key={index} className="flex items-start space-x-3 mb-1">
                    <span className="text-gray-500 text-xs">{output.timestamp}</span>
                    <span className={`text-xs uppercase font-medium ${
                      output.level === 'error' ? 'text-red-500' : 
                      output.level === 'warn' ? 'text-yellow-500' : 
                      'text-blue-500'
                    }`}>
                      {output.level}
                    </span>
                    <span className="text-gray-200">{output.message}</span>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="debug" className="h-full m-0">
            <div className="p-4 h-full flex items-center justify-center">
              <div className="text-center text-gray-400">
                <Bug size={48} className="mx-auto mb-2 opacity-50" />
                <p className="text-sm">Debug Console</p>
                <p className="text-xs">Start debugging to see console output</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EnhancedPanel;
