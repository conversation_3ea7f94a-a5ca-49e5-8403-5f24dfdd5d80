import { useState, useEffect } from "react";
import { IFile } from "../types";
import { open } from "@tauri-apps/api/dialog";
import NavFiles from "./NavFiles";
import { readDirectory } from "../helpers/filesys";
import Fuse, { Fu<PERSON>R<PERSON>ult } from "fuse.js";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { FolderOpen, Search, Plus, MoreHorizontal } from "lucide-react";

export default function Sidebar() {
  const [projectName, setProjectName] = useState("");
  const [files, setFiles] = useState<IFile[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [fuse, setFuse] = useState<Fuse<IFile> | null>(null);

  useEffect(() => {
    if (files.length > 0) {
      const fuseInstance = new Fuse(files, {
        keys: ["name", "path"],
        threshold: 0.3,
      });
      setFuse(fuseInstance);
    }
  }, [files]);

  const loadFile = async () => {
    const selected = await open({
      directory: true
    }) as string | null;

    if (!selected) return;

    setProjectName(selected);
    readDirectory(selected + '/').then(files => {
      setFiles(files);
    });
  };

  const filteredFiles = searchTerm && fuse ? fuse.search(searchTerm).map((result: FuseResult<IFile>) => result.item) : files;

  return (
    <DndProvider backend={HTML5Backend}>
      <aside id="sidebar" className="w-full h-full bg-surface-primary border-r border-surface-tertiary flex flex-col">
        <div className="sidebar-header p-3 border-b border-surface-tertiary">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Explorer</h3>
            <div className="flex space-x-1">
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-neon-purple/20">
                <Plus size={12} className="text-gray-400 hover:text-neon-purple" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 hover:bg-neon-purple/20">
                <MoreHorizontal size={12} className="text-gray-400 hover:text-neon-purple" />
              </Button>
            </div>
          </div>

          <Button
            onClick={loadFile}
            className="w-full mb-3 bg-neon-purple/20 hover:bg-neon-purple/30 border border-neon-purple/50 text-neon-purple"
            variant="outline"
          >
            <FolderOpen size={16} className="mr-2" />
            Open Folder
          </Button>

          {projectName && (
            <div className="mb-3">
              <Badge variant="outline" className="text-xs text-gray-400 border-gray-600">
                {projectName.split('/').pop() || projectName.split('\\').pop()}
              </Badge>
            </div>
          )}

          <div className="relative">
            <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              className="pl-9 bg-surface-secondary border-surface-tertiary text-gray-200 placeholder:text-gray-500 focus:border-neon-purple focus:ring-neon-purple/20"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-2">
            <NavFiles visible={true} files={filteredFiles}/>
          </div>
        </ScrollArea>
      </aside>
    </DndProvider>
  );
}
