{"version": 3, "names": [], "sources": ["../src/types.ts"], "sourcesContent": ["import type * as t from \"@babel/types\";\nimport type { NodePath } from \"./index\";\nimport type { VirtualTypeAliases } from \"./path/lib/virtual-types\";\n\nexport type Visitor<S = {}> = VisitNodeObject<S, t.Node> & {\n  [Type in t.Node[\"type\"]]?: VisitNode<S, Extract<t.Node, { type: Type }>>;\n} & {\n  [K in keyof t.Aliases]?: VisitNode<S, t.Aliases[K]>;\n} & {\n  [K in keyof VirtualTypeAliases]?: VisitNode<S, VirtualTypeAliases[K]>;\n} & {\n  [K in keyof InternalVisitorFlags]?: InternalVisitorFlags[K];\n} & {\n  // Babel supports `NodeTypesWithoutComment | NodeTypesWithoutComment | ... ` but it is\n  // too complex for TS. So we type it as a general visitor only if the key contains `|`\n  // this is good enough for non-visitor traverse options e.g. `noScope`\n  [k: `${string}|${string}`]: VisitNode<S, t.Node>;\n};\n\n/** @internal */\ntype InternalVisitorFlags = {\n  _exploded?: boolean;\n  _verified?: boolean;\n};\n\nexport type VisitNode<S, P extends t.Node> =\n  | VisitNodeFunction<S, P>\n  | VisitNodeObject<S, P>;\n\nexport type VisitNodeFunction<S, P extends t.Node> = (\n  this: S,\n  path: NodePath<P>,\n  state: S,\n) => void;\n\nexport interface VisitNodeObject<S, P extends t.Node> {\n  enter?: VisitNodeFunction<S, P>;\n  exit?: VisitNodeFunction<S, P>;\n}\n"], "mappings": ""}