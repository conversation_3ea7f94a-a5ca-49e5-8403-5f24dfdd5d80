"use strict";import{isAlternativeContainer as f,isQuantifiable as p}from"../../parser/node-utils.js";const d={"*"({node:e}){f(e)&&y(e)&&(e.body=e.body[0].body[0].body)},Group({node:e,parent:t,replaceWithMultiple:o}){const{atomic:r,body:a,flags:l}=e,n=a[0].body;if(a.length>1||t.type==="Quantifier")return;let i=!1;r?n.every(({type:u})=>s.has(u))&&(i=!0):l||(i=!0),i&&o(n,{traverse:!0})},Quantifier({node:e}){if(e.body.type!=="Group")return;const t=e.body;if(t.body.length>1)return;const o=t.body[0].body;if(o.length!==1)return;const r=o[0];!p(r)||t.atomic&&!s.has(r.type)||t.flags||(e.body=r)}},s=new Set(["Assertion","Backreference","Character","CharacterClass","CharacterSet","Directive","NamedCallout"]);function y({body:e}){const t=e[0].body;return e.length===1&&t.length===1&&t[0].type==="Group"&&!t[0].atomic&&!t[0].flags&&t[0].body.length>1}export{d as unwrapUselessGroups};
//# sourceMappingURL=unwrap-useless-groups.js.map
