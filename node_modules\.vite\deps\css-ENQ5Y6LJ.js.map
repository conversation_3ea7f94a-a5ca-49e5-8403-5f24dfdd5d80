{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/css/css.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/css/css.ts\nvar conf = {\n  wordPattern: /(#?-?\\d*\\.\\d\\w*%?)|((::|[@#.!:])?[\\w-?]+%?)|::|[@#.!:]/g,\n  comments: {\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#region\\\\b\\\\s*(.*?)\\\\s*\\\\*\\\\/\"),\n      end: new RegExp(\"^\\\\s*\\\\/\\\\*\\\\s*#endregion\\\\b.*\\\\*\\\\/\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".css\",\n  ws: \"[ \t\\n\\r\\f]*\",\n  identifier: \"-?-?([a-zA-Z]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))([\\\\w\\\\-]|(\\\\\\\\(([0-9a-fA-F]{1,6}\\\\s?)|[^[0-9a-fA-F])))*\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.bracket\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  tokenizer: {\n    root: [{ include: \"@selector\" }],\n    selector: [\n      { include: \"@comments\" },\n      { include: \"@import\" },\n      { include: \"@strings\" },\n      [\n        \"[@](keyframes|-webkit-keyframes|-moz-keyframes|-o-keyframes)\",\n        { token: \"keyword\", next: \"@keyframedeclaration\" }\n      ],\n      [\"[@](page|content|font-face|-moz-document)\", { token: \"keyword\" }],\n      [\"[@](charset|namespace)\", { token: \"keyword\", next: \"@declarationbody\" }],\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@selectorname\" },\n      [\"[\\\\*]\", \"tag\"],\n      [\"[>\\\\+,]\", \"delimiter\"],\n      [\"\\\\[\", { token: \"delimiter.bracket\", next: \"@selectorattribute\" }],\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }]\n    ],\n    selectorbody: [\n      { include: \"@comments\" },\n      [\"[*_]?@identifier@ws:(?=(\\\\s|\\\\d|[^{;}]*[;}]))\", \"attribute.name\", \"@rulevalue\"],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    selectorname: [\n      [\"(\\\\.|#(?=[^{])|%|(@identifier)|:)+\", \"tag\"]\n    ],\n    selectorattribute: [{ include: \"@term\" }, [\"]\", { token: \"delimiter.bracket\", next: \"@pop\" }]],\n    term: [\n      { include: \"@comments\" },\n      [\n        \"(url-prefix)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      [\n        \"(url)(\\\\()\",\n        [\"attribute.value\", { token: \"delimiter.parenthesis\", next: \"@urldeclaration\" }]\n      ],\n      { include: \"@functioninvocation\" },\n      { include: \"@numbers\" },\n      { include: \"@name\" },\n      { include: \"@strings\" },\n      [\"([<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,])\", \"delimiter\"],\n      [\",\", \"delimiter\"]\n    ],\n    rulevalue: [\n      { include: \"@comments\" },\n      { include: \"@strings\" },\n      { include: \"@term\" },\n      [\"!important\", \"keyword\"],\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n    ],\n    warndebug: [[\"[@](warn|debug)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    import: [[\"[@](import)\", { token: \"keyword\", next: \"@declarationbody\" }]],\n    urldeclaration: [\n      { include: \"@strings\" },\n      [\"[^)\\r\\n]+\", \"string\"],\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    parenthizedterm: [\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"delimiter.parenthesis\", next: \"@pop\" }]\n    ],\n    declarationbody: [\n      { include: \"@term\" },\n      [\";\", \"delimiter\", \"@pop\"],\n      [\"(?=})\", { token: \"\", next: \"@pop\" }]\n    ],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [/[^*/]+/, \"comment\"],\n      [/./, \"comment\"]\n    ],\n    name: [[\"@identifier\", \"attribute.value\"]],\n    numbers: [\n      [\"-?(\\\\d*\\\\.)?\\\\d+([eE][\\\\-+]?\\\\d+)?\", { token: \"attribute.value.number\", next: \"@units\" }],\n      [\"#[0-9a-fA-F_]+(?!\\\\w)\", \"attribute.value.hex\"]\n    ],\n    units: [\n      [\n        \"(em|ex|ch|rem|fr|vmin|vmax|vw|vh|vm|cm|mm|in|px|pt|pc|deg|grad|rad|turn|s|ms|Hz|kHz|%)?\",\n        \"attribute.value.unit\",\n        \"@pop\"\n      ]\n    ],\n    keyframedeclaration: [\n      [\"@identifier\", \"attribute.value\"],\n      [\"{\", { token: \"delimiter.bracket\", switchTo: \"@keyframebody\" }]\n    ],\n    keyframebody: [\n      { include: \"@term\" },\n      [\"{\", { token: \"delimiter.bracket\", next: \"@selectorbody\" }],\n      [\"}\", { token: \"delimiter.bracket\", next: \"@pop\" }]\n    ],\n    functioninvocation: [\n      [\"@identifier\\\\(\", { token: \"attribute.value\", next: \"@functionarguments\" }]\n    ],\n    functionarguments: [\n      [\"\\\\$@identifier@ws:\", \"attribute.name\"],\n      [\"[,]\", \"delimiter\"],\n      { include: \"@term\" },\n      [\"\\\\)\", { token: \"attribute.value\", next: \"@pop\" }]\n    ],\n    strings: [\n      ['~?\"', { token: \"string\", next: \"@stringenddoublequote\" }],\n      [\"~?'\", { token: \"string\", next: \"@stringendquote\" }]\n    ],\n    stringenddoublequote: [\n      [\"\\\\\\\\.\", \"string\"],\n      ['\"', { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\\"]+/, \"string\"],\n      [\".\", \"string\"]\n    ],\n    stringendquote: [\n      [\"\\\\\\\\.\", \"string\"],\n      [\"'\", { token: \"string\", next: \"@pop\" }],\n      [/[^\\\\']+/, \"string\"],\n      [\".\", \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,8CAA8C;AAAA,MAChE,KAAK,IAAI,OAAO,sCAAsC;AAAA,IACxD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,WAAW;AAAA,IACT,MAAM,CAAC,EAAE,SAAS,YAAY,CAAC;AAAA,IAC/B,UAAU;AAAA,MACR,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA,EAAE,OAAO,WAAW,MAAM,uBAAuB;AAAA,MACnD;AAAA,MACA,CAAC,6CAA6C,EAAE,OAAO,UAAU,CAAC;AAAA,MAClE,CAAC,0BAA0B,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC;AAAA,MACzE;AAAA,QACE;AAAA,QACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,MACjF;AAAA,MACA,EAAE,SAAS,gBAAgB;AAAA,MAC3B,CAAC,SAAS,KAAK;AAAA,MACf,CAAC,WAAW,WAAW;AAAA,MACvB,CAAC,OAAO,EAAE,OAAO,qBAAqB,MAAM,qBAAqB,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,gBAAgB,CAAC;AAAA,IAC7D;AAAA,IACA,cAAc;AAAA,MACZ,EAAE,SAAS,YAAY;AAAA,MACvB,CAAC,iDAAiD,kBAAkB,YAAY;AAAA,MAChF,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,sCAAsC,KAAK;AAAA,IAC9C;AAAA,IACA,mBAAmB,CAAC,EAAE,SAAS,QAAQ,GAAG,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC,CAAC;AAAA,IAC7F,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB;AAAA,QACE;AAAA,QACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,MACjF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,mBAAmB,EAAE,OAAO,yBAAyB,MAAM,kBAAkB,CAAC;AAAA,MACjF;AAAA,MACA,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,QAAQ;AAAA,MACnB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,iCAAiC,WAAW;AAAA,MAC7C,CAAC,KAAK,WAAW;AAAA,IACnB;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,cAAc,SAAS;AAAA,MACxB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA,IACvC;AAAA,IACA,WAAW,CAAC,CAAC,mBAAmB,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IAC/E,QAAQ,CAAC,CAAC,eAAe,EAAE,OAAO,WAAW,MAAM,mBAAmB,CAAC,CAAC;AAAA,IACxE,gBAAgB;AAAA,MACd,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,aAAa,QAAQ;AAAA,MACtB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IAC1D;AAAA,IACA,iBAAiB;AAAA,MACf,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,yBAAyB,MAAM,OAAO,CAAC;AAAA,IAC1D;AAAA,IACA,iBAAiB;AAAA,MACf,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,SAAS,EAAE,OAAO,IAAI,MAAM,OAAO,CAAC;AAAA,IACvC;AAAA,IACA,UAAU;AAAA,MACR,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,MAAM,CAAC,CAAC,eAAe,iBAAiB,CAAC;AAAA,IACzC,SAAS;AAAA,MACP,CAAC,sCAAsC,EAAE,OAAO,0BAA0B,MAAM,SAAS,CAAC;AAAA,MAC1F,CAAC,yBAAyB,qBAAqB;AAAA,IACjD;AAAA,IACA,OAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,eAAe,iBAAiB;AAAA,MACjC,CAAC,KAAK,EAAE,OAAO,qBAAqB,UAAU,gBAAgB,CAAC;AAAA,IACjE;AAAA,IACA,cAAc;AAAA,MACZ,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,gBAAgB,CAAC;AAAA,MAC3D,CAAC,KAAK,EAAE,OAAO,qBAAqB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,kBAAkB,EAAE,OAAO,mBAAmB,MAAM,qBAAqB,CAAC;AAAA,IAC7E;AAAA,IACA,mBAAmB;AAAA,MACjB,CAAC,sBAAsB,gBAAgB;AAAA,MACvC,CAAC,OAAO,WAAW;AAAA,MACnB,EAAE,SAAS,QAAQ;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,mBAAmB,MAAM,OAAO,CAAC;AAAA,IACpD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,wBAAwB,CAAC;AAAA,MAC1D,CAAC,OAAO,EAAE,OAAO,UAAU,MAAM,kBAAkB,CAAC;AAAA,IACtD;AAAA,IACA,sBAAsB;AAAA,MACpB,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACvC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,gBAAgB;AAAA,MACd,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,MACvC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;", "names": []}