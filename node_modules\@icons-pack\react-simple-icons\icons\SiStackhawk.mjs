import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';

const defaultColor = "#00CBC6";
const SiStackhawk = React.forwardRef(function SiStackhawk2({ title = "StackHawk", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsx("title", { children: title }),
        /* @__PURE__ */ jsx("path", { d: "M6.674 6.989c.11.092.207.178.298.255L12 4.34l4.971 2.867.298-.256-5.27-3.039-5.325 3.077Zm12.33 6.063c-.12.06-.244.114-.37.162v2.612l-3.315 1.911c.096.025.16.037.16.037-.11.148-.383.258-.607.33l.22.197 3.91-2.256.002-2.993Zm-6.165 10.51 1.182-.683-.682-.46-.179.102c-.086.353-.193.7-.321 1.04Zm-4.32-5.788s.065-.013.161-.037l-3.308-1.911v-2.572a3.963 3.963 0 0 1-.37-.149v2.935l3.997 2.308.238-.22c-.23-.059-.592-.18-.718-.354ZM5.36 5.788c.14.137.282.27.42.399L12 2.594l6.154 3.554c.139-.129.278-.262.42-.4L12 1.953 5.36 5.788Zm15.34 5.74a7.444 7.444 0 0 1-.553.648v4.523L15.95 19.12a11 11 0 0 1 .388.418l4.363-2.52v-5.491ZM4.689 5.116l7.389-4.26L19.3 5.023c.177-.186.346-.374.508-.562L12.075 0 4.172 4.563c.168.18.34.368.517.553ZM9.416 21.61l-6.992-4.037V8.205c-.25-.27-.501-.579-.74-.891V18l7.698 4.443c-.005-.26.01-.548.034-.834ZM21.732 7.913v9.659l-6.997 4.04c.025.287.038.575.033.836L22.474 18V6.965c-.232.318-.485.647-.742.948ZM9.625 20.028l-.235-.136-.793.186.934.54c.039-.246.07-.45.094-.59Zm-1.476-.854L3.86 16.7v-4.414a6.965 6.965 0 0 1-.552-.619v5.353l4.459 2.575c.118-.139.248-.28.382-.42Zm3.16 4.384a9.053 9.053 0 0 1-.319-1.042l-.175-.1-.682.462 1.176.68Zm3.342-3.69-.143.083c.022.123.057.332.096.587l.841-.486-.794-.183Zm-2.91-7.697c.111.238.234.483.348.732.11-.25.233-.494.349-.732.8-1.658 1.202-2.62.175-3.15l-.045-.035c-.168-.166-.332-.25-.479-.25a.711.711 0 0 0-.477.25l-.046.035c-1.026.53-.625 1.493.176 3.15Zm11.14-7.228c-2.374 3.884-7.401 5.72-7.401 5.72 8.85-5.24 8.518-10.51 8.518-10.51-3.172 5.97-8.499 9.199-10.452 10.231-.118.593-.441 1.261-.773 1.948-.17.354-.348.722-.51 1.104l-.172.396-.17-.396c-.162-.377-.34-.744-.511-1.104-.311-.647-.616-1.278-.75-1.843C8.891 9.584 3.292 6.341.002.152c0 0-.332 5.27 8.518 10.51 0 0-5.025-1.836-7.402-5.72 0 0 1.694 5.123 6.799 6.284 0 0-3.221-.043-5.052-1.94 0 0-.105 2.282 4.808 2.626 0 0-2.415.586-3.597-.488 0 0 .058 1.157 3.672 1.268 0 0 1.593-.111 2.606.597-.033 1.296.27 2.64.574 3.645-.053.036-.069.293-1.866.86 0 .029 1.152.309 1.713-.22.12.127-1.305.516-2.642 2.188.016-.066 1.749-.631 2.97-1.177.014-.007-.901.904-1.088 3.617.032-.01.566-.884 1.406-1.857-.06 1.252.164 2.5.65 3.655a8.317 8.317 0 0 0 .653-3.494c.762.907 1.24 1.686 1.27 1.696-.185-2.713-1.104-3.624-1.089-3.617 1.216.546 2.956 1.106 2.971 1.177-1.335-1.672-2.771-2.061-2.643-2.187.56.526 1.717.246 1.713.22-1.818-.574-1.813-.829-1.868-.861.305-1.002.61-2.347.58-3.646 1.015-.704 2.597-.59 2.597-.59 3.614-.11 3.672-1.268 3.672-1.268-1.182 1.074-3.597.488-3.597.488 4.913-.343 4.808-2.625 4.808-2.625-1.834 1.89-5.05 1.933-5.05 1.933 5.1-1.16 6.793-6.283 6.793-6.283Z" })
      ]
    }
  );
});

export { SiStackhawk as default, defaultColor };
