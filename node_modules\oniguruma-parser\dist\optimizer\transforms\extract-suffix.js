"use strict";import{isAlternativeContainer as h}from"../../parser/node-utils.js";import{createAlternative as p,createGroup as a}from"../../parser/parse.js";import{isAllowedSimpleNode as c,isNodeEqual as u}from"./extract-prefix.js";const g={"*"({node:e}){if(!h(e)||e.body.length<2)return;const y=e.body[0].body,t=[];let f=!1,s=0;for(;!f;){const o=y.length-1-s;t.push(y[o]);for(const i of e.body){const n=i.body.length-1-s,r=i.body[n];if(!r||!c(r)||!u(r,t[s])){f=!0;break}}s++}if(t.pop(),!t.length||t.length<3&&t[0].type!=="Assertion"&&t.length*(e.body.length-1)<4&&!e.body.some((o,i,n)=>{const r=n[i-1],d=t.length;return o.body.length-d<2&&r&&r.body.length-d<2}))return;t.reverse();for(const o of e.body)o.body=o.body.slice(0,-t.length);const l=p(),b=a({body:e.body});b.body.every(o=>!o.body.length)||l.body.push(b),l.body.push(...t),e.body=[l]}};export{g as extractSuffix};
//# sourceMappingURL=extract-suffix.js.map
