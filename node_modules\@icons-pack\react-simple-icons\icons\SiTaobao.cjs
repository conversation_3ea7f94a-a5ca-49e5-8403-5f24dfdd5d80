'use strict';

Object.defineProperties(exports, { __esModule: { value: true }, [Symbol.toStringTag]: { value: 'Module' } });

const jsxRuntime = require('react/jsx-runtime');
const React = require('react');

function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: 'Module' } });
  if (e) {
    for (const k in e) {
      if (k !== 'default') {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}

const React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);

const defaultColor = "#E94F20";
const SiTaobao = React__namespace.forwardRef(function SiTaobao2({ title = "Taobao", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxRuntime.jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsxRuntime.jsx("title", { children: title }),
        /* @__PURE__ */ jsxRuntime.jsx("path", { d: "M21.3099 9.9008c.5285 0 .9584.4284.9584.9589 0 .5276-.4299.958-.9584.958-.5276 0-.9585-.4304-.9585-.958 0-.5305.4309-.959.9585-.959zm2.3899 3.0462h-10.408v-.9595h4.15V9.7591h-2.8869v-.768h2.8868v-.9234h-2.508v.2034h-1.6418V5.3733h1.6418v.3497c.4945-.0607 1.463-.1814 2.5175-.2956v-.8257h1.8522v.6408c.9249-.0807 1.753-.1312 2.211-.1112 1.489.0716 2.4449.2816 2.485 1.273.0356.989-1.4244 1.9043-1.4244 1.9043l-.4509-.4338v.1929h-2.8116v.9233h3.229v.768h-3.229v2.2285h4.3873v.9595zM21.5259 7.299l-.0115-.0115s1.3722-.7595.3427-1.272c-.8633-.43-5.5346.3056-6.9234.6257v.6578h6.5922zM1.8822 6.4166c.5515 0 .9985-.449.9985-1 0-.5531-.447-.9995-.9985-.9995a.9984.9984 0 00-1.001.9995c0 .551.4463 1 1.001 1zm3.4094-.8596c.252-.4364.3717-.7195.3717-.7195l-1.466-.4123S3.6068 6.3546 2.5527 7.253c0 0 1.0195.5897 1.0095.5732a9.6444 9.6444 0 00.782-.8793c.2345-.1017.4585-.198.6794-.2876-.2715.487-.7094 1.219-1.1478 1.6809l.6178.5385s.4198-.4033.8792-.8907h.5246v.8993H3.8557v.7204h2.0416v1.7235c-.025 0-.0521 0-.0782-.002-.224-.0106-.5751-.0476-.7124-.265-.1678-.2636-.044-.7496-.0346-1.0457H3.6608l-.0496.026s-.517 2.3142 1.489 2.2621c1.8793.0521 2.9544-.523 3.4725-.9178l.2064.7645 1.1583-.4825-.785-1.9183-.941.292.1764.6574c-.2415.1809-.518.3157-.8187.4134V9.6076h1.995v-.7204h-1.995v-.8993h2.003v-.72h-3.557c.2565-.3111.4589-.5982.5095-.78L5.9058 6.32c2.6603-.9519 4.1408-.7886 4.1278.773v4.1128s.1568 1.4124-1.461 1.3107l-.8757-.188-.207.8307s3.7822 1.0812 4.0913-1.8246c.3096-2.9058-.0767-4.7576-.0767-4.7576s-.3451-2.6824-6.213-1.02zM.0582 12.1534l1.5867.9905c1.0967-2.3813 1.0265-2.0657 1.302-2.92.2832-.8737.3453-1.54-.1362-2.023-.6172-.6197-.6844-.6773-1.6017-1.3582L.5487 7.8562l1.2164.7576s.8141.4153.4274 1.1903c-.3617.7375-2.1343 2.3493-2.1343 2.3493zm19.94 6.8484s-.0186.523-.6704.523c-.5892 0-.6363-.4113-.6363-.4113-.2485.2996-.6207.4549-1.0696.4549-.7606 0-1.2961-.5135-1.2961-1.2896 0-.786.5576-1.28 1.3983-1.28.3832 0 .725.1462.9258.4037.0066-.0706.0136-.1362.0136-.1968 0-.5622-.3092-.8127-1.01-.8127-.3382 0-.6659.0486-1.0136.1408.1107-.219.1924-.3793.2971-.4574.1202-.0972.4294-.1408.9419-.1408 1.269 0 1.742.4259 1.742 1.4279v1.2024c0 .3161.033.4359.3772.4359zm-1.3272-.745c0-.4815-.2555-.747-.6378-.747-.4043 0-.6668.2825-.6668.7595 0 .467.2775.7615.6533.7615.3882 0 .6513-.275.6513-.774zm5.2706-.501c0 1.1528-.6924 1.8271-1.7786 1.8271-1.0957 0-1.7766-.6743-1.7766-1.8271 0-1.1584.6809-1.8252 1.7766-1.8252 1.0862 0 1.7786.6719 1.7786 1.8252zm-1.0867 0c0-.8693-.237-1.2997-.692-1.2997-.4734 0-.6938.4304-.6938 1.2997 0 .8662.2204 1.299.6939 1.299.467 0 .6919-.4328.6919-1.299zm-7.1313-.0441c0 1.1703-.6534 1.8567-1.5767 1.8567-.4233 0-.8056-.1633-1.0466-.47 0 0-.1082.4264-.6618.4264-.6884 0-.6644-.523-.6644-.523.3868.0165.3758-.2145.3758-.436v-2.8862c0-.3552-.0742-.4795-.4269-.4865.0136-.1072.0777-.5376.6789-.5376.8156 0 .7615.9149.7615.9149v.786c.229-.268.5516-.3942.9905-.3942.9624 0 1.5697.6447 1.5697 1.7495zm-1.0937.0806c0-.8983-.2716-1.3557-.7616-1.3557-.4393 0-.74.3968-.74 1.1047v.3602c0 .7205.3152 1.1168.7631 1.1168.477 0 .7385-.4134.7385-1.226zm-3.2425-.0365c0 1.1528-.688 1.8271-1.7806 1.8271-1.0937 0-1.7726-.6743-1.7726-1.8271 0-1.1584.6789-1.8252 1.7726-1.8252 1.0927 0 1.7806.6719 1.7806 1.8252zm-1.0832 0c0-.8693-.236-1.2997-.6974-1.2997-.467 0-.6874.4304-.6874 1.2997 0 .8662.2205 1.299.6874 1.299.469 0 .6974-.4328.6974-1.299zm-5.9895-2.716c-.268.0782-.7901.0972-1.5516.0972-.925 0-1.5391-.039-1.8543-.039-.5195 0-.7254.1342-.9088.6592.3006-.1017.6729-.1087 1.1353-.1087.3512 0 .4108.0431.4108.2941V18.846c0 .2821.1178.6789.7255.6789.7104 0 .8346-.526.8346-.526-.3552-.007-.4248-.1313-.4248-.4865V15.952c0-.2675.1012-.2795.471-.2795.1252 0 .2094.007.2605.007.545 0 .6883-.1047.9018-.6398zm3.1303 3.962s-.018.523-.6664.523c-.556 0-.6403-.4113-.6403-.4113-.248.2996-.6207.4549-1.0656.4549-.7655 0-1.2996-.5135-1.2996-1.2896 0-.786.557-1.28 1.3993-1.28.3843 0 .7274.1462.9258.4037.0036-.0706.0106-.1362.0106-.1968 0-.5622-.3051-.8127-1.007-.8127-.3382 0-.6669.0486-1.015.1408.1131-.219.1923-.3793.2945-.4574.1293-.0972.4329-.1408.9469-.1408 1.265 0 1.746.4259 1.746 1.4279v1.2024c0 .3161.0276.4359.3708.4359zm-1.3262-.745c0-.4815-.2555-.747-.6378-.747-.4013 0-.6668.2825-.6668.7595 0 .467.279.7615.6513.7615.3893 0 .6533-.275.6533-.774z" })
      ]
    }
  );
});

exports.default = SiTaobao;
exports.defaultColor = defaultColor;
