{"version": 3, "names": ["mergeOptions", "target", "source", "k", "Object", "keys", "parserOpts", "targetObj", "mergeDefaultFields", "val", "undefined", "isIterableIterator", "value", "next", "Symbol", "iterator"], "sources": ["../../src/config/util.ts"], "sourcesContent": ["import type { ValidatedOptions, NormalizedOptions } from \"./validation/options\";\n\nexport function mergeOptions(\n  target: ValidatedOptions,\n  source: ValidatedOptions | NormalizedOptions,\n): void {\n  for (const k of Object.keys(source)) {\n    if (\n      (k === \"parserOpts\" || k === \"generatorOpts\" || k === \"assumptions\") &&\n      source[k]\n    ) {\n      const parserOpts = source[k];\n      const targetObj = target[k] || (target[k] = {});\n      mergeDefaultFields(targetObj, parserOpts);\n    } else {\n      //@ts-expect-error k must index source\n      const val = source[k];\n      //@ts-expect-error assigning source to target\n      if (val !== undefined) target[k] = val as any;\n    }\n  }\n}\n\nfunction mergeDefaultFields<T extends {}>(target: T, source: T) {\n  for (const k of Object.keys(source) as (keyof T)[]) {\n    const val = source[k];\n    if (val !== undefined) target[k] = val;\n  }\n}\n\nexport function isIterableIterator(value: any): value is IterableIterator<any> {\n  return (\n    !!value &&\n    typeof value.next === \"function\" &&\n    typeof value[Symbol.iterator] === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;;AAEO,SAASA,YAAT,CACLC,MADK,EAELC,MAFK,EAGC;EACN,KAAK,MAAMC,CAAX,IAAgBC,MAAM,CAACC,IAAP,CAAYH,MAAZ,CAAhB,EAAqC;IACnC,IACE,CAACC,CAAC,KAAK,YAAN,IAAsBA,CAAC,KAAK,eAA5B,IAA+CA,CAAC,KAAK,aAAtD,KACAD,MAAM,CAACC,CAAD,CAFR,EAGE;MACA,MAAMG,UAAU,GAAGJ,MAAM,CAACC,CAAD,CAAzB;MACA,MAAMI,SAAS,GAAGN,MAAM,CAACE,CAAD,CAAN,KAAcF,MAAM,CAACE,CAAD,CAAN,GAAY,EAA1B,CAAlB;MACAK,kBAAkB,CAACD,SAAD,EAAYD,UAAZ,CAAlB;IACD,CAPD,MAOO;MAEL,MAAMG,GAAG,GAAGP,MAAM,CAACC,CAAD,CAAlB;MAEA,IAAIM,GAAG,KAAKC,SAAZ,EAAuBT,MAAM,CAACE,CAAD,CAAN,GAAYM,GAAZ;IACxB;EACF;AACF;;AAED,SAASD,kBAAT,CAA0CP,MAA1C,EAAqDC,MAArD,EAAgE;EAC9D,KAAK,MAAMC,CAAX,IAAgBC,MAAM,CAACC,IAAP,CAAYH,MAAZ,CAAhB,EAAoD;IAClD,MAAMO,GAAG,GAAGP,MAAM,CAACC,CAAD,CAAlB;IACA,IAAIM,GAAG,KAAKC,SAAZ,EAAuBT,MAAM,CAACE,CAAD,CAAN,GAAYM,GAAZ;EACxB;AACF;;AAEM,SAASE,kBAAT,CAA4BC,KAA5B,EAAwE;EAC7E,OACE,CAAC,CAACA,KAAF,IACA,OAAOA,KAAK,CAACC,IAAb,KAAsB,UADtB,IAEA,OAAOD,KAAK,CAACE,MAAM,CAACC,QAAR,CAAZ,KAAkC,UAHpC;AAKD"}