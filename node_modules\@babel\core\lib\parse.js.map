{"version": 3, "names": ["parse<PERSON><PERSON><PERSON>", "gens<PERSON>", "parse", "code", "opts", "config", "loadConfig", "parser", "passes", "normalizeOptions", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "parseSync", "args", "parseAsync", "async"], "sources": ["../src/parse.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions } from \"./config\";\nimport parser from \"./parser\";\nimport type { ParseResult } from \"./parser\";\nimport normalizeOptions from \"./transformation/normalize-opts\";\nimport type { ValidatedOptions } from \"./config/validation/options\";\n\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace\";\n\ntype FileParseCallback = {\n  (err: Error, ast: null): void;\n  (err: null, ast: ParseResult | null): void;\n};\n\ntype Parse = {\n  (code: string, callback: FileParseCallback): void;\n  (\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileParseCallback,\n  ): void;\n  (code: string, opts?: InputOptions | null): ParseResult | null;\n};\n\nconst parseRunner = gensync(function* parse(\n  code: string,\n  opts: InputOptions | undefined | null,\n): <PERSON><PERSON><ParseResult | null> {\n  const config = yield* loadConfig(opts);\n\n  if (config === null) {\n    return null;\n  }\n\n  return yield* parser(config.passes, normalizeOptions(config), code);\n});\n\nexport const parse: Parse = function parse(\n  code,\n  opts?,\n  callback?: FileParseCallback,\n) {\n  if (typeof opts === \"function\") {\n    callback = opts;\n    opts = undefined as ValidatedOptions;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'parse' function expects a callback. If you need to call it synchronously, please use 'parseSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'parse' function will expect a callback. If you need to call it synchronously, please use 'parseSync'.\",\n      // );\n      return beginHiddenCallStack(parseRunner.sync)(code, opts);\n    }\n  }\n\n  beginHiddenCallStack(parseRunner.errback)(code, opts, callback);\n};\n\nexport function parseSync(...args: Parameters<typeof parseRunner.sync>) {\n  return beginHiddenCallStack(parseRunner.sync)(...args);\n}\nexport function parseAsync(...args: Parameters<typeof parseRunner.async>) {\n  return beginHiddenCallStack(parseRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;;AAEA;;AAGA;;AAiBA,MAAMA,WAAW,GAAGC,UAAA,CAAQ,UAAUC,KAAV,CAC1BC,IAD0B,EAE1BC,IAF0B,EAGG;EAC7B,MAAMC,MAAM,GAAG,OAAO,IAAAC,eAAA,EAAWF,IAAX,CAAtB;;EAEA,IAAIC,MAAM,KAAK,IAAf,EAAqB;IACnB,OAAO,IAAP;EACD;;EAED,OAAO,OAAO,IAAAE,eAAA,EAAOF,MAAM,CAACG,MAAd,EAAsB,IAAAC,sBAAA,EAAiBJ,MAAjB,CAAtB,EAAgDF,IAAhD,CAAd;AACD,CAXmB,CAApB;;AAaO,MAAMD,KAAY,GAAG,SAASA,KAAT,CAC1BC,IAD0B,EAE1BC,IAF0B,EAG1BM,QAH0B,EAI1B;EACA,IAAI,OAAON,IAAP,KAAgB,UAApB,EAAgC;IAC9BM,QAAQ,GAAGN,IAAX;IACAA,IAAI,GAAGO,SAAP;EACD;;EAED,IAAID,QAAQ,KAAKC,SAAjB,EAA4B;IAKnB;MAIL,OAAO,IAAAC,uCAAA,EAAqBZ,WAAW,CAACa,IAAjC,EAAuCV,IAAvC,EAA6CC,IAA7C,CAAP;IACD;EACF;;EAED,IAAAQ,uCAAA,EAAqBZ,WAAW,CAACc,OAAjC,EAA0CX,IAA1C,EAAgDC,IAAhD,EAAsDM,QAAtD;AACD,CAxBM;;;;AA0BA,SAASK,SAAT,CAAmB,GAAGC,IAAtB,EAAiE;EACtE,OAAO,IAAAJ,uCAAA,EAAqBZ,WAAW,CAACa,IAAjC,EAAuC,GAAGG,IAA1C,CAAP;AACD;;AACM,SAASC,UAAT,CAAoB,GAAGD,IAAvB,EAAmE;EACxE,OAAO,IAAAJ,uCAAA,EAAqBZ,WAAW,CAACkB,KAAjC,EAAwC,GAAGF,IAA3C,CAAP;AACD"}