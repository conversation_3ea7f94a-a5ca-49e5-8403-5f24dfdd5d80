{"version": 3, "names": ["ConfigError", "Error", "constructor", "message", "filename", "expectedError", "injcectVirtualStackFrame"], "sources": ["../../src/errors/config-error.ts"], "sourcesContent": ["import { injcectVirtualStackFrame, expectedError } from \"./rewrite-stack-trace\";\n\nexport default class ConfigError extends Error {\n  constructor(message: string, filename?: string) {\n    super(message);\n    expectedError(this);\n    if (filename) injcectVirtualStackFrame(this, filename);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAEe,MAAMA,WAAN,SAA0BC,KAA1B,CAAgC;EAC7CC,WAAW,CAACC,OAAD,EAAkBC,QAAlB,EAAqC;IAC9C,MAAMD,OAAN;IACA,IAAAE,gCAAA,EAAc,IAAd;IACA,IAAID,QAAJ,EAAc,IAAAE,2CAAA,EAAyB,IAAzB,EAA+BF,QAA/B;EACf;;AAL4C"}