{"version": 3, "names": ["isCallExpression", "isLiteral", "isMemberExpression", "isNewExpression", "UnaryExpression", "node", "operator", "word", "space", "token", "print", "argument", "DoExpression", "async", "body", "ParenthesizedExpression", "expression", "UpdateExpression", "prefix", "printTerminatorless", "ConditionalExpression", "test", "consequent", "alternate", "NewExpression", "parent", "callee", "format", "minified", "arguments", "length", "optional", "typeArguments", "typeParameters", "printList", "SequenceExpression", "expressions", "ThisExpression", "Super", "isDecoratorMemberExpression", "type", "computed", "property", "object", "shouldParenthesizeDecoratorExpression", "Decorator", "newline", "OptionalMemberExpression", "TypeError", "value", "OptionalCallExpression", "CallExpression", "Import", "AwaitExpression", "YieldExpression", "delegate", "EmptyStatement", "semicolon", "ExpressionStatement", "AssignmentPattern", "left", "typeAnnotation", "right", "AssignmentExpression", "parens", "inForStatementInitCounter", "n", "needsParens", "BindExpression", "MemberExpression", "MetaProperty", "meta", "PrivateName", "id", "V8IntrinsicIdentifier", "name", "ModuleExpression", "printSequence", "indent", "rightBrace"], "sources": ["../../src/generators/expressions.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isCallExpression,\n  isLiteral,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as n from \"../node\";\n\nexport function UnaryExpression(this: Printer, node: t.UnaryExpression) {\n  if (\n    node.operator === \"void\" ||\n    node.operator === \"delete\" ||\n    node.operator === \"typeof\" ||\n    // throwExpressions\n    node.operator === \"throw\"\n  ) {\n    this.word(node.operator);\n    this.space();\n  } else {\n    this.token(node.operator);\n  }\n\n  this.print(node.argument, node);\n}\n\nexport function DoExpression(this: Printer, node: t.DoExpression) {\n  if (node.async) {\n    this.word(\"async\");\n    this.space();\n  }\n  this.word(\"do\");\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ParenthesizedExpression(\n  this: Printer,\n  node: t.ParenthesizedExpression,\n) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.token(\")\");\n}\n\nexport function UpdateExpression(this: Printer, node: t.UpdateExpression) {\n  if (node.prefix) {\n    this.token(node.operator);\n    this.print(node.argument, node);\n  } else {\n    this.printTerminatorless(node.argument, node, true);\n    this.token(node.operator);\n  }\n}\n\nexport function ConditionalExpression(\n  this: Printer,\n  node: t.ConditionalExpression,\n) {\n  this.print(node.test, node);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.consequent, node);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.alternate, node);\n}\n\nexport function NewExpression(\n  this: Printer,\n  node: t.NewExpression,\n  parent: t.Node,\n) {\n  this.word(\"new\");\n  this.space();\n  this.print(node.callee, node);\n  if (\n    this.format.minified &&\n    node.arguments.length === 0 &&\n    !node.optional &&\n    !isCallExpression(parent, { callee: node }) &&\n    !isMemberExpression(parent) &&\n    !isNewExpression(parent)\n  ) {\n    return;\n  }\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    // TODO: This can never happen\n    this.token(\"?.\");\n  }\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function SequenceExpression(this: Printer, node: t.SequenceExpression) {\n  this.printList(node.expressions, node);\n}\n\nexport function ThisExpression(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function Super(this: Printer) {\n  this.word(\"super\");\n}\n\nfunction isDecoratorMemberExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n): boolean {\n  switch (node.type) {\n    case \"Identifier\":\n      return true;\n    case \"MemberExpression\":\n      return (\n        !node.computed &&\n        node.property.type === \"Identifier\" &&\n        isDecoratorMemberExpression(node.object)\n      );\n    default:\n      return false;\n  }\n}\nfunction shouldParenthesizeDecoratorExpression(\n  node: t.Expression | t.Super | t.V8IntrinsicIdentifier,\n) {\n  if (node.type === \"ParenthesizedExpression\") {\n    // We didn't check extra?.parenthesized here because we don't track decorators in needsParen\n    return false;\n  }\n  return !isDecoratorMemberExpression(\n    node.type === \"CallExpression\" ? node.callee : node,\n  );\n}\n\nexport function Decorator(this: Printer, node: t.Decorator) {\n  this.token(\"@\");\n  const { expression } = node;\n  if (shouldParenthesizeDecoratorExpression(expression)) {\n    this.token(\"(\");\n    this.print(expression, node);\n    this.token(\")\");\n  } else {\n    this.print(expression, node);\n  }\n  this.newline();\n}\n\nexport function OptionalMemberExpression(\n  this: Printer,\n  node: t.OptionalMemberExpression,\n) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe instead of typeof check specific literal types?\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n  } else {\n    if (!node.optional) {\n      this.token(\".\");\n    }\n    this.print(node.property, node);\n  }\n}\n\nexport function OptionalCallExpression(\n  this: Printer,\n  node: t.OptionalCallExpression,\n) {\n  this.print(node.callee, node);\n\n  this.print(node.typeParameters, node); // TS\n\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n\n  this.print(node.typeArguments, node); // Flow\n\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function CallExpression(this: Printer, node: t.CallExpression) {\n  this.print(node.callee, node);\n\n  this.print(node.typeArguments, node); // Flow\n  this.print(node.typeParameters, node); // TS\n  this.token(\"(\");\n  this.printList(node.arguments, node);\n  this.token(\")\");\n}\n\nexport function Import(this: Printer) {\n  this.word(\"import\");\n}\n\nexport function AwaitExpression(this: Printer, node: t.AwaitExpression) {\n  this.word(\"await\");\n\n  if (node.argument) {\n    this.space();\n    this.printTerminatorless(node.argument, node, false);\n  }\n}\n\nexport function YieldExpression(this: Printer, node: t.YieldExpression) {\n  this.word(\"yield\");\n\n  if (node.delegate) {\n    this.token(\"*\");\n  }\n\n  if (node.argument) {\n    this.space();\n    this.printTerminatorless(node.argument, node, false);\n  }\n}\n\nexport function EmptyStatement(this: Printer) {\n  this.semicolon(true /* force */);\n}\n\nexport function ExpressionStatement(\n  this: Printer,\n  node: t.ExpressionStatement,\n) {\n  this.print(node.expression, node);\n  this.semicolon();\n}\n\nexport function AssignmentPattern(this: Printer, node: t.AssignmentPattern) {\n  this.print(node.left, node);\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  if (node.left.optional) this.token(\"?\");\n  // @ts-expect-error todo(flow->ts) property present on some of the types in union but not all\n  this.print(node.left.typeAnnotation, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n}\n\nexport function AssignmentExpression(\n  this: Printer,\n  node: t.AssignmentExpression,\n  parent: t.Node,\n) {\n  // Somewhere inside a for statement `init` node but doesn't usually\n  // needs a paren except for `in` expressions: `for (a in b ? a : b;;)`\n  const parens =\n    this.inForStatementInitCounter &&\n    node.operator === \"in\" &&\n    !n.needsParens(node, parent);\n\n  if (parens) {\n    this.token(\"(\");\n  }\n\n  this.print(node.left, node);\n\n  this.space();\n  if (node.operator === \"in\" || node.operator === \"instanceof\") {\n    this.word(node.operator);\n  } else {\n    this.token(node.operator);\n  }\n  this.space();\n\n  this.print(node.right, node);\n\n  if (parens) {\n    this.token(\")\");\n  }\n}\n\nexport function BindExpression(this: Printer, node: t.BindExpression) {\n  this.print(node.object, node);\n  this.token(\"::\");\n  this.print(node.callee, node);\n}\n\nexport {\n  AssignmentExpression as BinaryExpression,\n  AssignmentExpression as LogicalExpression,\n};\n\nexport function MemberExpression(this: Printer, node: t.MemberExpression) {\n  this.print(node.object, node);\n\n  if (!node.computed && isMemberExpression(node.property)) {\n    throw new TypeError(\"Got a MemberExpression for MemberExpression property\");\n  }\n\n  let computed = node.computed;\n  // @ts-expect-error todo(flow->ts) maybe use specific literal types\n  if (isLiteral(node.property) && typeof node.property.value === \"number\") {\n    computed = true;\n  }\n\n  if (computed) {\n    this.token(\"[\");\n    this.print(node.property, node);\n    this.token(\"]\");\n  } else {\n    this.token(\".\");\n    this.print(node.property, node);\n  }\n}\n\nexport function MetaProperty(this: Printer, node: t.MetaProperty) {\n  this.print(node.meta, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function PrivateName(this: Printer, node: t.PrivateName) {\n  this.token(\"#\");\n  this.print(node.id, node);\n}\n\nexport function V8IntrinsicIdentifier(\n  this: Printer,\n  node: t.V8IntrinsicIdentifier,\n) {\n  this.token(\"%\");\n  this.word(node.name);\n}\n\nexport function ModuleExpression(this: Printer, node: t.ModuleExpression) {\n  this.word(\"module\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body.body, node, { indent: true });\n    this.rightBrace();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAOA;;;EANEA,gB;EACAC,S;EACAC,kB;EACAC;;;AAKK,SAASC,eAAT,CAAwCC,IAAxC,EAAiE;EACtE,IACEA,IAAI,CAACC,QAAL,KAAkB,MAAlB,IACAD,IAAI,CAACC,QAAL,KAAkB,QADlB,IAEAD,IAAI,CAACC,QAAL,KAAkB,QAFlB,IAIAD,IAAI,CAACC,QAAL,KAAkB,OALpB,EAME;IACA,KAAKC,IAAL,CAAUF,IAAI,CAACC,QAAf;IACA,KAAKE,KAAL;EACD,CATD,MASO;IACL,KAAKC,KAAL,CAAWJ,IAAI,CAACC,QAAhB;EACD;;EAED,KAAKI,KAAL,CAAWL,IAAI,CAACM,QAAhB,EAA0BN,IAA1B;AACD;;AAEM,SAASO,YAAT,CAAqCP,IAArC,EAA2D;EAChE,IAAIA,IAAI,CAACQ,KAAT,EAAgB;IACd,KAAKN,IAAL,CAAU,OAAV;IACA,KAAKC,KAAL;EACD;;EACD,KAAKD,IAAL,CAAU,IAAV;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWL,IAAI,CAACS,IAAhB,EAAsBT,IAAtB;AACD;;AAEM,SAASU,uBAAT,CAELV,IAFK,EAGL;EACA,KAAKI,SAAL;EACA,KAAKC,KAAL,CAAWL,IAAI,CAACW,UAAhB,EAA4BX,IAA5B;EACA,KAAKI,SAAL;AACD;;AAEM,SAASQ,gBAAT,CAAyCZ,IAAzC,EAAmE;EACxE,IAAIA,IAAI,CAACa,MAAT,EAAiB;IACf,KAAKT,KAAL,CAAWJ,IAAI,CAACC,QAAhB;IACA,KAAKI,KAAL,CAAWL,IAAI,CAACM,QAAhB,EAA0BN,IAA1B;EACD,CAHD,MAGO;IACL,KAAKc,mBAAL,CAAyBd,IAAI,CAACM,QAA9B,EAAwCN,IAAxC,EAA8C,IAA9C;IACA,KAAKI,KAAL,CAAWJ,IAAI,CAACC,QAAhB;EACD;AACF;;AAEM,SAASc,qBAAT,CAELf,IAFK,EAGL;EACA,KAAKK,KAAL,CAAWL,IAAI,CAACgB,IAAhB,EAAsBhB,IAAtB;EACA,KAAKG,KAAL;EACA,KAAKC,SAAL;EACA,KAAKD,KAAL;EACA,KAAKE,KAAL,CAAWL,IAAI,CAACiB,UAAhB,EAA4BjB,IAA5B;EACA,KAAKG,KAAL;EACA,KAAKC,SAAL;EACA,KAAKD,KAAL;EACA,KAAKE,KAAL,CAAWL,IAAI,CAACkB,SAAhB,EAA2BlB,IAA3B;AACD;;AAEM,SAASmB,aAAT,CAELnB,IAFK,EAGLoB,MAHK,EAIL;EACA,KAAKlB,IAAL,CAAU,KAAV;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWL,IAAI,CAACqB,MAAhB,EAAwBrB,IAAxB;;EACA,IACE,KAAKsB,MAAL,CAAYC,QAAZ,IACAvB,IAAI,CAACwB,SAAL,CAAeC,MAAf,KAA0B,CAD1B,IAEA,CAACzB,IAAI,CAAC0B,QAFN,IAGA,CAAC/B,gBAAgB,CAACyB,MAAD,EAAS;IAAEC,MAAM,EAAErB;EAAV,CAAT,CAHjB,IAIA,CAACH,kBAAkB,CAACuB,MAAD,CAJnB,IAKA,CAACtB,eAAe,CAACsB,MAAD,CANlB,EAOE;IACA;EACD;;EAED,KAAKf,KAAL,CAAWL,IAAI,CAAC2B,aAAhB,EAA+B3B,IAA/B;EACA,KAAKK,KAAL,CAAWL,IAAI,CAAC4B,cAAhB,EAAgC5B,IAAhC;;EAEA,IAAIA,IAAI,CAAC0B,QAAT,EAAmB;IAEjB,KAAKtB,KAAL,CAAW,IAAX;EACD;;EACD,KAAKA,SAAL;EACA,KAAKyB,SAAL,CAAe7B,IAAI,CAACwB,SAApB,EAA+BxB,IAA/B;EACA,KAAKI,SAAL;AACD;;AAEM,SAAS0B,kBAAT,CAA2C9B,IAA3C,EAAuE;EAC5E,KAAK6B,SAAL,CAAe7B,IAAI,CAAC+B,WAApB,EAAiC/B,IAAjC;AACD;;AAEM,SAASgC,cAAT,GAAuC;EAC5C,KAAK9B,IAAL,CAAU,MAAV;AACD;;AAEM,SAAS+B,KAAT,GAA8B;EACnC,KAAK/B,IAAL,CAAU,OAAV;AACD;;AAED,SAASgC,2BAAT,CACElC,IADF,EAEW;EACT,QAAQA,IAAI,CAACmC,IAAb;IACE,KAAK,YAAL;MACE,OAAO,IAAP;;IACF,KAAK,kBAAL;MACE,OACE,CAACnC,IAAI,CAACoC,QAAN,IACApC,IAAI,CAACqC,QAAL,CAAcF,IAAd,KAAuB,YADvB,IAEAD,2BAA2B,CAAClC,IAAI,CAACsC,MAAN,CAH7B;;IAKF;MACE,OAAO,KAAP;EAVJ;AAYD;;AACD,SAASC,qCAAT,CACEvC,IADF,EAEE;EACA,IAAIA,IAAI,CAACmC,IAAL,KAAc,yBAAlB,EAA6C;IAE3C,OAAO,KAAP;EACD;;EACD,OAAO,CAACD,2BAA2B,CACjClC,IAAI,CAACmC,IAAL,KAAc,gBAAd,GAAiCnC,IAAI,CAACqB,MAAtC,GAA+CrB,IADd,CAAnC;AAGD;;AAEM,SAASwC,SAAT,CAAkCxC,IAAlC,EAAqD;EAC1D,KAAKI,SAAL;EACA,MAAM;IAAEO;EAAF,IAAiBX,IAAvB;;EACA,IAAIuC,qCAAqC,CAAC5B,UAAD,CAAzC,EAAuD;IACrD,KAAKP,SAAL;IACA,KAAKC,KAAL,CAAWM,UAAX,EAAuBX,IAAvB;IACA,KAAKI,SAAL;EACD,CAJD,MAIO;IACL,KAAKC,KAAL,CAAWM,UAAX,EAAuBX,IAAvB;EACD;;EACD,KAAKyC,OAAL;AACD;;AAEM,SAASC,wBAAT,CAEL1C,IAFK,EAGL;EACA,KAAKK,KAAL,CAAWL,IAAI,CAACsC,MAAhB,EAAwBtC,IAAxB;;EAEA,IAAI,CAACA,IAAI,CAACoC,QAAN,IAAkBvC,kBAAkB,CAACG,IAAI,CAACqC,QAAN,CAAxC,EAAyD;IACvD,MAAM,IAAIM,SAAJ,CAAc,sDAAd,CAAN;EACD;;EAED,IAAIP,QAAQ,GAAGpC,IAAI,CAACoC,QAApB;;EAEA,IAAIxC,SAAS,CAACI,IAAI,CAACqC,QAAN,CAAT,IAA4B,OAAOrC,IAAI,CAACqC,QAAL,CAAcO,KAArB,KAA+B,QAA/D,EAAyE;IACvER,QAAQ,GAAG,IAAX;EACD;;EACD,IAAIpC,IAAI,CAAC0B,QAAT,EAAmB;IACjB,KAAKtB,KAAL,CAAW,IAAX;EACD;;EAED,IAAIgC,QAAJ,EAAc;IACZ,KAAKhC,SAAL;IACA,KAAKC,KAAL,CAAWL,IAAI,CAACqC,QAAhB,EAA0BrC,IAA1B;IACA,KAAKI,SAAL;EACD,CAJD,MAIO;IACL,IAAI,CAACJ,IAAI,CAAC0B,QAAV,EAAoB;MAClB,KAAKtB,SAAL;IACD;;IACD,KAAKC,KAAL,CAAWL,IAAI,CAACqC,QAAhB,EAA0BrC,IAA1B;EACD;AACF;;AAEM,SAAS6C,sBAAT,CAEL7C,IAFK,EAGL;EACA,KAAKK,KAAL,CAAWL,IAAI,CAACqB,MAAhB,EAAwBrB,IAAxB;EAEA,KAAKK,KAAL,CAAWL,IAAI,CAAC4B,cAAhB,EAAgC5B,IAAhC;;EAEA,IAAIA,IAAI,CAAC0B,QAAT,EAAmB;IACjB,KAAKtB,KAAL,CAAW,IAAX;EACD;;EAED,KAAKC,KAAL,CAAWL,IAAI,CAAC2B,aAAhB,EAA+B3B,IAA/B;EAEA,KAAKI,SAAL;EACA,KAAKyB,SAAL,CAAe7B,IAAI,CAACwB,SAApB,EAA+BxB,IAA/B;EACA,KAAKI,SAAL;AACD;;AAEM,SAAS0C,cAAT,CAAuC9C,IAAvC,EAA+D;EACpE,KAAKK,KAAL,CAAWL,IAAI,CAACqB,MAAhB,EAAwBrB,IAAxB;EAEA,KAAKK,KAAL,CAAWL,IAAI,CAAC2B,aAAhB,EAA+B3B,IAA/B;EACA,KAAKK,KAAL,CAAWL,IAAI,CAAC4B,cAAhB,EAAgC5B,IAAhC;EACA,KAAKI,SAAL;EACA,KAAKyB,SAAL,CAAe7B,IAAI,CAACwB,SAApB,EAA+BxB,IAA/B;EACA,KAAKI,SAAL;AACD;;AAEM,SAAS2C,MAAT,GAA+B;EACpC,KAAK7C,IAAL,CAAU,QAAV;AACD;;AAEM,SAAS8C,eAAT,CAAwChD,IAAxC,EAAiE;EACtE,KAAKE,IAAL,CAAU,OAAV;;EAEA,IAAIF,IAAI,CAACM,QAAT,EAAmB;IACjB,KAAKH,KAAL;IACA,KAAKW,mBAAL,CAAyBd,IAAI,CAACM,QAA9B,EAAwCN,IAAxC,EAA8C,KAA9C;EACD;AACF;;AAEM,SAASiD,eAAT,CAAwCjD,IAAxC,EAAiE;EACtE,KAAKE,IAAL,CAAU,OAAV;;EAEA,IAAIF,IAAI,CAACkD,QAAT,EAAmB;IACjB,KAAK9C,SAAL;EACD;;EAED,IAAIJ,IAAI,CAACM,QAAT,EAAmB;IACjB,KAAKH,KAAL;IACA,KAAKW,mBAAL,CAAyBd,IAAI,CAACM,QAA9B,EAAwCN,IAAxC,EAA8C,KAA9C;EACD;AACF;;AAEM,SAASmD,cAAT,GAAuC;EAC5C,KAAKC,SAAL,CAAe,IAAf;AACD;;AAEM,SAASC,mBAAT,CAELrD,IAFK,EAGL;EACA,KAAKK,KAAL,CAAWL,IAAI,CAACW,UAAhB,EAA4BX,IAA5B;EACA,KAAKoD,SAAL;AACD;;AAEM,SAASE,iBAAT,CAA0CtD,IAA1C,EAAqE;EAC1E,KAAKK,KAAL,CAAWL,IAAI,CAACuD,IAAhB,EAAsBvD,IAAtB;EAEA,IAAIA,IAAI,CAACuD,IAAL,CAAU7B,QAAd,EAAwB,KAAKtB,SAAL;EAExB,KAAKC,KAAL,CAAWL,IAAI,CAACuD,IAAL,CAAUC,cAArB,EAAqCxD,IAArC;EACA,KAAKG,KAAL;EACA,KAAKC,SAAL;EACA,KAAKD,KAAL;EACA,KAAKE,KAAL,CAAWL,IAAI,CAACyD,KAAhB,EAAuBzD,IAAvB;AACD;;AAEM,SAAS0D,oBAAT,CAEL1D,IAFK,EAGLoB,MAHK,EAIL;EAGA,MAAMuC,MAAM,GACV,KAAKC,yBAAL,IACA5D,IAAI,CAACC,QAAL,KAAkB,IADlB,IAEA,CAAC4D,CAAC,CAACC,WAAF,CAAc9D,IAAd,EAAoBoB,MAApB,CAHH;;EAKA,IAAIuC,MAAJ,EAAY;IACV,KAAKvD,SAAL;EACD;;EAED,KAAKC,KAAL,CAAWL,IAAI,CAACuD,IAAhB,EAAsBvD,IAAtB;EAEA,KAAKG,KAAL;;EACA,IAAIH,IAAI,CAACC,QAAL,KAAkB,IAAlB,IAA0BD,IAAI,CAACC,QAAL,KAAkB,YAAhD,EAA8D;IAC5D,KAAKC,IAAL,CAAUF,IAAI,CAACC,QAAf;EACD,CAFD,MAEO;IACL,KAAKG,KAAL,CAAWJ,IAAI,CAACC,QAAhB;EACD;;EACD,KAAKE,KAAL;EAEA,KAAKE,KAAL,CAAWL,IAAI,CAACyD,KAAhB,EAAuBzD,IAAvB;;EAEA,IAAI2D,MAAJ,EAAY;IACV,KAAKvD,SAAL;EACD;AACF;;AAEM,SAAS2D,cAAT,CAAuC/D,IAAvC,EAA+D;EACpE,KAAKK,KAAL,CAAWL,IAAI,CAACsC,MAAhB,EAAwBtC,IAAxB;EACA,KAAKI,KAAL,CAAW,IAAX;EACA,KAAKC,KAAL,CAAWL,IAAI,CAACqB,MAAhB,EAAwBrB,IAAxB;AACD;;AAOM,SAASgE,gBAAT,CAAyChE,IAAzC,EAAmE;EACxE,KAAKK,KAAL,CAAWL,IAAI,CAACsC,MAAhB,EAAwBtC,IAAxB;;EAEA,IAAI,CAACA,IAAI,CAACoC,QAAN,IAAkBvC,kBAAkB,CAACG,IAAI,CAACqC,QAAN,CAAxC,EAAyD;IACvD,MAAM,IAAIM,SAAJ,CAAc,sDAAd,CAAN;EACD;;EAED,IAAIP,QAAQ,GAAGpC,IAAI,CAACoC,QAApB;;EAEA,IAAIxC,SAAS,CAACI,IAAI,CAACqC,QAAN,CAAT,IAA4B,OAAOrC,IAAI,CAACqC,QAAL,CAAcO,KAArB,KAA+B,QAA/D,EAAyE;IACvER,QAAQ,GAAG,IAAX;EACD;;EAED,IAAIA,QAAJ,EAAc;IACZ,KAAKhC,SAAL;IACA,KAAKC,KAAL,CAAWL,IAAI,CAACqC,QAAhB,EAA0BrC,IAA1B;IACA,KAAKI,SAAL;EACD,CAJD,MAIO;IACL,KAAKA,SAAL;IACA,KAAKC,KAAL,CAAWL,IAAI,CAACqC,QAAhB,EAA0BrC,IAA1B;EACD;AACF;;AAEM,SAASiE,YAAT,CAAqCjE,IAArC,EAA2D;EAChE,KAAKK,KAAL,CAAWL,IAAI,CAACkE,IAAhB,EAAsBlE,IAAtB;EACA,KAAKI,SAAL;EACA,KAAKC,KAAL,CAAWL,IAAI,CAACqC,QAAhB,EAA0BrC,IAA1B;AACD;;AAEM,SAASmE,WAAT,CAAoCnE,IAApC,EAAyD;EAC9D,KAAKI,SAAL;EACA,KAAKC,KAAL,CAAWL,IAAI,CAACoE,EAAhB,EAAoBpE,IAApB;AACD;;AAEM,SAASqE,qBAAT,CAELrE,IAFK,EAGL;EACA,KAAKI,SAAL;EACA,KAAKF,IAAL,CAAUF,IAAI,CAACsE,IAAf;AACD;;AAEM,SAASC,gBAAT,CAAyCvE,IAAzC,EAAmE;EACxE,KAAKE,IAAL,CAAU,QAAV;EACA,KAAKC,KAAL;EACA,KAAKC,SAAL;;EACA,IAAIJ,IAAI,CAACS,IAAL,CAAUA,IAAV,CAAegB,MAAf,KAA0B,CAA9B,EAAiC;IAC/B,KAAKrB,SAAL;EACD,CAFD,MAEO;IACL,KAAKqC,OAAL;IACA,KAAK+B,aAAL,CAAmBxE,IAAI,CAACS,IAAL,CAAUA,IAA7B,EAAmCT,IAAnC,EAAyC;MAAEyE,MAAM,EAAE;IAAV,CAAzC;IACA,KAAKC,UAAL;EACD;AACF"}