// node_modules/@tauri-apps/api/tslib.es6-9bc0804d.js
var t = function(n2, r3) {
  return t = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(t2, n3) {
    t2.__proto__ = n3;
  } || function(t2, n3) {
    for (var r4 in n3)
      Object.prototype.hasOwnProperty.call(n3, r4) && (t2[r4] = n3[r4]);
  }, t(n2, r3);
};
function n(n2, r3) {
  if ("function" != typeof r3 && null !== r3)
    throw new TypeError("Class extends value " + String(r3) + " is not a constructor or null");
  function e() {
    this.constructor = n2;
  }
  t(n2, r3), n2.prototype = null === r3 ? Object.create(r3) : (e.prototype = r3.prototype, new e());
}
var r = function() {
  return r = Object.assign || function(t2) {
    for (var n2, r3 = 1, e = arguments.length; r3 < e; r3++)
      for (var o3 in n2 = arguments[r3])
        Object.prototype.hasOwnProperty.call(n2, o3) && (t2[o3] = n2[o3]);
    return t2;
  }, r.apply(this, arguments);
};
function o(t2, n2, r3, e) {
  return new (r3 || (r3 = Promise))(function(o3, a2) {
    function c2(t3) {
      try {
        i2(e.next(t3));
      } catch (t4) {
        a2(t4);
      }
    }
    function l(t3) {
      try {
        i2(e.throw(t3));
      } catch (t4) {
        a2(t4);
      }
    }
    function i2(t3) {
      var n3;
      t3.done ? o3(t3.value) : (n3 = t3.value, n3 instanceof r3 ? n3 : new r3(function(t4) {
        t4(n3);
      })).then(c2, l);
    }
    i2((e = e.apply(t2, n2 || [])).next());
  });
}
function a(t2, n2) {
  var r3, e, o3, a2, c2 = { label: 0, sent: function() {
    if (1 & o3[0])
      throw o3[1];
    return o3[1];
  }, trys: [], ops: [] };
  return a2 = { next: l(0), throw: l(1), return: l(2) }, "function" == typeof Symbol && (a2[Symbol.iterator] = function() {
    return this;
  }), a2;
  function l(a3) {
    return function(l2) {
      return function(a4) {
        if (r3)
          throw new TypeError("Generator is already executing.");
        for (; c2; )
          try {
            if (r3 = 1, e && (o3 = 2 & a4[0] ? e.return : a4[0] ? e.throw || ((o3 = e.return) && o3.call(e), 0) : e.next) && !(o3 = o3.call(e, a4[1])).done)
              return o3;
            switch (e = 0, o3 && (a4 = [2 & a4[0], o3.value]), a4[0]) {
              case 0:
              case 1:
                o3 = a4;
                break;
              case 4:
                return c2.label++, { value: a4[1], done: false };
              case 5:
                c2.label++, e = a4[1], a4 = [0];
                continue;
              case 7:
                a4 = c2.ops.pop(), c2.trys.pop();
                continue;
              default:
                if (!(o3 = c2.trys, (o3 = o3.length > 0 && o3[o3.length - 1]) || 6 !== a4[0] && 2 !== a4[0])) {
                  c2 = 0;
                  continue;
                }
                if (3 === a4[0] && (!o3 || a4[1] > o3[0] && a4[1] < o3[3])) {
                  c2.label = a4[1];
                  break;
                }
                if (6 === a4[0] && c2.label < o3[1]) {
                  c2.label = o3[1], o3 = a4;
                  break;
                }
                if (o3 && c2.label < o3[2]) {
                  c2.label = o3[2], c2.ops.push(a4);
                  break;
                }
                o3[2] && c2.ops.pop(), c2.trys.pop();
                continue;
            }
            a4 = n2.call(t2, c2);
          } catch (t3) {
            a4 = [6, t3], e = 0;
          } finally {
            r3 = o3 = 0;
          }
        if (5 & a4[0])
          throw a4[1];
        return { value: a4[0] ? a4[1] : void 0, done: true };
      }([a3, l2]);
    };
  }
}

// node_modules/@tauri-apps/api/tauri-a4b3335a.js
function o2(n2, t2) {
  void 0 === t2 && (t2 = false);
  var e = window.crypto.getRandomValues(new Uint32Array(1))[0], o3 = "_".concat(e);
  return Object.defineProperty(window, o3, { value: function(e2) {
    return t2 && Reflect.deleteProperty(window, o3), null == n2 ? void 0 : n2(e2);
  }, writable: false, configurable: true }), e;
}
function r2(r3, c2) {
  return void 0 === c2 && (c2 = {}), o(this, void 0, void 0, function() {
    return a(this, function(n2) {
      return [2, new Promise(function(n3, t2) {
        var i2 = o2(function(t3) {
          n3(t3), Reflect.deleteProperty(window, "_".concat(a2));
        }, true), a2 = o2(function(n4) {
          t2(n4), Reflect.deleteProperty(window, "_".concat(i2));
        }, true);
        window.__TAURI_IPC__(r({ cmd: r3, callback: i2, error: a2 }, c2));
      })];
    });
  });
}
function c(n2, t2) {
  void 0 === t2 && (t2 = "asset");
  var e = encodeURIComponent(n2);
  return navigator.userAgent.includes("Windows") ? "https://".concat(t2, ".localhost/").concat(e) : "".concat(t2, "://").concat(e);
}
var i = Object.freeze({ __proto__: null, transformCallback: o2, invoke: r2, convertFileSrc: c });

export {
  n,
  r,
  o,
  a,
  o2,
  r2,
  c
};
//# sourceMappingURL=chunk-IFV3GWMF.js.map
