{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/sparql/sparql.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/sparql/sparql.ts\nvar conf = {\n  comments: {\n    lineComment: \"#\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"'\", close: \"'\", notIn: [\"string\"] },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rq\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.angle\", open: \"<\", close: \">\" }\n  ],\n  keywords: [\n    \"add\",\n    \"as\",\n    \"asc\",\n    \"ask\",\n    \"base\",\n    \"by\",\n    \"clear\",\n    \"construct\",\n    \"copy\",\n    \"create\",\n    \"data\",\n    \"delete\",\n    \"desc\",\n    \"describe\",\n    \"distinct\",\n    \"drop\",\n    \"false\",\n    \"filter\",\n    \"from\",\n    \"graph\",\n    \"group\",\n    \"having\",\n    \"in\",\n    \"insert\",\n    \"limit\",\n    \"load\",\n    \"minus\",\n    \"move\",\n    \"named\",\n    \"not\",\n    \"offset\",\n    \"optional\",\n    \"order\",\n    \"prefix\",\n    \"reduced\",\n    \"select\",\n    \"service\",\n    \"silent\",\n    \"to\",\n    \"true\",\n    \"undef\",\n    \"union\",\n    \"using\",\n    \"values\",\n    \"where\",\n    \"with\"\n  ],\n  builtinFunctions: [\n    \"a\",\n    \"abs\",\n    \"avg\",\n    \"bind\",\n    \"bnode\",\n    \"bound\",\n    \"ceil\",\n    \"coalesce\",\n    \"concat\",\n    \"contains\",\n    \"count\",\n    \"datatype\",\n    \"day\",\n    \"encode_for_uri\",\n    \"exists\",\n    \"floor\",\n    \"group_concat\",\n    \"hours\",\n    \"if\",\n    \"iri\",\n    \"isblank\",\n    \"isiri\",\n    \"isliteral\",\n    \"isnumeric\",\n    \"isuri\",\n    \"lang\",\n    \"langmatches\",\n    \"lcase\",\n    \"max\",\n    \"md5\",\n    \"min\",\n    \"minutes\",\n    \"month\",\n    \"now\",\n    \"rand\",\n    \"regex\",\n    \"replace\",\n    \"round\",\n    \"sameterm\",\n    \"sample\",\n    \"seconds\",\n    \"sha1\",\n    \"sha256\",\n    \"sha384\",\n    \"sha512\",\n    \"str\",\n    \"strafter\",\n    \"strbefore\",\n    \"strdt\",\n    \"strends\",\n    \"strlang\",\n    \"strlen\",\n    \"strstarts\",\n    \"struuid\",\n    \"substr\",\n    \"sum\",\n    \"timezone\",\n    \"tz\",\n    \"ucase\",\n    \"uri\",\n    \"uuid\",\n    \"year\"\n  ],\n  ignoreCase: true,\n  tokenizer: {\n    root: [\n      [/<[^\\s\\u00a0>]*>?/, \"tag\"],\n      { include: \"@strings\" },\n      [/#.*/, \"comment\"],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[;,.]/, \"delimiter\"],\n      [/[_\\w\\d]+:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])*/, \"tag\"],\n      [/:(\\.(?=[\\w_\\-\\\\%])|[:\\w_-]|\\\\[-\\\\_~.!$&'()*+,;=/?#@%]|%[a-f\\d][a-f\\d])+/, \"tag\"],\n      [\n        /[$?]?[_\\w\\d]+/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\" },\n            \"@builtinFunctions\": { token: \"predefined.sql\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/\\^\\^/, \"operator.sql\"],\n      [/\\^[*+\\-<>=&|^\\/!?]*/, \"operator.sql\"],\n      [/[*+\\-<>=&|\\/!?]/, \"operator.sql\"],\n      [/@[a-z\\d\\-]*/, \"metatag.html\"],\n      [/\\s+/, \"white\"]\n    ],\n    strings: [\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'$/, \"string.sql\", \"@pop\"],\n      [/'/, \"string.sql\", \"@stringBody\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"$/, \"string.sql\", \"@pop\"],\n      [/\"/, \"string.sql\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/'/, \"string.sql\", \"@pop\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+/, \"string.sql\"],\n      [/\\\\./, \"string.escape\"],\n      [/\"/, \"string.sql\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,IACnD,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,YAAY;AAAA,EACZ,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,oBAAoB,KAAK;AAAA,MAC1B,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,OAAO,SAAS;AAAA,MACjB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mFAAmF,KAAK;AAAA,MACzF,CAAC,2EAA2E,KAAK;AAAA,MACjF;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,UAAU;AAAA,YAChC,qBAAqB,EAAE,OAAO,iBAAiB;AAAA,YAC/C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,cAAc;AAAA,MACvB,CAAC,uBAAuB,cAAc;AAAA,MACtC,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,eAAe,cAAc;AAAA,MAC9B,CAAC,OAAO,OAAO;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,MAAM,cAAc,MAAM;AAAA,MAC3B,CAAC,KAAK,cAAc,aAAa;AAAA,MACjC,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,MAAM,cAAc,MAAM;AAAA,MAC3B,CAAC,KAAK,cAAc,gBAAgB;AAAA,IACtC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,cAAc,MAAM;AAAA,IAC5B;AAAA,IACA,eAAe;AAAA,MACb,CAAC,WAAW,YAAY;AAAA,MACxB,CAAC,OAAO,eAAe;AAAA,MACvB,CAAC,KAAK,cAAc,MAAM;AAAA,IAC5B;AAAA,EACF;AACF;", "names": []}