{"name": "blazecoder", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@icons-pack/react-simple-icons": "^13.5.0", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shikijs/transformers": "^3.8.0", "@tailwindcss/postcss": "^4.1.11", "@tauri-apps/api": "^1.1.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "fuse.js": "^7.1.0", "lucide-react": "^0.525.0", "monaco-editor": "^0.44.0", "nanoid": "^4.0.0", "postcss": "^8.4.17", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "remark-gfm": "^4.0.1", "remixicon": "^2.5.0", "shiki": "^3.8.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "use-stick-to-bottom": "^1.1.1", "zustand": "^4.4.6"}, "devDependencies": {"@tauri-apps/cli": "^1.1.0", "@types/node": "^18.7.10", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@vitejs/plugin-react": "^2.0.0", "tw-animate-css": "^1.3.5", "typescript": "^4.6.4", "vite": "^3.0.2"}}