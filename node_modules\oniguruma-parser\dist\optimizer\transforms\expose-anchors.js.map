{"version": 3, "sources": ["../../../src/optimizer/transforms/expose-anchors.ts"], "sourcesContent": ["import type {Node} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\n\n/**\nPull leading and trailing assertions out of capturing groups when possible; helps group unwrapping.\nEx: `(^abc$)` -> `^(abc)$`.\nEx: `(\\b(?:a|bc)\\b)` -> `\\b((?:a|bc))\\b`. The inner group can subsequently be unwrapped.\n*/\nconst exposeAnchors: Visitor = {\n  // Done for capturing groups only because they can't be unwrapped like noncapturing groups (done\n  // via `unwrapUselessGroups` combined with `removeUselessFlags`; the latter also avoids hazards\n  // from flags that modify word boundary and text segment boundary assertions that would need to\n  // be handled here since noncapturing groups can specify flags to change). Pulling anchors out\n  // can subsequently enable unwrapping multi-alternative noncapturing groups within the capturing\n  // group, and has the side benefit that exposed anchors also improve readability\n  CapturingGroup({node, parent, replaceWithMultiple}) {\n    if (\n      parent.type === 'Quantifier' ||\n      node.body.length > 1 || // Multiple alts\n      node.isSubroutined\n    ) {\n      return;\n    }\n    const firstAlt = node.body[0];\n    const firstAltEls = firstAlt.body;\n    // Despite only pulling one assertion at a time, multiple can be extracted through multiple\n    // rounds of running this optimization\n    const leading = firstAltEls[0];\n    const trailing = firstAltEls.length > 1 ? firstAltEls.at(-1)! : null;\n    const hasLeadingAssertion = leading && leading.type === 'Assertion';\n    const hasTrailingAssertion = trailing && trailing.type === 'Assertion';\n    const clippedStart = hasLeadingAssertion ? 1 : 0;\n    const clippedEnd = firstAltEls.length - (hasTrailingAssertion ? 1 : 0);\n    if (hasLeadingAssertion || hasTrailingAssertion) {\n      firstAlt.body = firstAltEls.slice(clippedStart, clippedEnd);\n      const nodes: Array<Node> = [];\n      if (hasLeadingAssertion) {\n        // Could use `insertBefore` if the traverser supported it\n        nodes.push(leading);\n      }\n      nodes.push(node);\n      if (hasTrailingAssertion) {\n        // Could use `insertAfter` if the traverser supported it\n        nodes.push(trailing);\n      }\n      replaceWithMultiple(nodes, {traverse: true});\n    }\n  },\n};\n\nexport {\n  exposeAnchors,\n};\n"], "mappings": "aAQA,MAAMA,EAAyB,CAO7B,eAAe,CAAC,KAAAC,EAAM,OAAAC,EAAQ,oBAAAC,CAAmB,EAAG,CAClD,GACED,EAAO,OAAS,cAChBD,EAAK,KAAK,OAAS,GACnBA,EAAK,cAEL,OAEF,MAAMG,EAAWH,EAAK,KAAK,CAAC,EACtBI,EAAcD,EAAS,KAGvBE,EAAUD,EAAY,CAAC,EACvBE,EAAWF,EAAY,OAAS,EAAIA,EAAY,GAAG,EAAE,EAAK,KAC1DG,EAAsBF,GAAWA,EAAQ,OAAS,YAClDG,EAAuBF,GAAYA,EAAS,OAAS,YACrDG,EAAeF,EAAsB,EAAI,EACzCG,EAAaN,EAAY,QAAUI,EAAuB,EAAI,GACpE,GAAID,GAAuBC,EAAsB,CAC/CL,EAAS,KAAOC,EAAY,MAAMK,EAAcC,CAAU,EAC1D,MAAMC,EAAqB,CAAC,EACxBJ,GAEFI,EAAM,KAAKN,CAAO,EAEpBM,EAAM,KAAKX,CAAI,EACXQ,GAEFG,EAAM,KAAKL,CAAQ,EAErBJ,EAAoBS,EAAO,CAAC,SAAU,EAAI,CAAC,CAC7C,CACF,CACF,EAEA,OACEZ,KAAA", "names": ["exposeAnchors", "node", "parent", "replaceWithMultiple", "firstAlt", "firstAltEls", "leading", "trailing", "hasLeadingAssertion", "hasTrailingAssertion", "clippedStart", "clippedEnd", "nodes"]}