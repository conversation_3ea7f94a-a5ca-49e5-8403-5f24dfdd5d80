import React, { createContext, useContext, useState, useCallback } from 'react';

interface AIMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  code?: string;
  language?: string;
}

interface AIContextType {
  messages: AIMessage[];
  isLoading: boolean;
  addMessage: (message: Omit<AIMessage, 'id' | 'timestamp'>) => void;
  clearMessages: () => void;
  sendMessage: (content: string, context?: { file?: string; selectedCode?: string }) => Promise<void>;
}

const AIContext = createContext<AIContextType | undefined>(undefined);

export const useAI = () => {
  const context = useContext(AIContext);
  if (!context) {
    throw new Error('useAI must be used within an AIProvider');
  }
  return context;
};

interface AIProviderProps {
  children: React.ReactNode;
}

export const AIProvider: React.FC<AIProviderProps> = ({ children }) => {
  const [messages, setMessages] = useState<AIMessage[]>([
    {
      id: '1',
      type: 'system',
      content: 'AI Assistant is ready to help you with coding, debugging, and explanations.',
      timestamp: new Date(),
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);

  const addMessage = useCallback((message: Omit<AIMessage, 'id' | 'timestamp'>) => {
    const newMessage: AIMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newMessage]);
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([{
      id: '1',
      type: 'system',
      content: 'AI Assistant is ready to help you with coding, debugging, and explanations.',
      timestamp: new Date(),
    }]);
  }, []);

  const sendMessage = useCallback(async (content: string, context?: { file?: string; selectedCode?: string }) => {
    if (!content.trim() || isLoading) return;

    // Add user message
    addMessage({
      type: 'user',
      content,
    });

    setIsLoading(true);

    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate AI response based on context
      let aiResponse = `I understand you're asking about: "${content}".`;
      let suggestions: string[] = [];
      let code: string | undefined;
      let language: string | undefined;

      if (context?.selectedCode) {
        aiResponse += ` I can see you have selected some code. Here's my analysis and suggestions.`;
        code = `// Enhanced version of your code\n${context.selectedCode}\n// Added improvements and optimizations`;
        language = 'typescript';
        suggestions = [
          'Add error handling',
          'Optimize performance',
          'Add type safety',
          'Refactor for readability'
        ];
      } else if (content.toLowerCase().includes('help')) {
        suggestions = [
          'Explain this code',
          'Find bugs in my code',
          'Optimize performance',
          'Add documentation',
          'Refactor code structure'
        ];
      } else if (content.toLowerCase().includes('bug') || content.toLowerCase().includes('error')) {
        aiResponse = `I'll help you debug this issue. Here are some common solutions:`;
        suggestions = [
          'Check console for errors',
          'Verify import statements',
          'Check variable types',
          'Review function parameters'
        ];
      }

      // Add AI response
      addMessage({
        type: 'ai',
        content: aiResponse,
        suggestions,
        code,
        language,
      });

    } catch (error) {
      addMessage({
        type: 'ai',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, addMessage]);

  const value: AIContextType = {
    messages,
    isLoading,
    addMessage,
    clearMessages,
    sendMessage,
  };

  return (
    <AIContext.Provider value={value}>
      {children}
    </AIContext.Provider>
  );
};
