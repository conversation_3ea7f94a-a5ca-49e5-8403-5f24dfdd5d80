import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';

const defaultColor = "#FFA900";
const SiTado = React.forwardRef(function SiTado2({ title = "tado°", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsx("title", { children: title }),
        /* @__PURE__ */ jsx("path", { d: "M22.486 7.795a1.514 1.514 0 1 0 0 3.029 1.514 1.514 0 0 0 0-3.029zm-8.504.003v2.456c-.457-.344-.945-.563-1.686-.563-1.814 0-2.833 1.364-2.833 3.267 0 1.792 1.019 3.247 2.833 3.247 1.781 0 2.817-1.46 2.82-3.247v-5.16zM1.89 7.799l-1.124.378V9.69H0v.945h.757v3.873c0 .84.67 1.51 1.518 1.51h1.128v-.943h-.946a.566.566 0 0 1-.568-.566v-3.874h3.215V9.69H1.89zm20.596.375a1.135 1.135 0 1 1 0 2.27 1.135 1.135 0 0 1 0-2.27zM5.48 9.69v.946h1.906c.354 0 .549.277.549.54v.773l-1.322-.001c-1.134 0-2.267.769-2.267 2.08 0 1.307 1.13 2.087 2.265 2.087.953 0 1.326-.57 1.326-.57v.47H9.07v-4.864c0-.784-.667-1.461-1.51-1.461zm12.861.002c-1.808 0-2.835 1.369-2.835 3.237 0 1.911 1.027 3.276 2.835 3.276 1.787 0 2.828-1.36 2.828-3.276 0-1.863-1.046-3.237-2.828-3.237zm-6.046.95c1.14 0 1.68 1.185 1.68 2.316 0 1.117-.55 2.305-1.68 2.305-1.232 0-1.697-1.188-1.697-2.305 0-1.13.56-2.316 1.697-2.316zm6.046.005c1.12 0 1.703 1.18 1.703 2.3 0 1.117-.572 2.313-1.703 2.313-1.126 0-1.707-1.165-1.707-2.307 0-1.126.57-2.306 1.707-2.306zM6.614 12.9h1.322v1.207c0 .5-.373 1.062-1.323 1.062-.367 0-1.133-.19-1.133-1.134 0-.842.758-1.135 1.134-1.135Z" })
      ]
    }
  );
});

export { SiTado as default, defaultColor };
