{"version": 3, "names": ["parser", "pluginPasses", "parserOpts", "highlightCode", "filename", "code", "results", "plugins", "plugin", "parserOverride", "ast", "parse", "undefined", "push", "length", "then", "Error", "err", "message", "loc", "missingPlugin", "codeFrame", "codeFrameColumns", "start", "line", "column", "generateMissingPluginMessage"], "sources": ["../../src/parser/index.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\nimport { parse } from \"@babel/parser\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport generateMissingPluginMessage from \"./util/missing-plugin-helper\";\nimport type { PluginPasses } from \"../config\";\n\nexport type ParseResult = ReturnType<typeof parse>;\n\nexport default function* parser(\n  pluginPasses: PluginPasses,\n  { parserOpts, highlightCode = true, filename = \"unknown\" }: any,\n  code: string,\n): Handler<ParseResult> {\n  try {\n    const results = [];\n    for (const plugins of pluginPasses) {\n      for (const plugin of plugins) {\n        const { parserOverride } = plugin;\n        if (parserOverride) {\n          const ast = parserOverride(code, parserOpts, parse);\n\n          if (ast !== undefined) results.push(ast);\n        }\n      }\n    }\n\n    if (results.length === 0) {\n      return parse(code, parserOpts);\n    } else if (results.length === 1) {\n      // @ts-expect-error - If we want to allow async parsers\n      yield* [];\n      if (typeof results[0].then === \"function\") {\n        throw new Error(\n          `You appear to be using an async parser plugin, ` +\n            `which your current version of Babel does not support. ` +\n            `If you're using a published plugin, you may need to upgrade ` +\n            `your @babel/core version.`,\n        );\n      }\n      return results[0];\n    }\n    // TODO: Add an error code\n    throw new Error(\"More than one plugin attempted to override parsing.\");\n  } catch (err) {\n    if (err.code === \"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\") {\n      err.message +=\n        \"\\nConsider renaming the file to '.mjs', or setting sourceType:module \" +\n        \"or sourceType:unambiguous in your Babel config for this file.\";\n      // err.code will be changed to BABEL_PARSE_ERROR later.\n    }\n\n    const { loc, missingPlugin } = err;\n    if (loc) {\n      const codeFrame = codeFrameColumns(\n        code,\n        {\n          start: {\n            line: loc.line,\n            column: loc.column + 1,\n          },\n        },\n        {\n          highlightCode,\n        },\n      );\n      if (missingPlugin) {\n        err.message =\n          `${filename}: ` +\n          generateMissingPluginMessage(missingPlugin[0], loc, codeFrame);\n      } else {\n        err.message = `${filename}: ${err.message}\\n\\n` + codeFrame;\n      }\n      err.code = \"BABEL_PARSE_ERROR\";\n    }\n    throw err;\n  }\n}\n"], "mappings": ";;;;;;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAKe,UAAUA,MAAV,CACbC,YADa,EAEb;EAAEC,UAAF;EAAcC,aAAa,GAAG,IAA9B;EAAoCC,QAAQ,GAAG;AAA/C,CAFa,EAGbC,IAHa,EAIS;EACtB,IAAI;IACF,MAAMC,OAAO,GAAG,EAAhB;;IACA,KAAK,MAAMC,OAAX,IAAsBN,YAAtB,EAAoC;MAClC,KAAK,MAAMO,MAAX,IAAqBD,OAArB,EAA8B;QAC5B,MAAM;UAAEE;QAAF,IAAqBD,MAA3B;;QACA,IAAIC,cAAJ,EAAoB;UAClB,MAAMC,GAAG,GAAGD,cAAc,CAACJ,IAAD,EAAOH,UAAP,EAAmBS,eAAnB,CAA1B;UAEA,IAAID,GAAG,KAAKE,SAAZ,EAAuBN,OAAO,CAACO,IAAR,CAAaH,GAAb;QACxB;MACF;IACF;;IAED,IAAIJ,OAAO,CAACQ,MAAR,KAAmB,CAAvB,EAA0B;MACxB,OAAO,IAAAH,eAAA,EAAMN,IAAN,EAAYH,UAAZ,CAAP;IACD,CAFD,MAEO,IAAII,OAAO,CAACQ,MAAR,KAAmB,CAAvB,EAA0B;MAE/B,OAAO,EAAP;;MACA,IAAI,OAAOR,OAAO,CAAC,CAAD,CAAP,CAAWS,IAAlB,KAA2B,UAA/B,EAA2C;QACzC,MAAM,IAAIC,KAAJ,CACH,iDAAD,GACG,wDADH,GAEG,8DAFH,GAGG,2BAJC,CAAN;MAMD;;MACD,OAAOV,OAAO,CAAC,CAAD,CAAd;IACD;;IAED,MAAM,IAAIU,KAAJ,CAAU,qDAAV,CAAN;EACD,CA9BD,CA8BE,OAAOC,GAAP,EAAY;IACZ,IAAIA,GAAG,CAACZ,IAAJ,KAAa,yCAAjB,EAA4D;MAC1DY,GAAG,CAACC,OAAJ,IACE,0EACA,+DAFF;IAID;;IAED,MAAM;MAAEC,GAAF;MAAOC;IAAP,IAAyBH,GAA/B;;IACA,IAAIE,GAAJ,EAAS;MACP,MAAME,SAAS,GAAG,IAAAC,6BAAA,EAChBjB,IADgB,EAEhB;QACEkB,KAAK,EAAE;UACLC,IAAI,EAAEL,GAAG,CAACK,IADL;UAELC,MAAM,EAAEN,GAAG,CAACM,MAAJ,GAAa;QAFhB;MADT,CAFgB,EAQhB;QACEtB;MADF,CARgB,CAAlB;;MAYA,IAAIiB,aAAJ,EAAmB;QACjBH,GAAG,CAACC,OAAJ,GACG,GAAEd,QAAS,IAAZ,GACA,IAAAsB,4BAAA,EAA6BN,aAAa,CAAC,CAAD,CAA1C,EAA+CD,GAA/C,EAAoDE,SAApD,CAFF;MAGD,CAJD,MAIO;QACLJ,GAAG,CAACC,OAAJ,GAAe,GAAEd,QAAS,KAAIa,GAAG,CAACC,OAAQ,MAA5B,GAAoCG,SAAlD;MACD;;MACDJ,GAAG,CAACZ,IAAJ,GAAW,mBAAX;IACD;;IACD,MAAMY,GAAN;EACD;AACF"}