{"version": 3, "sources": ["../../../src/optimizer/transforms/alternation-to-class.ts"], "sourcesContent": ["import type {AlternativeNode, CharacterClassNode, CharacterNode, CharacterSetNode, NodeCharacterSetKind} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\nimport {isAlternativeContainer} from '../../parser/node-utils.js';\nimport {createAlternative, createCharacterClass} from '../../parser/parse.js';\n\n/**\nUse character classes for adjacent alternatives with single-length values.\n*/\nconst alternationToClass: Visitor = {\n  '*'({node}) {\n    if (!isAlternativeContainer(node) || node.body.length < 2) {\n      return;\n    }\n    const newAlts = [];\n    let ccNodes = [];\n    for (const alt of node.body) {\n      const kid = alt.body[0];\n      if (\n        alt.body.length === 1 &&\n        ( kid.type === 'Character' ||\n          kid.type === 'CharacterClass' ||\n          (kid.type === 'CharacterSet' && universalCharacterSetKinds.has(kid.kind))\n        )\n      ) {\n        ccNodes.push(kid);\n      } else {\n        if (ccNodes.length) {\n          newAlts.push(createAlternativeWithCombinedNodes(ccNodes));\n          ccNodes = [];\n        }\n        newAlts.push(alt);\n      }\n    }\n    if (ccNodes.length) {\n      newAlts.push(createAlternativeWithCombinedNodes(ccNodes));\n    }\n    node.body = newAlts;\n  },\n};\n\nfunction createAlternativeWithCombinedNodes(nodes: Array<CharacterNode | CharacterClassNode | CharacterSetNode>): AlternativeNode {\n  const alt = createAlternative();\n  const node = nodes.length > 1 ? createCharacterClass({body: nodes}) : nodes[0];\n  if (node) {\n    alt.body.push(node);\n  }\n  return alt;\n}\n\n// Character set kinds that can appear inside and outside of character classes, and can be inverted\n// by setting `negate`. Some but not all of those excluded use `variableLength: true`\nconst universalCharacterSetKinds = new Set<NodeCharacterSetKind>([\n  'digit',\n  'hex',\n  'posix',\n  'property',\n  'space',\n  'word',\n]);\n\nexport {\n  alternationToClass,\n};\n"], "mappings": "aAEA,OAAQ,0BAAAA,MAA6B,6BACrC,OAAQ,qBAAAC,EAAmB,wBAAAC,MAA2B,wBAKtD,MAAMC,EAA8B,CAClC,IAAI,CAAC,KAAAC,CAAI,EAAG,CACV,GAAI,CAACJ,EAAuBI,CAAI,GAAKA,EAAK,KAAK,OAAS,EACtD,OAEF,MAAMC,EAAU,CAAC,EACjB,IAAIC,EAAU,CAAC,EACf,UAAWC,KAAOH,EAAK,KAAM,CAC3B,MAAMI,EAAMD,EAAI,KAAK,CAAC,EAEpBA,EAAI,KAAK,SAAW,IAClBC,EAAI,OAAS,aACbA,EAAI,OAAS,kBACZA,EAAI,OAAS,gBAAkBC,EAA2B,IAAID,EAAI,IAAI,GAGzEF,EAAQ,KAAKE,CAAG,GAEZF,EAAQ,SACVD,EAAQ,KAAKK,EAAmCJ,CAAO,CAAC,EACxDA,EAAU,CAAC,GAEbD,EAAQ,KAAKE,CAAG,EAEpB,CACID,EAAQ,QACVD,EAAQ,KAAKK,EAAmCJ,CAAO,CAAC,EAE1DF,EAAK,KAAOC,CACd,CACF,EAEA,SAASK,EAAmCC,EAAsF,CAChI,MAAMJ,EAAMN,EAAkB,EACxBG,EAAOO,EAAM,OAAS,EAAIT,EAAqB,CAAC,KAAMS,CAAK,CAAC,EAAIA,EAAM,CAAC,EAC7E,OAAIP,GACFG,EAAI,KAAK,KAAKH,CAAI,EAEbG,CACT,CAIA,MAAME,EAA6B,IAAI,IAA0B,CAC/D,QACA,MACA,QACA,WACA,QACA,MACF,CAAC,EAED,OACEN,KAAA", "names": ["isAlternativeContainer", "createAlternative", "createCharacterClass", "alternationToClass", "node", "newAlts", "ccNodes", "alt", "kid", "universalCharacterSetKinds", "createAlternativeWithCombinedNodes", "nodes"]}