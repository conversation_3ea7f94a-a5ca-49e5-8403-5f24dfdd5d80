import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useAI } from '@/context/AIContext';
import {
  Bot,
  User,
  Code,
  FileText,
  Lightbulb,
  Send,
  Sparkles,
  Trash2
} from 'lucide-react';

interface AIAgentProps {
  onCodeInsert?: (code: string, language: string) => void;
  currentFile?: string;
  selectedCode?: string;
}

const AIAgent: React.FC<AIAgentProps> = ({
  onCodeInsert,
  currentFile,
  selectedCode
}) => {
  const { messages, isLoading, sendMessage, clearMessages } = useAI();
  const [input, setInput] = useState('');
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const context = {
      file: currentFile,
      selectedCode: selectedCode
    };

    await sendMessage(input, context);
    setInput('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
  };

  const handleCodeInsert = (code: string, language: string) => {
    if (onCodeInsert) {
      onCodeInsert(code, language);
    }
  };

  return (
    <div className="ai-agent h-full flex flex-col bg-surface-primary">
      <div className="ai-header p-4 border-b border-surface-tertiary">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="text-neon-purple" size={20} />
            <h2 className="text-lg font-semibold text-neon-purple">AI Assistant</h2>
            <Badge variant="outline" className="text-xs">
              <Sparkles size={12} className="mr-1" />
              Active
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearMessages}
            className="h-8 w-8 p-0 hover:bg-red-500/20"
          >
            <Trash2 size={14} className="text-gray-400 hover:text-red-400" />
          </Button>
        </div>
        {currentFile && (
          <p className="text-xs text-gray-400 mt-1">
            Working on: {currentFile}
          </p>
        )}
      </div>

      <ScrollArea className="flex-1 p-4" ref={scrollRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="mb-4">
              <div className="flex items-start space-x-2">
                {message.type === 'user' ? (
                  <User size={16} className="text-neon-blue mt-1" />
                ) : message.type === 'ai' ? (
                  <Bot size={16} className="text-neon-purple mt-1" />
                ) : (
                  <Sparkles size={16} className="text-gray-400 mt-1" />
                )}
                <div className="flex-1">
                  <div className={`p-3 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-neon-blue/20 text-neon-blue'
                      : message.type === 'ai'
                      ? 'bg-neon-purple/20 text-gray-200'
                      : 'bg-surface-secondary text-gray-400'
                  }`}>
                    {message.content}
                  </div>

                  {message.code && (
                    <div className="mt-2 bg-surface-secondary rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Code size={16} className="text-neon-purple" />
                        <span className="text-sm font-medium">Code Suggestion</span>
                      </div>
                      <pre className="bg-surface-tertiary p-3 rounded-md text-sm code-font overflow-x-auto">
                        <code>{message.code}</code>
                      </pre>
                      <div className="flex space-x-2 mt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCodeInsert(message.code!, message.language!)}
                        >
                          Insert Code
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => navigator.clipboard.writeText(message.code!)}
                        >
                          Copy
                        </Button>
                      </div>
                    </div>
                  )}

                  {message.suggestions && (
                    <div className="mt-2 space-y-1">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          className="justify-start h-auto p-2 text-left hover:bg-neon-purple/10"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          <Lightbulb size={12} className="mr-2 text-neon-purple" />
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="mb-4">
              <div className="flex items-center space-x-2">
                <Bot size={16} className="text-neon-purple animate-pulse" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="ai-input-area p-4 border-t border-surface-tertiary">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask AI about your code, request explanations, or get suggestions..."
            disabled={isLoading}
            className="flex-1 neon-input"
          />
          <Button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="neon-button"
          >
            <Send size={16} />
          </Button>
        </form>
      </div>
    </div>
  );
};

export default AIAgent;
