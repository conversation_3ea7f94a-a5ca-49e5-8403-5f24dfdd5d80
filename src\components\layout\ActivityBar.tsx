import React from 'react';
import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Files, 
  Search, 
  GitBranch, 
  Settings, 
  Bot,
  Terminal
} from 'lucide-react';

interface ActivityBarProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

const ActivityBar: React.FC<ActivityBarProps> = ({ activeView, onViewChange }) => {
  const activities = [
    { id: 'explorer', icon: Files, label: 'Explorer' },
    { id: 'search', icon: Search, label: 'Search' },
    { id: 'git', icon: GitBranch, label: 'Source Control' },
    { id: 'ai', icon: Bot, label: 'AI Assistant' },
    { id: 'terminal', icon: Terminal, label: 'Terminal' },
  ];

  const bottomActivities = [
    { id: 'settings', icon: Settings, label: 'Settings' },
  ];

  return (
    <div className="activity-bar w-12 h-full bg-surface-primary border-r border-surface-tertiary flex flex-col">
      <TooltipProvider>
        <div className="flex-1 flex flex-col py-2">
          {activities.map((activity) => {
            const Icon = activity.icon;
            return (
              <Tooltip key={activity.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`w-10 h-10 mx-1 mb-1 p-0 ${
                      activeView === activity.id 
                        ? 'bg-neon-purple/20 text-neon-purple border-l-2 border-neon-purple' 
                        : 'text-gray-400 hover:text-neon-purple hover:bg-neon-purple/10'
                    }`}
                    onClick={() => onViewChange(activity.id)}
                  >
                    <Icon size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{activity.label}</p>
                </TooltipContent>
              </Tooltip>
            );
          })}
        </div>
        
        <div className="flex flex-col py-2">
          {bottomActivities.map((activity) => {
            const Icon = activity.icon;
            return (
              <Tooltip key={activity.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`w-10 h-10 mx-1 mb-1 p-0 ${
                      activeView === activity.id 
                        ? 'bg-neon-purple/20 text-neon-purple border-l-2 border-neon-purple' 
                        : 'text-gray-400 hover:text-neon-purple hover:bg-neon-purple/10'
                    }`}
                    onClick={() => onViewChange(activity.id)}
                  >
                    <Icon size={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{activity.label}</p>
                </TooltipContent>
              </Tooltip>
            );
          })}
        </div>
      </TooltipProvider>
    </div>
  );
};

export default ActivityBar;
