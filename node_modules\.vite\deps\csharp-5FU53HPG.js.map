{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/csharp/csharp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/csharp/csharp.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\$\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".cs\",\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"extern\",\n    \"alias\",\n    \"using\",\n    \"bool\",\n    \"decimal\",\n    \"sbyte\",\n    \"byte\",\n    \"short\",\n    \"ushort\",\n    \"int\",\n    \"uint\",\n    \"long\",\n    \"ulong\",\n    \"char\",\n    \"float\",\n    \"double\",\n    \"object\",\n    \"dynamic\",\n    \"string\",\n    \"assembly\",\n    \"is\",\n    \"as\",\n    \"ref\",\n    \"out\",\n    \"this\",\n    \"base\",\n    \"new\",\n    \"typeof\",\n    \"void\",\n    \"checked\",\n    \"unchecked\",\n    \"default\",\n    \"delegate\",\n    \"var\",\n    \"const\",\n    \"if\",\n    \"else\",\n    \"switch\",\n    \"case\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"foreach\",\n    \"in\",\n    \"break\",\n    \"continue\",\n    \"goto\",\n    \"return\",\n    \"throw\",\n    \"try\",\n    \"catch\",\n    \"finally\",\n    \"lock\",\n    \"yield\",\n    \"from\",\n    \"let\",\n    \"where\",\n    \"join\",\n    \"on\",\n    \"equals\",\n    \"into\",\n    \"orderby\",\n    \"ascending\",\n    \"descending\",\n    \"select\",\n    \"group\",\n    \"by\",\n    \"namespace\",\n    \"partial\",\n    \"class\",\n    \"field\",\n    \"event\",\n    \"method\",\n    \"param\",\n    \"public\",\n    \"protected\",\n    \"internal\",\n    \"private\",\n    \"abstract\",\n    \"sealed\",\n    \"static\",\n    \"struct\",\n    \"readonly\",\n    \"volatile\",\n    \"virtual\",\n    \"override\",\n    \"params\",\n    \"get\",\n    \"set\",\n    \"add\",\n    \"remove\",\n    \"operator\",\n    \"true\",\n    \"false\",\n    \"implicit\",\n    \"explicit\",\n    \"interface\",\n    \"enum\",\n    \"null\",\n    \"async\",\n    \"await\",\n    \"fixed\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"unsafe\",\n    \"nameof\",\n    \"when\"\n  ],\n  namespaceFollows: [\"namespace\", \"using\"],\n  parenFollows: [\"if\", \"for\", \"while\", \"switch\", \"foreach\", \"using\", \"catch\", \"when\"],\n  operators: [\n    \"=\",\n    \"??\",\n    \"||\",\n    \"&&\",\n    \"|\",\n    \"^\",\n    \"&\",\n    \"==\",\n    \"!=\",\n    \"<=\",\n    \">=\",\n    \"<<\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"!\",\n    \"~\",\n    \"++\",\n    \"--\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"&=\",\n    \"|=\",\n    \"^=\",\n    \"<<=\",\n    \">>=\",\n    \">>\",\n    \"=>\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [\n        /\\@?[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@keywords\": {\n              token: \"keyword.$0\",\n              next: \"@qualified\"\n            },\n            \"@default\": { token: \"identifier\", next: \"@qualified\" }\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [\n        /}/,\n        {\n          cases: {\n            \"$S2==interpolatedstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"$S2==litinterpstring\": {\n              token: \"string.quote\",\n              next: \"@pop\"\n            },\n            \"@default\": \"@brackets\"\n          }\n        }\n      ],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/[0-9_]*\\.[0-9_]+([eE][\\-+]?\\d+)?[fFdD]?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]+/, \"number.hex\"],\n      [/0[bB][01_]+/, \"number.hex\"],\n      [/[0-9_]+/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@string\" }],\n      [/\\$\\@\"/, { token: \"string.quote\", next: \"@litinterpstring\" }],\n      [/\\@\"/, { token: \"string.quote\", next: \"@litstring\" }],\n      [/\\$\"/, { token: \"string.quote\", next: \"@interpolatedstring\" }],\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    qualified: [\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/\\./, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Z]\\w*/, \"namespace\"],\n      [/[\\.=]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litstring: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    litinterpstring: [\n      [/[^\"{]+/, \"string\"],\n      [/\"\"/, \"string.escape\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.litinterpstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    interpolatedstring: [\n      [/[^\\\\\"{]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/{{/, \"string.escape\"],\n      [/}}/, \"string.escape\"],\n      [/{/, { token: \"string.quote\", next: \"root.interpolatedstring\" }],\n      [/\"/, { token: \"string.quote\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/^[ \\t\\v\\f]*#((r)|(load))(?=\\s)/, \"directive.csx\"],\n      [/^[ \\t\\v\\f]*#\\w.*$/, \"namespace.cpp\"],\n      [/[ \\t\\v\\f\\r\\n]+/, \"\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACtC;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC,aAAa,OAAO;AAAA,EACvC,cAAc,CAAC,MAAM,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,MAAM;AAAA,EAClF,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,qBAAqB;AAAA,cACnB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,aAAa;AAAA,cACX,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY,EAAE,OAAO,cAAc,MAAM,aAAa;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,2BAA2B;AAAA,cACzB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,wBAAwB;AAAA,cACtB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,2CAA2C,cAAc;AAAA,MAC1D,CAAC,sBAAsB,YAAY;AAAA,MACnC,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,UAAU,CAAC;AAAA,MAChD,CAAC,SAAS,EAAE,OAAO,gBAAgB,MAAM,mBAAmB,CAAC;AAAA,MAC7D,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,aAAa,CAAC;AAAA,MACrD,CAAC,OAAO,EAAE,OAAO,gBAAgB,MAAM,sBAAsB,CAAC;AAAA,MAC9D,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,WAAW;AAAA,MACT;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,MAAM,WAAW;AAAA,MAClB,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA,IACA,WAAW;AAAA,MACT,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,UAAU,QAAQ;AAAA,MACnB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAAA,MAC7D,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,MAAM,eAAe;AAAA,MACtB,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AAAA,MAChE,CAAC,KAAK,EAAE,OAAO,gBAAgB,MAAM,OAAO,CAAC;AAAA,IAC/C;AAAA,IACA,YAAY;AAAA,MACV,CAAC,kCAAkC,eAAe;AAAA,MAClD,CAAC,qBAAqB,eAAe;AAAA,MACrC,CAAC,kBAAkB,EAAE;AAAA,MACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,EACF;AACF;", "names": []}