{"version": 3, "names": ["DEPRECATED_KEYS", "FLIPPED_ALIAS_KEYS", "TYPES", "isVirtualType", "type", "virtualTypes", "explode", "visitor", "_exploded", "nodeType", "Object", "keys", "should<PERSON><PERSON>re<PERSON>ey", "parts", "split", "length", "fns", "part", "verify", "__esModule", "ensureEntranceObjects", "ensureCallbackArrays", "wrapCheck", "types", "mergePair", "aliases", "deprecated<PERSON>ey", "console", "trace", "alias", "existing", "_verified", "Error", "validateVisitorMethods", "indexOf", "visitors", "<PERSON><PERSON><PERSON>", "path", "val", "concat", "fn", "TypeError", "merge", "states", "wrapper", "rootVisitor", "i", "state", "visitorType", "wrapWithStateOrWrapper", "nodeVisitor", "oldVisitor", "newVisitor", "key", "Array", "isArray", "map", "newFn", "call", "toString", "obj", "enter", "exit", "apply", "arguments", "dest", "src"], "sources": ["../src/visitors.ts"], "sourcesContent": ["import * as virtualTypes from \"./path/lib/virtual-types\";\nimport { DEPRECATED_KEYS, FLIPPED_ALIAS_KEYS, TYPES } from \"@babel/types\";\nimport type { <PERSON>de<PERSON><PERSON>, Visitor } from \"./index\";\n\ntype VIRTUAL_TYPES = keyof typeof virtualTypes;\nfunction isVirtualType(type: string): type is VIRTUAL_TYPES {\n  return type in virtualTypes;\n}\n\n/**\n * explode() will take a visitor object with all of the various shorthands\n * that we support, and validates & normalizes it into a common format, ready\n * to be used in traversal\n *\n * The various shorthands are:\n * * `Identifier() { ... }` -> `Identifier: { enter() { ... } }`\n * * `\"Identifier|NumericLiteral\": { ... }` -> `Identifier: { ... }, NumericLiteral: { ... }`\n * * Aliases in `@babel/types`: e.g. `Property: { ... }` -> `ObjectProperty: { ... }, ClassProperty: { ... }`\n *\n * Other normalizations are:\n * * Visitors of virtual types are wrapped, so that they are only visited when\n *   their dynamic check passes\n * * `enter` and `exit` functions are wrapped in arrays, to ease merging of\n *   visitors\n */\nexport function explode(visitor: Visitor) {\n  if (visitor._exploded) return visitor;\n  visitor._exploded = true;\n\n  // normalise pipes\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    const parts: Array<string> = nodeType.split(\"|\");\n    if (parts.length === 1) continue;\n\n    const fns = visitor[nodeType];\n    delete visitor[nodeType];\n\n    for (const part of parts) {\n      // @ts-expect-error part will be verified by `verify` later\n      visitor[part] = fns;\n    }\n  }\n\n  // verify data structure\n  verify(visitor);\n\n  // make sure there's no __esModule type since this is because we're using loose mode\n  // and it sets __esModule to be enumerable on all modules :(\n  // @ts-expect-error ESModule interop\n  delete visitor.__esModule;\n\n  // ensure visitors are objects\n  ensureEntranceObjects(visitor);\n\n  // ensure enter/exit callbacks are arrays\n  ensureCallbackArrays(visitor);\n\n  // add type wrappers\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (!isVirtualType(nodeType)) continue;\n\n    // wrap all the functions\n    const fns = visitor[nodeType];\n    for (const type of Object.keys(fns)) {\n      // @ts-expect-error manipulating visitors\n      fns[type] = wrapCheck(nodeType, fns[type]);\n    }\n\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    const types = virtualTypes[nodeType];\n    if (types !== null) {\n      for (const type of types) {\n        // merge the visitor if necessary or just put it back in\n        if (visitor[type]) {\n          mergePair(visitor[type], fns);\n        } else {\n          // @ts-expect-error Expression produces too complex union\n          visitor[type] = fns;\n        }\n      }\n    } else {\n      mergePair(visitor, fns);\n    }\n  }\n\n  // add aliases\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    const fns = visitor[nodeType];\n\n    let aliases = FLIPPED_ALIAS_KEYS[nodeType];\n\n    const deprecatedKey = DEPRECATED_KEYS[nodeType];\n    if (deprecatedKey) {\n      console.trace(\n        `Visitor defined for ${nodeType} but it has been renamed to ${deprecatedKey}`,\n      );\n      aliases = [deprecatedKey];\n    }\n\n    if (!aliases) continue;\n\n    // clear it from the visitor\n    delete visitor[nodeType];\n\n    for (const alias of aliases) {\n      const existing = visitor[alias];\n      if (existing) {\n        mergePair(existing, fns);\n      } else {\n        // @ts-expect-error Expression produces a union type that is too complex to represent.\n        visitor[alias] = { ...fns };\n      }\n    }\n  }\n\n  for (const nodeType of Object.keys(visitor)) {\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    ensureCallbackArrays(\n      // @ts-expect-error nodeType must present in visitor after previous validations\n      visitor[nodeType],\n    );\n  }\n\n  return visitor;\n}\n\nexport function verify(visitor: Visitor) {\n  if (visitor._verified) return;\n\n  if (typeof visitor === \"function\") {\n    throw new Error(\n      \"You passed `traverse()` a function when it expected a visitor object, \" +\n        \"are you sure you didn't mean `{ enter: Function }`?\",\n    );\n  }\n\n  for (const nodeType of Object.keys(visitor) as (keyof Visitor)[]) {\n    if (nodeType === \"enter\" || nodeType === \"exit\") {\n      validateVisitorMethods(nodeType, visitor[nodeType]);\n    }\n\n    if (shouldIgnoreKey(nodeType)) continue;\n\n    if (TYPES.indexOf(nodeType) < 0) {\n      throw new Error(\n        `You gave us a visitor for the node type ${nodeType} but it's not a valid type`,\n      );\n    }\n\n    const visitors = visitor[nodeType];\n    if (typeof visitors === \"object\") {\n      for (const visitorKey of Object.keys(visitors)) {\n        if (visitorKey === \"enter\" || visitorKey === \"exit\") {\n          // verify that it just contains functions\n          validateVisitorMethods(\n            `${nodeType}.${visitorKey}`,\n            visitors[visitorKey],\n          );\n        } else {\n          throw new Error(\n            \"You passed `traverse()` a visitor object with the property \" +\n              `${nodeType} that has the invalid property ${visitorKey}`,\n          );\n        }\n      }\n    }\n  }\n\n  visitor._verified = true;\n}\n\nfunction validateVisitorMethods(\n  path: string,\n  val: any,\n): asserts val is Function | Function[] {\n  const fns = [].concat(val);\n  for (const fn of fns) {\n    if (typeof fn !== \"function\") {\n      throw new TypeError(\n        `Non-function found defined in ${path} with type ${typeof fn}`,\n      );\n    }\n  }\n}\n\nexport function merge<State>(visitors: Visitor<State>[]): Visitor<State>;\nexport function merge(\n  visitors: Visitor<unknown>[],\n  states?: any[],\n  wrapper?: Function | null,\n): Visitor<unknown>;\nexport function merge(\n  visitors: any[],\n  states: any[] = [],\n  wrapper?: Function | null,\n) {\n  const rootVisitor: Visitor = {};\n\n  for (let i = 0; i < visitors.length; i++) {\n    const visitor = visitors[i];\n    const state = states[i];\n\n    explode(visitor);\n\n    for (const type of Object.keys(visitor) as (keyof Visitor)[]) {\n      let visitorType = visitor[type];\n\n      // if we have state or wrapper then overload the callbacks to take it\n      if (state || wrapper) {\n        visitorType = wrapWithStateOrWrapper(visitorType, state, wrapper);\n      }\n\n      // @ts-expect-error: Expression produces a union type that is too complex to represent.\n      const nodeVisitor = (rootVisitor[type] ||= {});\n      mergePair(nodeVisitor, visitorType);\n    }\n  }\n\n  return rootVisitor;\n}\n\nfunction wrapWithStateOrWrapper<State>(\n  oldVisitor: Visitor<State>,\n  state: State,\n  wrapper?: Function | null,\n) {\n  const newVisitor: Visitor = {};\n\n  for (const key of Object.keys(oldVisitor) as (keyof Visitor<State>)[]) {\n    let fns = oldVisitor[key];\n\n    // not an enter/exit array of callbacks\n    if (!Array.isArray(fns)) continue;\n\n    // @ts-expect-error manipulating visitors\n    fns = fns.map(function (fn) {\n      let newFn = fn;\n\n      if (state) {\n        newFn = function (path: NodePath) {\n          return fn.call(state, path, state);\n        };\n      }\n\n      if (wrapper) {\n        // @ts-expect-error Fixme: document state.key\n        newFn = wrapper(state.key, key, newFn);\n      }\n\n      // Override toString in case this function is printed, we want to print the wrapped function, same as we do in `wrapCheck`\n      if (newFn !== fn) {\n        newFn.toString = () => fn.toString();\n      }\n\n      return newFn;\n    });\n\n    // @ts-expect-error: Expression produces a union type that is too complex to represent.\n    newVisitor[key] = fns;\n  }\n\n  return newVisitor;\n}\n\nfunction ensureEntranceObjects(obj: Visitor) {\n  for (const key of Object.keys(obj) as (keyof Visitor)[]) {\n    if (shouldIgnoreKey(key)) continue;\n\n    const fns = obj[key];\n    if (typeof fns === \"function\") {\n      // @ts-expect-error: Expression produces a union type that is too complex to represent.\n      obj[key] = { enter: fns };\n    }\n  }\n}\n\nfunction ensureCallbackArrays(obj: Visitor) {\n  // @ts-expect-error normalizing enter property\n  if (obj.enter && !Array.isArray(obj.enter)) obj.enter = [obj.enter];\n  // @ts-expect-error normalizing exit property\n  if (obj.exit && !Array.isArray(obj.exit)) obj.exit = [obj.exit];\n}\n\nfunction wrapCheck(nodeType: VIRTUAL_TYPES, fn: Function) {\n  const newFn = function (this: unknown, path: NodePath) {\n    if (path[`is${nodeType}`]()) {\n      return fn.apply(this, arguments);\n    }\n  };\n  newFn.toString = () => fn.toString();\n  return newFn;\n}\n\nfunction shouldIgnoreKey(\n  key: string,\n): key is\n  | \"enter\"\n  | \"exit\"\n  | \"shouldSkip\"\n  | \"denylist\"\n  | \"noScope\"\n  | \"skipKeys\"\n  | \"blacklist\" {\n  // internal/hidden key\n  if (key[0] === \"_\") return true;\n\n  // ignore function keys\n  if (key === \"enter\" || key === \"exit\" || key === \"shouldSkip\") return true;\n\n  // ignore other options\n  if (\n    key === \"denylist\" ||\n    key === \"noScope\" ||\n    key === \"skipKeys\" ||\n    // TODO: Remove in Babel 8\n    key === \"blacklist\"\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction mergePair(dest: any, src: any) {\n  for (const key of Object.keys(src)) {\n    dest[key] = [].concat(dest[key] || [], src[key]);\n  }\n}\n"], "mappings": ";;;;;;;;;AAAA;;AACA;;;EAASA,e;EAAiBC,kB;EAAoBC;;;AAI9C,SAASC,aAAT,CAAuBC,IAAvB,EAA4D;EAC1D,OAAOA,IAAI,IAAIC,YAAf;AACD;;AAkBM,SAASC,OAAT,CAAiBC,OAAjB,EAAmC;EACxC,IAAIA,OAAO,CAACC,SAAZ,EAAuB,OAAOD,OAAP;EACvBA,OAAO,CAACC,SAAR,GAAoB,IAApB;;EAGA,KAAK,MAAMC,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAvB,EAAkE;IAChE,IAAIK,eAAe,CAACH,QAAD,CAAnB,EAA+B;IAE/B,MAAMI,KAAoB,GAAGJ,QAAQ,CAACK,KAAT,CAAe,GAAf,CAA7B;IACA,IAAID,KAAK,CAACE,MAAN,KAAiB,CAArB,EAAwB;IAExB,MAAMC,GAAG,GAAGT,OAAO,CAACE,QAAD,CAAnB;IACA,OAAOF,OAAO,CAACE,QAAD,CAAd;;IAEA,KAAK,MAAMQ,IAAX,IAAmBJ,KAAnB,EAA0B;MAExBN,OAAO,CAACU,IAAD,CAAP,GAAgBD,GAAhB;IACD;EACF;;EAGDE,MAAM,CAACX,OAAD,CAAN;EAKA,OAAOA,OAAO,CAACY,UAAf;EAGAC,qBAAqB,CAACb,OAAD,CAArB;EAGAc,oBAAoB,CAACd,OAAD,CAApB;;EAGA,KAAK,MAAME,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAvB,EAA6C;IAC3C,IAAIK,eAAe,CAACH,QAAD,CAAnB,EAA+B;IAE/B,IAAI,CAACN,aAAa,CAACM,QAAD,CAAlB,EAA8B;IAG9B,MAAMO,GAAG,GAAGT,OAAO,CAACE,QAAD,CAAnB;;IACA,KAAK,MAAML,IAAX,IAAmBM,MAAM,CAACC,IAAP,CAAYK,GAAZ,CAAnB,EAAqC;MAEnCA,GAAG,CAACZ,IAAD,CAAH,GAAYkB,SAAS,CAACb,QAAD,EAAWO,GAAG,CAACZ,IAAD,CAAd,CAArB;IACD;;IAGD,OAAOG,OAAO,CAACE,QAAD,CAAd;IAEA,MAAMc,KAAK,GAAGlB,YAAY,CAACI,QAAD,CAA1B;;IACA,IAAIc,KAAK,KAAK,IAAd,EAAoB;MAClB,KAAK,MAAMnB,IAAX,IAAmBmB,KAAnB,EAA0B;QAExB,IAAIhB,OAAO,CAACH,IAAD,CAAX,EAAmB;UACjBoB,SAAS,CAACjB,OAAO,CAACH,IAAD,CAAR,EAAgBY,GAAhB,CAAT;QACD,CAFD,MAEO;UAELT,OAAO,CAACH,IAAD,CAAP,GAAgBY,GAAhB;QACD;MACF;IACF,CAVD,MAUO;MACLQ,SAAS,CAACjB,OAAD,EAAUS,GAAV,CAAT;IACD;EACF;;EAGD,KAAK,MAAMP,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAvB,EAAkE;IAChE,IAAIK,eAAe,CAACH,QAAD,CAAnB,EAA+B;IAE/B,MAAMO,GAAG,GAAGT,OAAO,CAACE,QAAD,CAAnB;IAEA,IAAIgB,OAAO,GAAGxB,kBAAkB,CAACQ,QAAD,CAAhC;IAEA,MAAMiB,aAAa,GAAG1B,eAAe,CAACS,QAAD,CAArC;;IACA,IAAIiB,aAAJ,EAAmB;MACjBC,OAAO,CAACC,KAAR,CACG,uBAAsBnB,QAAS,+BAA8BiB,aAAc,EAD9E;MAGAD,OAAO,GAAG,CAACC,aAAD,CAAV;IACD;;IAED,IAAI,CAACD,OAAL,EAAc;IAGd,OAAOlB,OAAO,CAACE,QAAD,CAAd;;IAEA,KAAK,MAAMoB,KAAX,IAAoBJ,OAApB,EAA6B;MAC3B,MAAMK,QAAQ,GAAGvB,OAAO,CAACsB,KAAD,CAAxB;;MACA,IAAIC,QAAJ,EAAc;QACZN,SAAS,CAACM,QAAD,EAAWd,GAAX,CAAT;MACD,CAFD,MAEO;QAELT,OAAO,CAACsB,KAAD,CAAP,qBAAsBb,GAAtB;MACD;IACF;EACF;;EAED,KAAK,MAAMP,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAvB,EAA6C;IAC3C,IAAIK,eAAe,CAACH,QAAD,CAAnB,EAA+B;IAE/BY,oBAAoB,CAElBd,OAAO,CAACE,QAAD,CAFW,CAApB;EAID;;EAED,OAAOF,OAAP;AACD;;AAEM,SAASW,MAAT,CAAgBX,OAAhB,EAAkC;EACvC,IAAIA,OAAO,CAACwB,SAAZ,EAAuB;;EAEvB,IAAI,OAAOxB,OAAP,KAAmB,UAAvB,EAAmC;IACjC,MAAM,IAAIyB,KAAJ,CACJ,2EACE,qDAFE,CAAN;EAID;;EAED,KAAK,MAAMvB,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAvB,EAAkE;IAChE,IAAIE,QAAQ,KAAK,OAAb,IAAwBA,QAAQ,KAAK,MAAzC,EAAiD;MAC/CwB,sBAAsB,CAACxB,QAAD,EAAWF,OAAO,CAACE,QAAD,CAAlB,CAAtB;IACD;;IAED,IAAIG,eAAe,CAACH,QAAD,CAAnB,EAA+B;;IAE/B,IAAIP,KAAK,CAACgC,OAAN,CAAczB,QAAd,IAA0B,CAA9B,EAAiC;MAC/B,MAAM,IAAIuB,KAAJ,CACH,2CAA0CvB,QAAS,4BADhD,CAAN;IAGD;;IAED,MAAM0B,QAAQ,GAAG5B,OAAO,CAACE,QAAD,CAAxB;;IACA,IAAI,OAAO0B,QAAP,KAAoB,QAAxB,EAAkC;MAChC,KAAK,MAAMC,UAAX,IAAyB1B,MAAM,CAACC,IAAP,CAAYwB,QAAZ,CAAzB,EAAgD;QAC9C,IAAIC,UAAU,KAAK,OAAf,IAA0BA,UAAU,KAAK,MAA7C,EAAqD;UAEnDH,sBAAsB,CACnB,GAAExB,QAAS,IAAG2B,UAAW,EADN,EAEpBD,QAAQ,CAACC,UAAD,CAFY,CAAtB;QAID,CAND,MAMO;UACL,MAAM,IAAIJ,KAAJ,CACJ,gEACG,GAAEvB,QAAS,kCAAiC2B,UAAW,EAFtD,CAAN;QAID;MACF;IACF;EACF;;EAED7B,OAAO,CAACwB,SAAR,GAAoB,IAApB;AACD;;AAED,SAASE,sBAAT,CACEI,IADF,EAEEC,GAFF,EAGwC;EACtC,MAAMtB,GAAG,GAAG,GAAGuB,MAAH,CAAUD,GAAV,CAAZ;;EACA,KAAK,MAAME,EAAX,IAAiBxB,GAAjB,EAAsB;IACpB,IAAI,OAAOwB,EAAP,KAAc,UAAlB,EAA8B;MAC5B,MAAM,IAAIC,SAAJ,CACH,iCAAgCJ,IAAK,cAAa,OAAOG,EAAG,EADzD,CAAN;IAGD;EACF;AACF;;AAQM,SAASE,KAAT,CACLP,QADK,EAELQ,MAAa,GAAG,EAFX,EAGLC,OAHK,EAIL;EACA,MAAMC,WAAoB,GAAG,EAA7B;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGX,QAAQ,CAACpB,MAA7B,EAAqC+B,CAAC,EAAtC,EAA0C;IACxC,MAAMvC,OAAO,GAAG4B,QAAQ,CAACW,CAAD,CAAxB;IACA,MAAMC,KAAK,GAAGJ,MAAM,CAACG,CAAD,CAApB;IAEAxC,OAAO,CAACC,OAAD,CAAP;;IAEA,KAAK,MAAMH,IAAX,IAAmBM,MAAM,CAACC,IAAP,CAAYJ,OAAZ,CAAnB,EAA8D;MAC5D,IAAIyC,WAAW,GAAGzC,OAAO,CAACH,IAAD,CAAzB;;MAGA,IAAI2C,KAAK,IAAIH,OAAb,EAAsB;QACpBI,WAAW,GAAGC,sBAAsB,CAACD,WAAD,EAAcD,KAAd,EAAqBH,OAArB,CAApC;MACD;;MAGD,MAAMM,WAAW,GAAIL,WAAW,CAACzC,IAAD,CAAf,KAAIyC,WAAW,CAACzC,IAAD,CAAf,GAA0B,EAA1B,CAAjB;MACAoB,SAAS,CAAC0B,WAAD,EAAcF,WAAd,CAAT;IACD;EACF;;EAED,OAAOH,WAAP;AACD;;AAED,SAASI,sBAAT,CACEE,UADF,EAEEJ,KAFF,EAGEH,OAHF,EAIE;EACA,MAAMQ,UAAmB,GAAG,EAA5B;;EAEA,KAAK,MAAMC,GAAX,IAAkB3C,MAAM,CAACC,IAAP,CAAYwC,UAAZ,CAAlB,EAAuE;IACrE,IAAInC,GAAG,GAAGmC,UAAU,CAACE,GAAD,CAApB;IAGA,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcvC,GAAd,CAAL,EAAyB;IAGzBA,GAAG,GAAGA,GAAG,CAACwC,GAAJ,CAAQ,UAAUhB,EAAV,EAAc;MAC1B,IAAIiB,KAAK,GAAGjB,EAAZ;;MAEA,IAAIO,KAAJ,EAAW;QACTU,KAAK,GAAG,UAAUpB,IAAV,EAA0B;UAChC,OAAOG,EAAE,CAACkB,IAAH,CAAQX,KAAR,EAAeV,IAAf,EAAqBU,KAArB,CAAP;QACD,CAFD;MAGD;;MAED,IAAIH,OAAJ,EAAa;QAEXa,KAAK,GAAGb,OAAO,CAACG,KAAK,CAACM,GAAP,EAAYA,GAAZ,EAAiBI,KAAjB,CAAf;MACD;;MAGD,IAAIA,KAAK,KAAKjB,EAAd,EAAkB;QAChBiB,KAAK,CAACE,QAAN,GAAiB,MAAMnB,EAAE,CAACmB,QAAH,EAAvB;MACD;;MAED,OAAOF,KAAP;IACD,CApBK,CAAN;IAuBAL,UAAU,CAACC,GAAD,CAAV,GAAkBrC,GAAlB;EACD;;EAED,OAAOoC,UAAP;AACD;;AAED,SAAShC,qBAAT,CAA+BwC,GAA/B,EAA6C;EAC3C,KAAK,MAAMP,GAAX,IAAkB3C,MAAM,CAACC,IAAP,CAAYiD,GAAZ,CAAlB,EAAyD;IACvD,IAAIhD,eAAe,CAACyC,GAAD,CAAnB,EAA0B;IAE1B,MAAMrC,GAAG,GAAG4C,GAAG,CAACP,GAAD,CAAf;;IACA,IAAI,OAAOrC,GAAP,KAAe,UAAnB,EAA+B;MAE7B4C,GAAG,CAACP,GAAD,CAAH,GAAW;QAAEQ,KAAK,EAAE7C;MAAT,CAAX;IACD;EACF;AACF;;AAED,SAASK,oBAAT,CAA8BuC,GAA9B,EAA4C;EAE1C,IAAIA,GAAG,CAACC,KAAJ,IAAa,CAACP,KAAK,CAACC,OAAN,CAAcK,GAAG,CAACC,KAAlB,CAAlB,EAA4CD,GAAG,CAACC,KAAJ,GAAY,CAACD,GAAG,CAACC,KAAL,CAAZ;EAE5C,IAAID,GAAG,CAACE,IAAJ,IAAY,CAACR,KAAK,CAACC,OAAN,CAAcK,GAAG,CAACE,IAAlB,CAAjB,EAA0CF,GAAG,CAACE,IAAJ,GAAW,CAACF,GAAG,CAACE,IAAL,CAAX;AAC3C;;AAED,SAASxC,SAAT,CAAmBb,QAAnB,EAA4C+B,EAA5C,EAA0D;EACxD,MAAMiB,KAAK,GAAG,UAAyBpB,IAAzB,EAAyC;IACrD,IAAIA,IAAI,CAAE,KAAI5B,QAAS,EAAf,CAAJ,EAAJ,EAA6B;MAC3B,OAAO+B,EAAE,CAACuB,KAAH,CAAS,IAAT,EAAeC,SAAf,CAAP;IACD;EACF,CAJD;;EAKAP,KAAK,CAACE,QAAN,GAAiB,MAAMnB,EAAE,CAACmB,QAAH,EAAvB;;EACA,OAAOF,KAAP;AACD;;AAED,SAAS7C,eAAT,CACEyC,GADF,EASgB;EAEd,IAAIA,GAAG,CAAC,CAAD,CAAH,KAAW,GAAf,EAAoB,OAAO,IAAP;EAGpB,IAAIA,GAAG,KAAK,OAAR,IAAmBA,GAAG,KAAK,MAA3B,IAAqCA,GAAG,KAAK,YAAjD,EAA+D,OAAO,IAAP;;EAG/D,IACEA,GAAG,KAAK,UAAR,IACAA,GAAG,KAAK,SADR,IAEAA,GAAG,KAAK,UAFR,IAIAA,GAAG,KAAK,WALV,EAME;IACA,OAAO,IAAP;EACD;;EAED,OAAO,KAAP;AACD;;AAED,SAAS7B,SAAT,CAAmByC,IAAnB,EAA8BC,GAA9B,EAAwC;EACtC,KAAK,MAAMb,GAAX,IAAkB3C,MAAM,CAACC,IAAP,CAAYuD,GAAZ,CAAlB,EAAoC;IAClCD,IAAI,CAACZ,GAAD,CAAJ,GAAY,GAAGd,MAAH,CAAU0B,IAAI,CAACZ,GAAD,CAAJ,IAAa,EAAvB,EAA2Ba,GAAG,CAACb,GAAD,CAA9B,CAAZ;EACD;AACF"}