{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/restructuredtext/restructuredtext.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/restructuredtext/restructuredtext.ts\nvar conf = {\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\", notIn: [\"string\"] }\n  ],\n  surroundingPairs: [\n    { open: \"(\", close: \")\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*<!--\\\\s*#?region\\\\b.*-->\"),\n      end: new RegExp(\"^\\\\s*<!--\\\\s*#?endregion\\\\b.*-->\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".rst\",\n  control: /[\\\\`*_\\[\\]{}()#+\\-\\.!]/,\n  escapes: /\\\\(?:@control)/,\n  empty: [\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"link\",\n    \"meta\",\n    \"param\"\n  ],\n  alphanumerics: /[A-Za-z0-9]/,\n  simpleRefNameWithoutBq: /(?:@alphanumerics[-_+:.]*@alphanumerics)+|(?:@alphanumerics+)/,\n  simpleRefName: /(?:`@phrase`|@simpleRefNameWithoutBq)/,\n  phrase: /@simpleRefNameWithoutBq(?:\\s@simpleRefNameWithoutBq)*/,\n  citationName: /[A-Za-z][A-Za-z0-9-_.]*/,\n  blockLiteralStart: /(?:[!\"#$%&'()*+,-./:;<=>?@\\[\\]^_`{|}~]|[\\s])/,\n  precedingChars: /(?:[ -:/'\"<([{])/,\n  followingChars: /(?:[ -.,:;!?/'\")\\]}>]|$)/,\n  punctuation: /(=|-|~|`|#|\"|\\^|\\+|\\*|:|\\.|'|_|\\+)/,\n  tokenizer: {\n    root: [\n      [/^(@punctuation{3,}$){1,1}?/, \"keyword\"],\n      [/^\\s*([\\*\\-+‣•]|[a-zA-Z0-9]+\\.|\\([a-zA-Z0-9]+\\)|[a-zA-Z0-9]+\\))\\s/, \"keyword\"],\n      [/([ ]::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      [/(::)\\s*$/, \"keyword\", \"@blankLineOfLiteralBlocks\"],\n      { include: \"@tables\" },\n      { include: \"@explicitMarkupBlocks\" },\n      { include: \"@inlineMarkup\" }\n    ],\n    explicitMarkupBlocks: [\n      { include: \"@citations\" },\n      { include: \"@footnotes\" },\n      [\n        /^(\\.\\.\\s)(@simpleRefName)(::\\s)(.*)$/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"keyword\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.)(\\s+)(_)(@simpleRefName)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"hyperlinks\" }, \"\", \"\", \"string.link\", \"\", \"\", \"string.link\"]\n      ],\n      [\n        /^((?:(?:\\.\\.)(?:\\s+))?)(__)(:)(\\s+)(.*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"\", \"\", \"string.link\"]\n      ],\n      [/^(__\\s+)(.+)/, [\"\", \"string.link\"]],\n      [\n        /^(\\.\\.)( \\|)([^| ]+[^|]*[^| ]*)(\\| )(@simpleRefName)(:: .*)/,\n        [{ token: \"\", next: \"subsequentLines\" }, \"\", \"string.link\", \"\", \"keyword\", \"\"],\n        \"@rawBlocks\"\n      ],\n      [/(\\|)([^| ]+[^|]*[^| ]*)(\\|_{0,2})/, [\"\", \"string.link\", \"\"]],\n      [/^(\\.\\.)([ ].*)$/, [{ token: \"\", next: \"@comments\" }, \"comment\"]]\n    ],\n    inlineMarkup: [\n      { include: \"@citationsReference\" },\n      { include: \"@footnotesReference\" },\n      [/(@simpleRefName)(_{1,2})/, [\"string.link\", \"\"]],\n      [/(`)([^<`]+\\s+)(<)(.*)(>)(`)(_)/, [\"\", \"string.link\", \"\", \"string.link\", \"\", \"\", \"\"]],\n      [/\\*\\*([^\\\\*]|\\*(?!\\*))+\\*\\*/, \"strong\"],\n      [/\\*[^*]+\\*/, \"emphasis\"],\n      [/(``)((?:[^`]|\\`(?!`))+)(``)/, [\"\", \"keyword\", \"\"]],\n      [/(__\\s+)(.+)/, [\"\", \"keyword\"]],\n      [/(:)((?:@simpleRefNameWithoutBq)?)(:`)([^`]+)(`)/, [\"\", \"keyword\", \"\", \"\", \"\"]],\n      [/(`)([^`]+)(`:)((?:@simpleRefNameWithoutBq)?)(:)/, [\"\", \"\", \"\", \"keyword\", \"\"]],\n      [/(`)([^`]+)(`)/, \"\"],\n      [/(_`)(@phrase)(`)/, [\"\", \"string.link\", \"\"]]\n    ],\n    citations: [\n      [\n        /^(\\.\\.\\s+\\[)((?:@citationName))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    citationsReference: [[/(\\[)(@citationName)(\\]_)/, [\"\", \"string.link\", \"\"]]],\n    footnotes: [\n      [\n        /^(\\.\\.\\s+\\[)((?:[0-9]+))(\\]\\s+.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:#@simpleRefName?))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ],\n      [\n        /^(\\.\\.\\s+\\[)((?:\\*))(\\]\\s+)(.*)/,\n        [{ token: \"\", next: \"@subsequentLines\" }, \"string.link\", \"\", \"\"]\n      ]\n    ],\n    footnotesReference: [\n      [/(\\[)([0-9]+)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(#@simpleRefName?)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]],\n      [/(\\[)(\\*)(\\])(_)/, [\"\", \"string.link\", \"\", \"\"]]\n    ],\n    blankLineOfLiteralBlocks: [\n      [/^$/, \"\", \"@subsequentLinesOfLiteralBlocks\"],\n      [/^.*$/, \"\", \"@pop\"]\n    ],\n    subsequentLinesOfLiteralBlocks: [\n      [/(@blockLiteralStart+)(.*)/, [\"keyword\", \"\"]],\n      [/^(?!blockLiteralStart)/, \"\", \"@popall\"]\n    ],\n    subsequentLines: [\n      [/^[\\s]+.*/, \"\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    hyperlinks: [\n      [/^[\\s]+.*/, \"string.link\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    comments: [\n      [/^[\\s]+.*/, \"comment\"],\n      [/^(?!\\s)/, \"\", \"@pop\"]\n    ],\n    tables: [\n      [/\\+-[+-]+/, \"keyword\"],\n      [/\\+=[+=]+/, \"keyword\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,EAC7C;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,+BAA+B;AAAA,MACjD,KAAK,IAAI,OAAO,kCAAkC;AAAA,IACpD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,SAAS;AAAA,EACT,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe;AAAA,EACf,wBAAwB;AAAA,EACxB,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,8BAA8B,SAAS;AAAA,MACxC,CAAC,oEAAoE,SAAS;AAAA,MAC9E,CAAC,eAAe,WAAW,2BAA2B;AAAA,MACtD,CAAC,YAAY,WAAW,2BAA2B;AAAA,MACnD,EAAE,SAAS,UAAU;AAAA,MACrB,EAAE,SAAS,wBAAwB;AAAA,MACnC,EAAE,SAAS,gBAAgB;AAAA,IAC7B;AAAA,IACA,sBAAsB;AAAA,MACpB,EAAE,SAAS,aAAa;AAAA,MACxB,EAAE,SAAS,aAAa;AAAA,MACxB;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,WAAW,IAAI,EAAE;AAAA,MAC5D;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,aAAa,GAAG,IAAI,IAAI,eAAe,IAAI,IAAI,aAAa;AAAA,MAClF;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,IAAI,IAAI,aAAa;AAAA,MACpE;AAAA,MACA,CAAC,gBAAgB,CAAC,IAAI,aAAa,CAAC;AAAA,MACpC;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,kBAAkB,GAAG,IAAI,eAAe,IAAI,WAAW,EAAE;AAAA,QAC7E;AAAA,MACF;AAAA,MACA,CAAC,qCAAqC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MAC7D,CAAC,mBAAmB,CAAC,EAAE,OAAO,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC;AAAA,IACnE;AAAA,IACA,cAAc;AAAA,MACZ,EAAE,SAAS,sBAAsB;AAAA,MACjC,EAAE,SAAS,sBAAsB;AAAA,MACjC,CAAC,4BAA4B,CAAC,eAAe,EAAE,CAAC;AAAA,MAChD,CAAC,kCAAkC,CAAC,IAAI,eAAe,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;AAAA,MACrF,CAAC,8BAA8B,QAAQ;AAAA,MACvC,CAAC,aAAa,UAAU;AAAA,MACxB,CAAC,+BAA+B,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MACnD,CAAC,eAAe,CAAC,IAAI,SAAS,CAAC;AAAA,MAC/B,CAAC,mDAAmD,CAAC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;AAAA,MAC/E,CAAC,mDAAmD,CAAC,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/E,CAAC,iBAAiB,EAAE;AAAA,MACpB,CAAC,oBAAoB,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IAC9C;AAAA,IACA,WAAW;AAAA,MACT;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IACA,oBAAoB,CAAC,CAAC,4BAA4B,CAAC,IAAI,eAAe,EAAE,CAAC,CAAC;AAAA,IAC1E,WAAW;AAAA,MACT;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,EAAE;AAAA,MAC7D;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACjE;AAAA,MACA;AAAA,QACE;AAAA,QACA,CAAC,EAAE,OAAO,IAAI,MAAM,mBAAmB,GAAG,eAAe,IAAI,EAAE;AAAA,MACjE;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,uBAAuB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,MACnD,CAAC,iCAAiC,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,MAC7D,CAAC,mBAAmB,CAAC,IAAI,eAAe,IAAI,EAAE,CAAC;AAAA,IACjD;AAAA,IACA,0BAA0B;AAAA,MACxB,CAAC,MAAM,IAAI,iCAAiC;AAAA,MAC5C,CAAC,QAAQ,IAAI,MAAM;AAAA,IACrB;AAAA,IACA,gCAAgC;AAAA,MAC9B,CAAC,6BAA6B,CAAC,WAAW,EAAE,CAAC;AAAA,MAC7C,CAAC,0BAA0B,IAAI,SAAS;AAAA,IAC1C;AAAA,IACA,iBAAiB;AAAA,MACf,CAAC,YAAY,EAAE;AAAA,MACf,CAAC,WAAW,IAAI,MAAM;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,YAAY,aAAa;AAAA,MAC1B,CAAC,WAAW,IAAI,MAAM;AAAA,IACxB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,WAAW,IAAI,MAAM;AAAA,IACxB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,YAAY,SAAS;AAAA,IACxB;AAAA,EACF;AACF;", "names": []}