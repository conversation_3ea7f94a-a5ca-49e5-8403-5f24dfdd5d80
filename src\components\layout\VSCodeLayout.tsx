import React, { useState, useCallback } from 'react';
import { PanelGroup, Panel, PanelResizeHandle } from 'react-resizable-panels';
import ActivityBar from './ActivityBar';
import EnhancedSidebar from './EnhancedSidebar';
import StatusBar from './StatusBar';
import EnhancedPanel from './EnhancedPanel';
import CodeArea from '../CodeArea';
import Titlebar from '../Titlebar';
import { SourceProvider } from '../../context/SourceContext';
import { AIProvider } from '../../context/AIContext';

const VSCodeLayout: React.FC = () => {
  const [activeView, setActiveView] = useState('explorer');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [panelOpen, setPanelOpen] = useState(true);
  const [panelHeight, setPanelHeight] = useState(250);
  const [currentFile, setCurrentFile] = useState<string>('');
  const [selectedCode, setSelectedCode] = useState<string>('');

  const handleViewChange = useCallback((view: string) => {
    if (view === activeView && sidebarOpen) {
      setSidebarOpen(false);
    } else {
      setActiveView(view);
      setSidebarOpen(true);
    }
  }, [activeView, sidebarOpen]);

  const handleCodeInsert = useCallback((code: string, language: string) => {
    // This would integrate with the Monaco editor to insert code
    console.log('Inserting code:', { code, language });
    // TODO: Implement actual code insertion logic
  }, []);

  const togglePanel = useCallback(() => {
    setPanelOpen(!panelOpen);
  }, [panelOpen]);

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'b':
            e.preventDefault();
            setSidebarOpen(!sidebarOpen);
            break;
          case '`':
            e.preventDefault();
            setPanelOpen(!panelOpen);
            break;
          case 'j':
            e.preventDefault();
            togglePanel();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen, panelOpen, togglePanel]);

  return (
    <AIProvider>
      <div className="vscode-layout h-screen flex flex-col bg-background">
        <Titlebar />

        <div className="flex-1 flex overflow-hidden">
          <ActivityBar
            activeView={activeView}
            onViewChange={handleViewChange}
          />

          <div className="flex-1 flex flex-col">
            <PanelGroup direction="horizontal">
              {sidebarOpen && (
                <>
                  <Panel
                    defaultSize={25}
                    minSize={15}
                    maxSize={40}
                    className="min-w-[200px]"
                  >
                    <EnhancedSidebar
                      activeView={activeView}
                      onCodeInsert={handleCodeInsert}
                      currentFile={currentFile}
                      selectedCode={selectedCode}
                    />
                  </Panel>
                  <PanelResizeHandle className="w-1 bg-surface-tertiary hover:bg-neon-purple/50 transition-colors" />
                </>
              )}

              <Panel defaultSize={sidebarOpen ? 75 : 100}>
                <PanelGroup direction="vertical">
                  <Panel
                    defaultSize={panelOpen ? 70 : 100}
                    minSize={30}
                  >
                    <SourceProvider>
                      <CodeArea />
                    </SourceProvider>
                  </Panel>

                  {panelOpen && (
                    <>
                      <PanelResizeHandle className="h-1 bg-surface-tertiary hover:bg-neon-purple/50 transition-colors" />
                      <Panel
                        defaultSize={30}
                        minSize={15}
                        maxSize={60}
                      >
                        <EnhancedPanel
                          isOpen={panelOpen}
                          onToggle={togglePanel}
                          height={panelHeight}
                        />
                      </Panel>
                    </>
                  )}
                </PanelGroup>
              </Panel>
            </PanelGroup>
          </div>
        </div>

        <StatusBar
          currentFile={currentFile}
          language="TypeScript"
          line={1}
          column={1}
          errors={1}
          warnings={1}
        />
      </div>
    </AIProvider>
  );
};

export default VSCodeLayout;
