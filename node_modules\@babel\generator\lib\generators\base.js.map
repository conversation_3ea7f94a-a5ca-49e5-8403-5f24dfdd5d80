{"version": 3, "names": ["File", "node", "program", "print", "interpreter", "Program", "printInnerComments", "printSequence", "directives", "length", "newline", "body", "BlockStatement", "token", "hasDirectives", "indent", "removeTrailingNewline", "source", "loc", "endsWith", "rightBrace", "Directive", "value", "semicolon", "unescapedSingleQuoteRE", "unescapedDoubleQuoteRE", "DirectiveLiteral", "raw", "getPossibleRaw", "format", "minified", "undefined", "test", "Error", "InterpreterDirective", "Placeholder", "name", "expectedNode"], "sources": ["../../src/generators/base.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function File(this: Printer, node: t.File) {\n  if (node.program) {\n    // Print this here to ensure that Program node 'leadingComments' still\n    // get printed after the hashbang.\n    this.print(node.program.interpreter, node);\n  }\n\n  this.print(node.program, node);\n}\n\nexport function Program(this: Printer, node: t.Program) {\n  this.printInnerComments(node, false);\n\n  this.printSequence(node.directives, node);\n  if (node.directives && node.directives.length) this.newline();\n\n  this.printSequence(node.body, node);\n}\n\nexport function BlockStatement(this: Printer, node: t.BlockStatement) {\n  this.token(\"{\");\n  this.printInnerComments(node);\n\n  const hasDirectives = node.directives?.length;\n\n  if (node.body.length || hasDirectives) {\n    this.newline();\n\n    this.printSequence(node.directives, node, { indent: true });\n    if (hasDirectives) this.newline();\n\n    this.printSequence(node.body, node, { indent: true });\n    this.removeTrailingNewline();\n\n    this.source(\"end\", node.loc);\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.rightBrace();\n  } else {\n    this.source(\"end\", node.loc);\n    this.token(\"}\");\n  }\n}\n\nexport function Directive(this: Printer, node: t.Directive) {\n  this.print(node.value, node);\n  this.semicolon();\n}\n\n// These regexes match an even number of \\ followed by a quote\nconst unescapedSingleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*'/;\nconst unescapedDoubleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*\"/;\n\nexport function DirectiveLiteral(this: Printer, node: t.DirectiveLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const { value } = node;\n\n  // NOTE: In directives we can't change escapings,\n  // because they change the behavior.\n  // e.g. \"us\\x65 string\" (\\x65 is e) is not a \"use strict\" directive.\n\n  if (!unescapedDoubleQuoteRE.test(value)) {\n    this.token(`\"${value}\"`);\n  } else if (!unescapedSingleQuoteRE.test(value)) {\n    this.token(`'${value}'`);\n  } else {\n    throw new Error(\n      \"Malformed AST: it is not possible to print a directive containing\" +\n        \" both unescaped single and double quotes.\",\n    );\n  }\n}\n\nexport function InterpreterDirective(\n  this: Printer,\n  node: t.InterpreterDirective,\n) {\n  this.token(`#!${node.value}\\n`, true);\n}\n\nexport function Placeholder(this: Printer, node: t.Placeholder) {\n  this.token(\"%%\");\n  this.print(node.name);\n  this.token(\"%%\");\n\n  if (node.expectedNode === \"Statement\") {\n    this.semicolon();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAIO,SAASA,IAAT,CAA6BC,IAA7B,EAA2C;EAChD,IAAIA,IAAI,CAACC,OAAT,EAAkB;IAGhB,KAAKC,KAAL,CAAWF,IAAI,CAACC,OAAL,CAAaE,WAAxB,EAAqCH,IAArC;EACD;;EAED,KAAKE,KAAL,CAAWF,IAAI,CAACC,OAAhB,EAAyBD,IAAzB;AACD;;AAEM,SAASI,OAAT,CAAgCJ,IAAhC,EAAiD;EACtD,KAAKK,kBAAL,CAAwBL,IAAxB,EAA8B,KAA9B;EAEA,KAAKM,aAAL,CAAmBN,IAAI,CAACO,UAAxB,EAAoCP,IAApC;EACA,IAAIA,IAAI,CAACO,UAAL,IAAmBP,IAAI,CAACO,UAAL,CAAgBC,MAAvC,EAA+C,KAAKC,OAAL;EAE/C,KAAKH,aAAL,CAAmBN,IAAI,CAACU,IAAxB,EAA8BV,IAA9B;AACD;;AAEM,SAASW,cAAT,CAAuCX,IAAvC,EAA+D;EAAA;;EACpE,KAAKY,SAAL;EACA,KAAKP,kBAAL,CAAwBL,IAAxB;EAEA,MAAMa,aAAa,uBAAGb,IAAI,CAACO,UAAR,qBAAG,iBAAiBC,MAAvC;;EAEA,IAAIR,IAAI,CAACU,IAAL,CAAUF,MAAV,IAAoBK,aAAxB,EAAuC;IACrC,KAAKJ,OAAL;IAEA,KAAKH,aAAL,CAAmBN,IAAI,CAACO,UAAxB,EAAoCP,IAApC,EAA0C;MAAEc,MAAM,EAAE;IAAV,CAA1C;IACA,IAAID,aAAJ,EAAmB,KAAKJ,OAAL;IAEnB,KAAKH,aAAL,CAAmBN,IAAI,CAACU,IAAxB,EAA8BV,IAA9B,EAAoC;MAAEc,MAAM,EAAE;IAAV,CAApC;IACA,KAAKC,qBAAL;IAEA,KAAKC,MAAL,CAAY,KAAZ,EAAmBhB,IAAI,CAACiB,GAAxB;IAEA,IAAI,CAAC,KAAKC,QAAL,IAAL,EAAwC,KAAKT,OAAL;IAExC,KAAKU,UAAL;EACD,CAdD,MAcO;IACL,KAAKH,MAAL,CAAY,KAAZ,EAAmBhB,IAAI,CAACiB,GAAxB;IACA,KAAKL,SAAL;EACD;AACF;;AAEM,SAASQ,SAAT,CAAkCpB,IAAlC,EAAqD;EAC1D,KAAKE,KAAL,CAAWF,IAAI,CAACqB,KAAhB,EAAuBrB,IAAvB;EACA,KAAKsB,SAAL;AACD;;AAGD,MAAMC,sBAAsB,GAAG,uBAA/B;AACA,MAAMC,sBAAsB,GAAG,uBAA/B;;AAEO,SAASC,gBAAT,CAAyCzB,IAAzC,EAAmE;EACxE,MAAM0B,GAAG,GAAG,KAAKC,cAAL,CAAoB3B,IAApB,CAAZ;;EACA,IAAI,CAAC,KAAK4B,MAAL,CAAYC,QAAb,IAAyBH,GAAG,KAAKI,SAArC,EAAgD;IAC9C,KAAKlB,KAAL,CAAWc,GAAX;IACA;EACD;;EAED,MAAM;IAAEL;EAAF,IAAYrB,IAAlB;;EAMA,IAAI,CAACwB,sBAAsB,CAACO,IAAvB,CAA4BV,KAA5B,CAAL,EAAyC;IACvC,KAAKT,KAAL,CAAY,IAAGS,KAAM,GAArB;EACD,CAFD,MAEO,IAAI,CAACE,sBAAsB,CAACQ,IAAvB,CAA4BV,KAA5B,CAAL,EAAyC;IAC9C,KAAKT,KAAL,CAAY,IAAGS,KAAM,GAArB;EACD,CAFM,MAEA;IACL,MAAM,IAAIW,KAAJ,CACJ,sEACE,2CAFE,CAAN;EAID;AACF;;AAEM,SAASC,oBAAT,CAELjC,IAFK,EAGL;EACA,KAAKY,KAAL,CAAY,KAAIZ,IAAI,CAACqB,KAAM,IAA3B,EAAgC,IAAhC;AACD;;AAEM,SAASa,WAAT,CAAoClC,IAApC,EAAyD;EAC9D,KAAKY,KAAL,CAAW,IAAX;EACA,KAAKV,KAAL,CAAWF,IAAI,CAACmC,IAAhB;EACA,KAAKvB,KAAL,CAAW,IAAX;;EAEA,IAAIZ,IAAI,CAACoC,YAAL,KAAsB,WAA1B,EAAuC;IACrC,KAAKd,SAAL;EACD;AACF"}