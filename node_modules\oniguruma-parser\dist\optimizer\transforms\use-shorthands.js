"use strict";import{createCharacterSet as p}from"../../parser/parse.js";import{cpOf as l}from"../../utils.js";const S={CharacterSet({node:r,parent:o,root:e,replaceWith:s}){const{kind:a,negate:u,value:t}=r;let c=null;a==="property"&&(t==="Decimal_Number"||t==="Nd")&&!e.flags.digitIsAscii&&!e.flags.posixIsAscii||a==="posix"&&t==="digit"?c=p("digit",{negate:u}):a==="property"&&(t==="ASCII_Hex_Digit"||t==="AHex")||a==="posix"&&t==="xdigit"?c=p("hex",{negate:u}):a==="property"&&(t==="White_Space"||t==="WSpace")&&!e.flags.spaceIsAscii&&!e.flags.posixIsAscii||a==="posix"&&t==="space"?c=p("space",{negate:u}):o.type!=="CharacterClass"&&a==="property"&&!u&&t==="Any"&&(c=p("any")),c&&s(c)},CharacterClass({node:r,root:o}){if(r.kind!=="union")return;const e={rangeDigit0To9:!1,rangeAToFLower:!1,rangeAToFUpper:!1,unicodeL:!1,unicodeM:!1,unicodeN:!1,unicodePc:!1};for(const s of r.body)s.type==="CharacterClassRange"?(e.rangeDigit0To9||=n(s,i.n0,i.n9),e.rangeAToFLower||=n(s,i.a,i.f),e.rangeAToFUpper||=n(s,i.A,i.F)):s.type==="CharacterSet"&&(e.unicodeL||=f(s,"L"),e.unicodeM||=f(s,"M"),e.unicodeN||=f(s,"N"),e.unicodePc||=f(s,"Pc",{includeSupercategories:!0}));e.rangeDigit0To9&&e.rangeAToFUpper&&e.rangeAToFLower&&(r.body=r.body.filter(s=>!(n(s,i.n0,i.n9)||n(s,i.a,i.f)||n(s,i.A,i.F))),r.body.push(p("hex"))),e.unicodeL&&e.unicodeM&&e.unicodeN&&e.unicodePc&&!o.flags.wordIsAscii&&!o.flags.posixIsAscii&&(r.body=r.body.filter(s=>!f(s,["L","M","N","Pc"],{includeSubcategories:!0})),r.body.push(p("word")))}},i={n0:l("0"),n9:l("9"),A:l("A"),F:l("F"),a:l("a"),f:l("f")};function n(r,o,e){return r.type==="CharacterClassRange"&&r.min.value===o&&r.max.value===e}function f(r,o,e={}){if(r.type!=="CharacterSet"||r.kind!=="property"||r.negate)return!1;const s=Array.isArray(o)?o:[o],a=[];for(const u of s){a.push(u);const t=g[u]?.full,c=b[u],y=g[u]?.sub;t&&a.push(t),e.includeSupercategories&&c&&(a.push(c),a.push(g[c].full)),e.includeSubcategories&&y&&a.push(...y)}return a.includes(r.value)}const d=["Ll","Lm","Lo","Lt","Lu"],h=["Mc","Me","Mn"],m=["Nd","Nl","No"],N=["Pc","Pd","Pe","Pf","Pi","Po","Ps"],g={L:{full:"Letter",sub:d},M:{full:"Mark",sub:h},N:{full:"Number",sub:m},P:{full:"Punctuation",sub:N}},b={};for(const r of Object.keys(g))for(const o of g[r].sub)b[o]=r;export{n as isRange,S as useShorthands};
//# sourceMappingURL=use-shorthands.js.map
