"use strict";const o={Flags({node:e}){e.extended=!1,e.textSegmentMode==="grapheme"&&(e.textSegmentMode=null)},Directive({node:e,remove:t}){e.kind==="flags"&&(i(e),r(e)&&t())},Group({node:e}){e.flags&&(i(e),r(e))}};function r(e){const{flags:t}=e;return t&&!t.enable&&!t.disable?(delete e.flags,!0):!1}function i({flags:e}){if(!e)throw new Error("Expected flags");e.enable&&delete e.enable.extended,e.disable&&delete e.disable.extended,l(e)}function l(e){e.enable&&!Object.keys(e.enable).length&&delete e.enable,e.disable&&!Object.keys(e.disable).length&&delete e.disable}export{o as removeUselessFlags};
//# sourceMappingURL=remove-useless-flags.js.map
