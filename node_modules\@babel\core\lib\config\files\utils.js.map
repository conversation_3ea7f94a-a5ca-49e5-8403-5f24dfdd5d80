{"version": 3, "names": ["makeStaticFileCache", "fn", "makeStrongCache", "filepath", "cache", "cached", "invalidate", "fileMtime", "fs", "readFile", "nodeFs", "existsSync", "statSync", "mtime", "e", "code"], "sources": ["../../../src/config/files/utils.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport { makeStrongCache } from \"../caching\";\nimport type { CacheConfigurator } from \"../caching\";\nimport * as fs from \"../../gensync-utils/fs\";\nimport nodeFs from \"fs\";\n\nexport function makeStaticFileCache<T>(\n  fn: (filepath: string, contents: string) => T,\n) {\n  return makeStrongCache(function* (\n    filepath: string,\n    cache: CacheConfigurator<void>,\n  ): Handler<null | T> {\n    const cached = cache.invalidate(() => fileMtime(filepath));\n\n    if (cached === null) {\n      return null;\n    }\n\n    return fn(filepath, yield* fs.readFile(filepath, \"utf8\"));\n  });\n}\n\nfunction fileMtime(filepath: string): number | null {\n  if (!nodeFs.existsSync(filepath)) return null;\n\n  try {\n    return +nodeFs.statSync(filepath).mtime;\n  } catch (e) {\n    if (e.code !== \"ENOENT\" && e.code !== \"ENOTDIR\") throw e;\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;AAEA;;AAEA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEO,SAASA,mBAAT,CACLC,EADK,EAEL;EACA,OAAO,IAAAC,wBAAA,EAAgB,WACrBC,QADqB,EAErBC,KAFqB,EAGF;IACnB,MAAMC,MAAM,GAAGD,KAAK,CAACE,UAAN,CAAiB,MAAMC,SAAS,CAACJ,QAAD,CAAhC,CAAf;;IAEA,IAAIE,MAAM,KAAK,IAAf,EAAqB;MACnB,OAAO,IAAP;IACD;;IAED,OAAOJ,EAAE,CAACE,QAAD,EAAW,OAAOK,EAAE,CAACC,QAAH,CAAYN,QAAZ,EAAsB,MAAtB,CAAlB,CAAT;EACD,CAXM,CAAP;AAYD;;AAED,SAASI,SAAT,CAAmBJ,QAAnB,EAAoD;EAClD,IAAI,CAACO,MAAA,CAAOC,UAAP,CAAkBR,QAAlB,CAAL,EAAkC,OAAO,IAAP;;EAElC,IAAI;IACF,OAAO,CAACO,MAAA,CAAOE,QAAP,CAAgBT,QAAhB,EAA0BU,KAAlC;EACD,CAFD,CAEE,OAAOC,CAAP,EAAU;IACV,IAAIA,CAAC,CAACC,IAAF,KAAW,QAAX,IAAuBD,CAAC,CAACC,IAAF,KAAW,SAAtC,EAAiD,MAAMD,CAAN;EAClD;;EAED,OAAO,IAAP;AACD"}