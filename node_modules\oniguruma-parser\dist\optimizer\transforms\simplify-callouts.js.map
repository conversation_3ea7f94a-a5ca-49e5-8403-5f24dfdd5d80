{"version": 3, "sources": ["../../../src/optimizer/transforms/simplify-callouts.ts"], "sourcesContent": ["import type {Visitor} from '../../traverser/traverse.js';\nimport {createLookaroundAssertion} from '../../parser/parse.js';\n\n/**\nCleanup callout arguments, removing redundant commas, leading zeros, and empty braces.\n*/\nconst simplifyCallouts: Visitor = {\n  NamedCallout({node, replaceWith}) {\n    const {arguments: args, kind} = node;\n\n    // Special case: `(*FAIL)` -> `(?!)`\n    if (kind === 'fail') {\n      replaceWith(createLookaroundAssertion({negate: true}));\n      return;\n    }\n\n    if (!args) {\n      return;\n    }\n    const newArgs: Array<string | number> = args.\n      filter(arg => arg !== '').\n      // TODO: If supporting custom callout names in the future, add `kind !== 'custom'` to this\n      // condition, since custom named callouts might treat e.g. `+05` as a string\n      map(arg => (typeof arg === 'string' && /^[+-]?\\d+$/.test(arg)) ? +arg : arg);\n    node.arguments = newArgs.length ? newArgs : null;\n  },\n};\n\nexport {\n  simplifyCallouts,\n};\n"], "mappings": "aACA,OAAQ,6BAAAA,MAAgC,wBAKxC,MAAMC,EAA4B,CAChC,aAAa,CAAC,KAAAC,EAAM,YAAAC,CAAW,EAAG,CAChC,KAAM,CAAC,UAAWC,EAAM,KAAAC,CAAI,EAAIH,EAGhC,GAAIG,IAAS,OAAQ,CACnBF,EAAYH,EAA0B,CAAC,OAAQ,EAAI,CAAC,CAAC,EACrD,MACF,CAEA,GAAI,CAACI,EACH,OAEF,MAAME,EAAkCF,EACtC,OAAOG,GAAOA,IAAQ,EAAE,EAGxB,IAAIA,GAAQ,OAAOA,GAAQ,UAAY,aAAa,KAAKA,CAAG,EAAK,CAACA,EAAMA,CAAG,EAC7EL,EAAK,UAAYI,EAAQ,OAASA,EAAU,IAC9C,CACF,EAEA,OACEL,KAAA", "names": ["createLookaroundAssertion", "simplifyCallouts", "node", "replaceWith", "args", "kind", "newArgs", "arg"]}