{"version": 3, "names": ["import_", "require", "importMetaResolveP", "process", "execArgv", "includes", "then", "m", "default", "polyfill", "Promise", "resolve", "specifier", "parent"], "sources": ["../../../src/config/files/import-meta-resolve.ts"], "sourcesContent": ["import { createRequire } from \"module\";\nimport { resolve as polyfill } from \"../../vendor/import-meta-resolve\";\n\nconst require = createRequire(import.meta.url);\n\nlet import_;\ntry {\n  // Node < 13.3 doesn't support import() syntax.\n  import_ = require(\"./import.cjs\");\n} catch {}\n\n// import.meta.resolve is only available in ESM, but this file is compiled to CJS.\n// We can extract it using dynamic import.\nconst importMetaResolveP: Promise<ImportMeta[\"resolve\"]> =\n  import_ &&\n  // Due to a Node.js/V8 bug (https://github.com/nodejs/node/issues/35889), we cannot\n  // use always dynamic import because it segfaults when running in a Node.js `vm` context,\n  // which is used by the default Jest environment and by webpack-cli.\n  //\n  // However, import.meta.resolve is experimental and only enabled when Node.js is run\n  // with the `--experimental-import-meta-resolve` flag: we can avoid calling import()\n  // when that flag is not enabled, so that the default behavior never segfaults.\n  //\n  // Hopefully, before Node.js unflags import.meta.resolve, either:\n  // - we will move to ESM, so that we have direct access to import.meta.resolve, or\n  // - the V8 bug will be fixed so that we can safely use dynamic import by default.\n  //\n  // I (@nicolo-ribaudo) am really anoyed by this bug, because there is no known\n  // work-around other than \"don't use dynamic import if you are running in a `vm` context\",\n  // but there is no reliable way to detect it (you cannot try/catch segfaults).\n  //\n  // This is the only place where we *need* to use dynamic import because we need to access\n  // an ES module. All the other places will first try using require() and *then*, if\n  // it throws because it's a module, will fallback to import().\n  process.execArgv.includes(\"--experimental-import-meta-resolve\")\n    ? import_(\"data:text/javascript,export default import.meta.resolve\").then(\n        (m: { default: ImportMeta[\"resolve\"] | undefined }) =>\n          m.default || polyfill,\n        () => polyfill,\n      )\n    : Promise.resolve(polyfill);\n\nexport default async function resolve(\n  specifier: Parameters<ImportMeta[\"resolve\"]>[0],\n  parent?: Parameters<ImportMeta[\"resolve\"]>[1],\n): ReturnType<ImportMeta[\"resolve\"]> {\n  return (await importMetaResolveP)(specifier, parent);\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;;;;;AAIA,IAAIA,OAAJ;;AACA,IAAI;EAEFA,OAAO,GAAGC,OAAO,CAAC,cAAD,CAAjB;AACD,CAHD,CAGE,gBAAM,CAAE;;AAIV,MAAMC,kBAAkD,GACtDF,OAAO,IAoBPG,OAAO,CAACC,QAAR,CAAiBC,QAAjB,CAA0B,oCAA1B,CApBA,GAqBIL,OAAO,CAAC,yDAAD,CAAP,CAAmEM,IAAnE,CACGC,CAAD,IACEA,CAAC,CAACC,OAAF,IAAaC,0BAFjB,EAGE,MAAMA,0BAHR,CArBJ,GA0BIC,OAAO,CAACC,OAAR,CAAgBF,0BAAhB,CA3BN;;SA6B8BE,O;;;;;+BAAf,WACbC,SADa,EAEbC,MAFa,EAGsB;IACnC,OAAO,OAAOX,kBAAP,EAA2BU,SAA3B,EAAsCC,MAAtC,CAAP;EACD,C"}