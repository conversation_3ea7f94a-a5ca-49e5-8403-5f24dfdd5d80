"use strict";import{isAlternativeContainer as y}from"../../parser/node-utils.js";import{createAlternative as f,createGroup as c}from"../../parser/parse.js";const l={"*"({node:e}){if(!y(e)||e.body.length<2)return;const t=[];let i=!1,o=0;for(;!i;){t.push(e.body[0].body[o]);for(const r of e.body){const s=r.body[o];if(!s||!d(s)||!p(s,t[o])){i=!0;break}}o++}if(t.pop(),!t.length)return;for(const r of e.body)r.body=r.body.slice(t.length);const a=f({body:t}),n=c({body:e.body});n.body.every(r=>!r.body.length)||a.body.push(n),e.body=[a]}};function d(e){return e.type==="Assertion"||e.type==="Character"||e.type==="CharacterSet"}function p(e,t){if(e.type!==t.type)return!1;if(e.type==="Assertion"||e.type==="CharacterSet")return e.kind===t.kind&&e.negate===t.negate;if(e.type==="Character")return e.value===t.value;throw new Error(`Unexpected node type "${e.type}"`)}export{l as extractPrefix,d as isAllowedSimpleNode,p as isNodeEqual};
//# sourceMappingURL=extract-prefix.js.map
