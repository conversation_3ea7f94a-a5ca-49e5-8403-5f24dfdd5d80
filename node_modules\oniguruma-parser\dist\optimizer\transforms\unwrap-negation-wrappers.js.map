{"version": 3, "sources": ["../../../src/optimizer/transforms/unwrap-negation-wrappers.ts"], "sourcesContent": ["import type {Visitor} from '../../traverser/traverse.js';\nimport {createCharacterSet} from '../../parser/parse.js';\n\n/**\nUnwrap negated classes used to negate an individual character set.\nAllows independently controlling this behavior, and avoids logic duplication in\n`unwrapUselessClasses` and `unnestUselessClasses`.\n*/\nconst unwrapNegationWrappers: Visitor = {\n  CharacterClass({node, parent, replaceWith}) {\n    const {body, kind, negate} = node;\n    if (!negate || kind !== 'union' || body.length !== 1) {\n      return;\n    }\n    const kid = body[0];\n    if (kid.type === 'CharacterSet') {\n      kid.negate = !kid.negate;\n      // Might unnest into a class or unwrap into a non-class. All character set kinds valid in a\n      // class are also valid outside of a class, though the inverse isn't true\n      replaceWith(kid);\n    } else if (\n      parent.type !== 'CharacterClass' &&\n      kid.type === 'Character' &&\n      kid.value === 10 // '\\n'\n    ) {\n      if (parent.type === 'Quantifier' && parent.kind !== 'lazy') {\n        // Avoid introducing a trigger for a `vscode-oniguruma` bug (v2.0.1 tested); see\n        // <github.com/kkos/oniguruma/issues/347>\n        return;\n      }\n      // `[^\\n]` -> `\\N`; can only use `\\N` if not in a class\n      replaceWith(createCharacterSet('newline', {negate: true}));\n    }\n  },\n};\n\nexport {\n  unwrapNegationWrappers,\n};\n"], "mappings": "aACA,OAAQ,sBAAAA,MAAyB,wBAOjC,MAAMC,EAAkC,CACtC,eAAe,CAAC,KAAAC,EAAM,OAAAC,EAAQ,YAAAC,CAAW,EAAG,CAC1C,KAAM,CAAC,KAAAC,EAAM,KAAAC,EAAM,OAAAC,CAAM,EAAIL,EAC7B,GAAI,CAACK,GAAUD,IAAS,SAAWD,EAAK,SAAW,EACjD,OAEF,MAAMG,EAAMH,EAAK,CAAC,EAClB,GAAIG,EAAI,OAAS,eACfA,EAAI,OAAS,CAACA,EAAI,OAGlBJ,EAAYI,CAAG,UAEfL,EAAO,OAAS,kBAChBK,EAAI,OAAS,aACbA,EAAI,QAAU,GACd,CACA,GAAIL,EAAO,OAAS,cAAgBA,EAAO,OAAS,OAGlD,OAGFC,EAAYJ,EAAmB,UAAW,CAAC,OAAQ,EAAI,CAAC,CAAC,CAC3D,CACF,CACF,EAEA,OACEC,KAAA", "names": ["createCharacterSet", "unwrapNegationWrappers", "node", "parent", "replaceWith", "body", "kind", "negate", "kid"]}