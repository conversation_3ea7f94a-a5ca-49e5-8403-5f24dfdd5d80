module.exports={A:{A:{"4":"J D E F A B 4B"},B:{"4":"C K L G M N O P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H"},C:{"1":"AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB","8":"0 1 2 3 4 5 6 7 8 9 5B pB I q J D E F A B C K L G M N O r s t u v w x y z 6B 7B"},D:{"4":"0 1 2 3 4 5 6 7 8 9 q J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B","8":"I"},E:{"4":"q J D E F A B C K L G BC CC DC EC vB mB nB wB FC GC xB yB zB 0B oB 1B HC","8":"I AC uB"},F:{"4":"0 1 2 3 4 5 6 7 8 9 G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a","8":"F B C IC JC KC LC mB 2B MC nB"},G:{"4":"E OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B","8":"uB NC 3B"},H:{"8":"hC"},I:{"4":"pB I H lC 3B mC nC","8":"iC jC kC"},J:{"4":"A","8":"D"},K:{"4":"c","8":"A B C mB 2B nB"},L:{"4":"H"},M:{"1":"b"},N:{"4":"A B"},O:{"4":"oC"},P:{"4":"I pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"4":"wB"},R:{"4":"1C"},S:{"1":"2C"}},B:1,C:"Ruby annotation"};
