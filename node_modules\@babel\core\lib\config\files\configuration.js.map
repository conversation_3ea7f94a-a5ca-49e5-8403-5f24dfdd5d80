{"version": 3, "names": ["debug", "buildDebug", "ROOT_CONFIG_FILENAMES", "RELATIVE_CONFIG_FILENAMES", "BABELIGNORE_FILENAME", "findConfigUpwards", "rootDir", "dirname", "filename", "nodeFs", "existsSync", "path", "join", "nextDir", "findRelativeConfig", "packageData", "envName", "caller", "config", "ignore", "filepath", "loc", "directories", "loadOneConfig", "pkg", "packageToBabelConfig", "ignoreLoc", "readIgnoreConfig", "findRootConfig", "names", "previousConfig", "configs", "gens<PERSON>", "all", "map", "readConfig", "reduce", "ConfigError", "basename", "loadConfig", "name", "paths", "conf", "ext", "extname", "readConfigJS", "readConfigJSON5", "LOADING_CONFIGS", "Set", "makeStrongCache", "cache", "never", "has", "options", "add", "loadCjsOrMjsDefault", "delete", "assertCache", "endHiddenCallStack", "makeConfigAPI", "Array", "isArray", "then", "configured", "throwConfigError", "makeWeakCacheSync", "file", "babel", "makeStaticFileCache", "content", "json5", "parse", "err", "message", "ignoreDir", "ignorePatterns", "split", "line", "replace", "trim", "filter", "pattern", "pathPatternToRegex", "resolveShowConfigPath", "targetPath", "process", "env", "BABEL_SHOW_CONFIG_FOR", "absolutePath", "resolve", "stats", "fs", "stat", "isFile", "Error"], "sources": ["../../../src/config/files/configuration.ts"], "sourcesContent": ["import buildDebug from \"debug\";\nimport nodeFs from \"fs\";\nimport path from \"path\";\nimport json5 from \"json5\";\nimport gensync from \"gensync\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { makeStrongCache, makeWeakCacheSync } from \"../caching\";\nimport type { CacheConfigurator } from \"../caching\";\nimport { makeConfigAPI } from \"../helpers/config-api\";\nimport type { ConfigAPI } from \"../helpers/config-api\";\nimport { makeStaticFileCache } from \"./utils\";\nimport loadCjsOrMjsDefault from \"./module-types\";\nimport pathPatternToRegex from \"../pattern-to-regex\";\nimport type { FilePackageData, RelativeConfig, ConfigFile } from \"./types\";\nimport type { CallerMetadata } from \"../validation/options\";\nimport ConfigError from \"../../errors/config-error\";\n\nimport * as fs from \"../../gensync-utils/fs\";\n\nimport { createRequire } from \"module\";\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace\";\nconst require = createRequire(import.meta.url);\n\nconst debug = buildDebug(\"babel:config:loading:files:configuration\");\n\nexport const ROOT_CONFIG_FILENAMES = [\n  \"babel.config.js\",\n  \"babel.config.cjs\",\n  \"babel.config.mjs\",\n  \"babel.config.json\",\n];\nconst RELATIVE_CONFIG_FILENAMES = [\n  \".babelrc\",\n  \".babelrc.js\",\n  \".babelrc.cjs\",\n  \".babelrc.mjs\",\n  \".babelrc.json\",\n];\n\nconst BABELIGNORE_FILENAME = \".babelignore\";\n\nexport function findConfigUpwards(rootDir: string): string | null {\n  let dirname = rootDir;\n  for (;;) {\n    for (const filename of ROOT_CONFIG_FILENAMES) {\n      if (nodeFs.existsSync(path.join(dirname, filename))) {\n        return dirname;\n      }\n    }\n\n    const nextDir = path.dirname(dirname);\n    if (dirname === nextDir) break;\n    dirname = nextDir;\n  }\n\n  return null;\n}\n\nexport function* findRelativeConfig(\n  packageData: FilePackageData,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<RelativeConfig> {\n  let config = null;\n  let ignore = null;\n\n  const dirname = path.dirname(packageData.filepath);\n\n  for (const loc of packageData.directories) {\n    if (!config) {\n      config = yield* loadOneConfig(\n        RELATIVE_CONFIG_FILENAMES,\n        loc,\n        envName,\n        caller,\n        packageData.pkg?.dirname === loc\n          ? packageToBabelConfig(packageData.pkg as ConfigFile)\n          : null,\n      );\n    }\n\n    if (!ignore) {\n      const ignoreLoc = path.join(loc, BABELIGNORE_FILENAME);\n      ignore = yield* readIgnoreConfig(ignoreLoc);\n\n      if (ignore) {\n        debug(\"Found ignore %o from %o.\", ignore.filepath, dirname);\n      }\n    }\n  }\n\n  return { config, ignore };\n}\n\nexport function findRootConfig(\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  return loadOneConfig(ROOT_CONFIG_FILENAMES, dirname, envName, caller);\n}\n\nfunction* loadOneConfig(\n  names: string[],\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n  previousConfig: ConfigFile | null = null,\n): Handler<ConfigFile | null> {\n  const configs = yield* gensync.all(\n    names.map(filename =>\n      readConfig(path.join(dirname, filename), envName, caller),\n    ),\n  );\n  const config = configs.reduce((previousConfig: ConfigFile | null, config) => {\n    if (config && previousConfig) {\n      throw new ConfigError(\n        `Multiple configuration files found. Please remove one:\\n` +\n          ` - ${path.basename(previousConfig.filepath)}\\n` +\n          ` - ${config.filepath}\\n` +\n          `from ${dirname}`,\n      );\n    }\n\n    return config || previousConfig;\n  }, previousConfig);\n\n  if (config) {\n    debug(\"Found configuration %o from %o.\", config.filepath, dirname);\n  }\n  return config;\n}\n\nexport function* loadConfig(\n  name: string,\n  dirname: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile> {\n  const filepath = require.resolve(name, { paths: [dirname] });\n\n  const conf = yield* readConfig(filepath, envName, caller);\n  if (!conf) {\n    throw new ConfigError(\n      `Config file contains no configuration data`,\n      filepath,\n    );\n  }\n\n  debug(\"Loaded config %o from %o.\", name, dirname);\n  return conf;\n}\n\n/**\n * Read the given config file, returning the result. Returns null if no config was found, but will\n * throw if there are parsing errors while loading a config.\n */\nfunction readConfig(\n  filepath: string,\n  envName: string,\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  const ext = path.extname(filepath);\n  return ext === \".js\" || ext === \".cjs\" || ext === \".mjs\"\n    ? readConfigJS(filepath, { envName, caller })\n    : readConfigJSON5(filepath);\n}\n\nconst LOADING_CONFIGS = new Set();\n\nconst readConfigJS = makeStrongCache(function* readConfigJS(\n  filepath: string,\n  cache: CacheConfigurator<{\n    envName: string;\n    caller: CallerMetadata | undefined;\n  }>,\n): Handler<ConfigFile | null> {\n  if (!nodeFs.existsSync(filepath)) {\n    cache.never();\n    return null;\n  }\n\n  // The `require()` call below can make this code reentrant if a require hook like @babel/register has been\n  // loaded into the system. That would cause Babel to attempt to compile the `.babelrc.js` file as it loads\n  // below. To cover this case, we auto-ignore re-entrant config processing.\n  if (LOADING_CONFIGS.has(filepath)) {\n    cache.never();\n\n    debug(\"Auto-ignoring usage of config %o.\", filepath);\n    return {\n      filepath,\n      dirname: path.dirname(filepath),\n      options: {},\n    };\n  }\n\n  let options: unknown;\n  try {\n    LOADING_CONFIGS.add(filepath);\n    options = yield* loadCjsOrMjsDefault(\n      filepath,\n      \"You appear to be using a native ECMAScript module configuration \" +\n        \"file, which is only supported when running Babel asynchronously.\",\n    );\n  } finally {\n    LOADING_CONFIGS.delete(filepath);\n  }\n\n  let assertCache = false;\n  if (typeof options === \"function\") {\n    // @ts-expect-error - if we want to make it possible to use async configs\n    yield* [];\n\n    options = endHiddenCallStack(options as any as (api: ConfigAPI) => {})(\n      makeConfigAPI(cache),\n    );\n\n    assertCache = true;\n  }\n\n  if (!options || typeof options !== \"object\" || Array.isArray(options)) {\n    throw new ConfigError(\n      `Configuration should be an exported JavaScript object.`,\n      filepath,\n    );\n  }\n\n  // @ts-expect-error todo(flow->ts)\n  if (typeof options.then === \"function\") {\n    throw new ConfigError(\n      `You appear to be using an async configuration, ` +\n        `which your current version of Babel does not support. ` +\n        `We may add support for this in the future, ` +\n        `but if you're on the most recent version of @babel/core and still ` +\n        `seeing this error, then you'll need to synchronously return your config.`,\n      filepath,\n    );\n  }\n\n  if (assertCache && !cache.configured()) throwConfigError(filepath);\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    options,\n  };\n});\n\nconst packageToBabelConfig = makeWeakCacheSync(\n  (file: ConfigFile): ConfigFile | null => {\n    const babel: unknown = file.options[\"babel\"];\n\n    if (typeof babel === \"undefined\") return null;\n\n    if (typeof babel !== \"object\" || Array.isArray(babel) || babel === null) {\n      throw new ConfigError(`.babel property must be an object`, file.filepath);\n    }\n\n    return {\n      filepath: file.filepath,\n      dirname: file.dirname,\n      options: babel,\n    };\n  },\n);\n\nconst readConfigJSON5 = makeStaticFileCache((filepath, content): ConfigFile => {\n  let options;\n  try {\n    options = json5.parse(content);\n  } catch (err) {\n    throw new ConfigError(\n      `Error while parsing config - ${err.message}`,\n      filepath,\n    );\n  }\n\n  if (!options) throw new ConfigError(`No config detected`, filepath);\n\n  if (typeof options !== \"object\") {\n    throw new ConfigError(`Config returned typeof ${typeof options}`, filepath);\n  }\n  if (Array.isArray(options)) {\n    throw new ConfigError(`Expected config object but found array`, filepath);\n  }\n\n  delete options[\"$schema\"];\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    options,\n  };\n});\n\nconst readIgnoreConfig = makeStaticFileCache((filepath, content) => {\n  const ignoreDir = path.dirname(filepath);\n  const ignorePatterns = content\n    .split(\"\\n\")\n    .map<string>(line => line.replace(/#(.*?)$/, \"\").trim())\n    .filter(line => !!line);\n\n  for (const pattern of ignorePatterns) {\n    if (pattern[0] === \"!\") {\n      throw new ConfigError(\n        `Negation of file paths is not supported.`,\n        filepath,\n      );\n    }\n  }\n\n  return {\n    filepath,\n    dirname: path.dirname(filepath),\n    ignore: ignorePatterns.map(pattern =>\n      pathPatternToRegex(pattern, ignoreDir),\n    ),\n  };\n});\n\nexport function* resolveShowConfigPath(\n  dirname: string,\n): Handler<string | null> {\n  const targetPath = process.env.BABEL_SHOW_CONFIG_FOR;\n  if (targetPath != null) {\n    const absolutePath = path.resolve(dirname, targetPath);\n    const stats = yield* fs.stat(absolutePath);\n    if (!stats.isFile()) {\n      throw new Error(\n        `${absolutePath}: BABEL_SHOW_CONFIG_FOR must refer to a regular file, directories are not supported.`,\n      );\n    }\n    return absolutePath;\n  }\n  return null;\n}\n\nfunction throwConfigError(filepath: string): never {\n  throw new ConfigError(\n    `\\\nCaching was left unconfigured. Babel's plugins, presets, and .babelrc.js files can be configured\nfor various types of caching, using the first param of their handler functions:\n\nmodule.exports = function(api) {\n  // The API exposes the following:\n\n  // Cache the returned value forever and don't call this function again.\n  api.cache(true);\n\n  // Don't cache at all. Not recommended because it will be very slow.\n  api.cache(false);\n\n  // Cached based on the value of some function. If this function returns a value different from\n  // a previously-encountered value, the plugins will re-evaluate.\n  var env = api.cache(() => process.env.NODE_ENV);\n\n  // If testing for a specific env, we recommend specifics to avoid instantiating a plugin for\n  // any possible NODE_ENV value that might come up during plugin execution.\n  var isProd = api.cache(() => process.env.NODE_ENV === \"production\");\n\n  // .cache(fn) will perform a linear search though instances to find the matching plugin based\n  // based on previous instantiated plugins. If you want to recreate the plugin and discard the\n  // previous instance whenever something changes, you may use:\n  var isProd = api.cache.invalidate(() => process.env.NODE_ENV === \"production\");\n\n  // Note, we also expose the following more-verbose versions of the above examples:\n  api.cache.forever(); // api.cache(true)\n  api.cache.never();   // api.cache(false)\n  api.cache.using(fn); // api.cache(fn)\n\n  // Return the value that will be cached.\n  return { };\n};`,\n    filepath,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;;AAEA;;AACA;;AACA;;AAGA;;AAEA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAGA,MAAMA,KAAK,GAAGC,QAAA,CAAW,0CAAX,CAAd;;AAEO,MAAMC,qBAAqB,GAAG,CACnC,iBADmC,EAEnC,kBAFmC,EAGnC,kBAHmC,EAInC,mBAJmC,CAA9B;;AAMP,MAAMC,yBAAyB,GAAG,CAChC,UADgC,EAEhC,aAFgC,EAGhC,cAHgC,EAIhC,cAJgC,EAKhC,eALgC,CAAlC;AAQA,MAAMC,oBAAoB,GAAG,cAA7B;;AAEO,SAASC,iBAAT,CAA2BC,OAA3B,EAA2D;EAChE,IAAIC,OAAO,GAAGD,OAAd;;EACA,SAAS;IACP,KAAK,MAAME,QAAX,IAAuBN,qBAAvB,EAA8C;MAC5C,IAAIO,KAAA,CAAOC,UAAP,CAAkBC,OAAA,CAAKC,IAAL,CAAUL,OAAV,EAAmBC,QAAnB,CAAlB,CAAJ,EAAqD;QACnD,OAAOD,OAAP;MACD;IACF;;IAED,MAAMM,OAAO,GAAGF,OAAA,CAAKJ,OAAL,CAAaA,OAAb,CAAhB;;IACA,IAAIA,OAAO,KAAKM,OAAhB,EAAyB;IACzBN,OAAO,GAAGM,OAAV;EACD;;EAED,OAAO,IAAP;AACD;;AAEM,UAAUC,kBAAV,CACLC,WADK,EAELC,OAFK,EAGLC,MAHK,EAIoB;EACzB,IAAIC,MAAM,GAAG,IAAb;EACA,IAAIC,MAAM,GAAG,IAAb;;EAEA,MAAMZ,OAAO,GAAGI,OAAA,CAAKJ,OAAL,CAAaQ,WAAW,CAACK,QAAzB,CAAhB;;EAEA,KAAK,MAAMC,GAAX,IAAkBN,WAAW,CAACO,WAA9B,EAA2C;IACzC,IAAI,CAACJ,MAAL,EAAa;MAAA;;MACXA,MAAM,GAAG,OAAOK,aAAa,CAC3BpB,yBAD2B,EAE3BkB,GAF2B,EAG3BL,OAH2B,EAI3BC,MAJ2B,EAK3B,qBAAAF,WAAW,CAACS,GAAZ,sCAAiBjB,OAAjB,MAA6Bc,GAA7B,GACII,oBAAoB,CAACV,WAAW,CAACS,GAAb,CADxB,GAEI,IAPuB,CAA7B;IASD;;IAED,IAAI,CAACL,MAAL,EAAa;MACX,MAAMO,SAAS,GAAGf,OAAA,CAAKC,IAAL,CAAUS,GAAV,EAAejB,oBAAf,CAAlB;;MACAe,MAAM,GAAG,OAAOQ,gBAAgB,CAACD,SAAD,CAAhC;;MAEA,IAAIP,MAAJ,EAAY;QACVnB,KAAK,CAAC,0BAAD,EAA6BmB,MAAM,CAACC,QAApC,EAA8Cb,OAA9C,CAAL;MACD;IACF;EACF;;EAED,OAAO;IAAEW,MAAF;IAAUC;EAAV,CAAP;AACD;;AAEM,SAASS,cAAT,CACLrB,OADK,EAELS,OAFK,EAGLC,MAHK,EAIuB;EAC5B,OAAOM,aAAa,CAACrB,qBAAD,EAAwBK,OAAxB,EAAiCS,OAAjC,EAA0CC,MAA1C,CAApB;AACD;;AAED,UAAUM,aAAV,CACEM,KADF,EAEEtB,OAFF,EAGES,OAHF,EAIEC,MAJF,EAKEa,cAAiC,GAAG,IALtC,EAM8B;EAC5B,MAAMC,OAAO,GAAG,OAAOC,UAAA,CAAQC,GAAR,CACrBJ,KAAK,CAACK,GAAN,CAAU1B,QAAQ,IAChB2B,UAAU,CAACxB,OAAA,CAAKC,IAAL,CAAUL,OAAV,EAAmBC,QAAnB,CAAD,EAA+BQ,OAA/B,EAAwCC,MAAxC,CADZ,CADqB,CAAvB;EAKA,MAAMC,MAAM,GAAGa,OAAO,CAACK,MAAR,CAAe,CAACN,cAAD,EAAoCZ,MAApC,KAA+C;IAC3E,IAAIA,MAAM,IAAIY,cAAd,EAA8B;MAC5B,MAAM,IAAIO,oBAAJ,CACH,0DAAD,GACG,MAAK1B,OAAA,CAAK2B,QAAL,CAAcR,cAAc,CAACV,QAA7B,CAAuC,IAD/C,GAEG,MAAKF,MAAM,CAACE,QAAS,IAFxB,GAGG,QAAOb,OAAQ,EAJd,CAAN;IAMD;;IAED,OAAOW,MAAM,IAAIY,cAAjB;EACD,CAXc,EAWZA,cAXY,CAAf;;EAaA,IAAIZ,MAAJ,EAAY;IACVlB,KAAK,CAAC,iCAAD,EAAoCkB,MAAM,CAACE,QAA3C,EAAqDb,OAArD,CAAL;EACD;;EACD,OAAOW,MAAP;AACD;;AAEM,UAAUqB,UAAV,CACLC,IADK,EAELjC,OAFK,EAGLS,OAHK,EAILC,MAJK,EAKgB;EACrB,MAAMG,QAAQ,GAAG;IAAA;EAAA;IAAA;;IAAA;IAAA;IAAA;IAAA;EAAA,GAAgBoB,IAAhB,EAAsB;IAAEC,KAAK,EAAE,CAAClC,OAAD;EAAT,CAAtB,CAAjB;EAEA,MAAMmC,IAAI,GAAG,OAAOP,UAAU,CAACf,QAAD,EAAWJ,OAAX,EAAoBC,MAApB,CAA9B;;EACA,IAAI,CAACyB,IAAL,EAAW;IACT,MAAM,IAAIL,oBAAJ,CACH,4CADG,EAEJjB,QAFI,CAAN;EAID;;EAEDpB,KAAK,CAAC,2BAAD,EAA8BwC,IAA9B,EAAoCjC,OAApC,CAAL;EACA,OAAOmC,IAAP;AACD;;AAMD,SAASP,UAAT,CACEf,QADF,EAEEJ,OAFF,EAGEC,MAHF,EAI8B;EAC5B,MAAM0B,GAAG,GAAGhC,OAAA,CAAKiC,OAAL,CAAaxB,QAAb,CAAZ;;EACA,OAAOuB,GAAG,KAAK,KAAR,IAAiBA,GAAG,KAAK,MAAzB,IAAmCA,GAAG,KAAK,MAA3C,GACHE,YAAY,CAACzB,QAAD,EAAW;IAAEJ,OAAF;IAAWC;EAAX,CAAX,CADT,GAEH6B,eAAe,CAAC1B,QAAD,CAFnB;AAGD;;AAED,MAAM2B,eAAe,GAAG,IAAIC,GAAJ,EAAxB;AAEA,MAAMH,YAAY,GAAG,IAAAI,wBAAA,EAAgB,UAAUJ,YAAV,CACnCzB,QADmC,EAEnC8B,KAFmC,EAMP;EAC5B,IAAI,CAACzC,KAAA,CAAOC,UAAP,CAAkBU,QAAlB,CAAL,EAAkC;IAChC8B,KAAK,CAACC,KAAN;IACA,OAAO,IAAP;EACD;;EAKD,IAAIJ,eAAe,CAACK,GAAhB,CAAoBhC,QAApB,CAAJ,EAAmC;IACjC8B,KAAK,CAACC,KAAN;IAEAnD,KAAK,CAAC,mCAAD,EAAsCoB,QAAtC,CAAL;IACA,OAAO;MACLA,QADK;MAELb,OAAO,EAAEI,OAAA,CAAKJ,OAAL,CAAaa,QAAb,CAFJ;MAGLiC,OAAO,EAAE;IAHJ,CAAP;EAKD;;EAED,IAAIA,OAAJ;;EACA,IAAI;IACFN,eAAe,CAACO,GAAhB,CAAoBlC,QAApB;IACAiC,OAAO,GAAG,OAAO,IAAAE,oBAAA,EACfnC,QADe,EAEf,qEACE,kEAHa,CAAjB;EAKD,CAPD,SAOU;IACR2B,eAAe,CAACS,MAAhB,CAAuBpC,QAAvB;EACD;;EAED,IAAIqC,WAAW,GAAG,KAAlB;;EACA,IAAI,OAAOJ,OAAP,KAAmB,UAAvB,EAAmC;IAEjC,OAAO,EAAP;IAEAA,OAAO,GAAG,IAAAK,qCAAA,EAAmBL,OAAnB,EACR,IAAAM,wBAAA,EAAcT,KAAd,CADQ,CAAV;IAIAO,WAAW,GAAG,IAAd;EACD;;EAED,IAAI,CAACJ,OAAD,IAAY,OAAOA,OAAP,KAAmB,QAA/B,IAA2CO,KAAK,CAACC,OAAN,CAAcR,OAAd,CAA/C,EAAuE;IACrE,MAAM,IAAIhB,oBAAJ,CACH,wDADG,EAEJjB,QAFI,CAAN;EAID;;EAGD,IAAI,OAAOiC,OAAO,CAACS,IAAf,KAAwB,UAA5B,EAAwC;IACtC,MAAM,IAAIzB,oBAAJ,CACH,iDAAD,GACG,wDADH,GAEG,6CAFH,GAGG,oEAHH,GAIG,0EALC,EAMJjB,QANI,CAAN;EAQD;;EAED,IAAIqC,WAAW,IAAI,CAACP,KAAK,CAACa,UAAN,EAApB,EAAwCC,gBAAgB,CAAC5C,QAAD,CAAhB;EAExC,OAAO;IACLA,QADK;IAELb,OAAO,EAAEI,OAAA,CAAKJ,OAAL,CAAaa,QAAb,CAFJ;IAGLiC;EAHK,CAAP;AAKD,CA5EoB,CAArB;AA8EA,MAAM5B,oBAAoB,GAAG,IAAAwC,0BAAA,EAC1BC,IAAD,IAAyC;EACvC,MAAMC,KAAc,GAAGD,IAAI,CAACb,OAAL,CAAa,OAAb,CAAvB;EAEA,IAAI,OAAOc,KAAP,KAAiB,WAArB,EAAkC,OAAO,IAAP;;EAElC,IAAI,OAAOA,KAAP,KAAiB,QAAjB,IAA6BP,KAAK,CAACC,OAAN,CAAcM,KAAd,CAA7B,IAAqDA,KAAK,KAAK,IAAnE,EAAyE;IACvE,MAAM,IAAI9B,oBAAJ,CAAiB,mCAAjB,EAAqD6B,IAAI,CAAC9C,QAA1D,CAAN;EACD;;EAED,OAAO;IACLA,QAAQ,EAAE8C,IAAI,CAAC9C,QADV;IAELb,OAAO,EAAE2D,IAAI,CAAC3D,OAFT;IAGL8C,OAAO,EAAEc;EAHJ,CAAP;AAKD,CAf0B,CAA7B;AAkBA,MAAMrB,eAAe,GAAG,IAAAsB,0BAAA,EAAoB,CAAChD,QAAD,EAAWiD,OAAX,KAAmC;EAC7E,IAAIhB,OAAJ;;EACA,IAAI;IACFA,OAAO,GAAGiB,OAAA,CAAMC,KAAN,CAAYF,OAAZ,CAAV;EACD,CAFD,CAEE,OAAOG,GAAP,EAAY;IACZ,MAAM,IAAInC,oBAAJ,CACH,gCAA+BmC,GAAG,CAACC,OAAQ,EADxC,EAEJrD,QAFI,CAAN;EAID;;EAED,IAAI,CAACiC,OAAL,EAAc,MAAM,IAAIhB,oBAAJ,CAAiB,oBAAjB,EAAsCjB,QAAtC,CAAN;;EAEd,IAAI,OAAOiC,OAAP,KAAmB,QAAvB,EAAiC;IAC/B,MAAM,IAAIhB,oBAAJ,CAAiB,0BAAyB,OAAOgB,OAAQ,EAAzD,EAA4DjC,QAA5D,CAAN;EACD;;EACD,IAAIwC,KAAK,CAACC,OAAN,CAAcR,OAAd,CAAJ,EAA4B;IAC1B,MAAM,IAAIhB,oBAAJ,CAAiB,wCAAjB,EAA0DjB,QAA1D,CAAN;EACD;;EAED,OAAOiC,OAAO,CAAC,SAAD,CAAd;EAEA,OAAO;IACLjC,QADK;IAELb,OAAO,EAAEI,OAAA,CAAKJ,OAAL,CAAaa,QAAb,CAFJ;IAGLiC;EAHK,CAAP;AAKD,CA3BuB,CAAxB;AA6BA,MAAM1B,gBAAgB,GAAG,IAAAyC,0BAAA,EAAoB,CAAChD,QAAD,EAAWiD,OAAX,KAAuB;EAClE,MAAMK,SAAS,GAAG/D,OAAA,CAAKJ,OAAL,CAAaa,QAAb,CAAlB;;EACA,MAAMuD,cAAc,GAAGN,OAAO,CAC3BO,KADoB,CACd,IADc,EAEpB1C,GAFoB,CAER2C,IAAI,IAAIA,IAAI,CAACC,OAAL,CAAa,SAAb,EAAwB,EAAxB,EAA4BC,IAA5B,EAFA,EAGpBC,MAHoB,CAGbH,IAAI,IAAI,CAAC,CAACA,IAHG,CAAvB;;EAKA,KAAK,MAAMI,OAAX,IAAsBN,cAAtB,EAAsC;IACpC,IAAIM,OAAO,CAAC,CAAD,CAAP,KAAe,GAAnB,EAAwB;MACtB,MAAM,IAAI5C,oBAAJ,CACH,0CADG,EAEJjB,QAFI,CAAN;IAID;EACF;;EAED,OAAO;IACLA,QADK;IAELb,OAAO,EAAEI,OAAA,CAAKJ,OAAL,CAAaa,QAAb,CAFJ;IAGLD,MAAM,EAAEwD,cAAc,CAACzC,GAAf,CAAmB+C,OAAO,IAChC,IAAAC,uBAAA,EAAmBD,OAAnB,EAA4BP,SAA5B,CADM;EAHH,CAAP;AAOD,CAvBwB,CAAzB;;AAyBO,UAAUS,qBAAV,CACL5E,OADK,EAEmB;EACxB,MAAM6E,UAAU,GAAGC,OAAO,CAACC,GAAR,CAAYC,qBAA/B;;EACA,IAAIH,UAAU,IAAI,IAAlB,EAAwB;IACtB,MAAMI,YAAY,GAAG7E,OAAA,CAAK8E,OAAL,CAAalF,OAAb,EAAsB6E,UAAtB,CAArB;;IACA,MAAMM,KAAK,GAAG,OAAOC,EAAE,CAACC,IAAH,CAAQJ,YAAR,CAArB;;IACA,IAAI,CAACE,KAAK,CAACG,MAAN,EAAL,EAAqB;MACnB,MAAM,IAAIC,KAAJ,CACH,GAAEN,YAAa,sFADZ,CAAN;IAGD;;IACD,OAAOA,YAAP;EACD;;EACD,OAAO,IAAP;AACD;;AAED,SAASxB,gBAAT,CAA0B5C,QAA1B,EAAmD;EACjD,MAAM,IAAIiB,oBAAJ,CACH;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAlCQ,EAmCJjB,QAnCI,CAAN;AAqCD"}