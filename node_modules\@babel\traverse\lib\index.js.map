{"version": 3, "names": ["VISITOR_KEYS", "removeProperties", "traverseFast", "traverse", "parent", "opts", "scope", "state", "parentPath", "noScope", "type", "Error", "visitors", "explode", "traverseNode", "verify", "cheap", "node", "enter", "path", "<PERSON><PERSON><PERSON><PERSON>", "clearNode", "cache", "delete", "tree", "hasDenylistedType", "has", "stop", "hasType", "denylistTypes", "includes", "denylist"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as visitors from \"./visitors\";\nimport {\n  VISITOR_KEYS,\n  removeProperties,\n  type RemovePropertiesOptions,\n  traverseFast,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as cache from \"./cache\";\nimport type NodePath from \"./path\";\nimport type { default as Scope, Binding } from \"./scope\";\nimport type { Visitor } from \"./types\";\nimport { traverseNode } from \"./traverse-node\";\n\nexport type { Visitor, Binding };\nexport { default as NodePath } from \"./path\";\nexport { default as Scope } from \"./scope\";\nexport { default as Hub } from \"./hub\";\nexport type { HubInterface } from \"./hub\";\n\nexport { visitors };\n\nexport type TraverseOptions<S = t.Node> = {\n  scope?: Scope;\n  noScope?: boolean;\n  denylist?: string[];\n} & Visitor<S>;\n\nfunction traverse<S>(\n  parent: t.Node,\n  opts: TraverseOptions<S>,\n  scope: Scope | undefined,\n  state: S,\n  parentPath?: NodePath,\n): void;\n\nfunction traverse(\n  parent: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n): void;\n\nfunction traverse<Options extends TraverseOptions>(\n  parent: t.Node,\n  // @ts-expect-error provide {} as default value for Options\n  opts: Options = {},\n  scope?: Scope,\n  state?: any,\n  parentPath?: NodePath,\n) {\n  if (!parent) return;\n\n  if (!opts.noScope && !scope) {\n    if (parent.type !== \"Program\" && parent.type !== \"File\") {\n      throw new Error(\n        \"You must pass a scope and parentPath unless traversing a Program/File. \" +\n          `Instead of that you tried to traverse a ${parent.type} node without ` +\n          \"passing scope and parentPath.\",\n      );\n    }\n  }\n\n  if (!VISITOR_KEYS[parent.type]) {\n    return;\n  }\n\n  visitors.explode(opts as Visitor);\n\n  traverseNode(parent, opts, scope, state, parentPath);\n}\n\nexport default traverse;\n\ntraverse.visitors = visitors;\ntraverse.verify = visitors.verify;\ntraverse.explode = visitors.explode;\n\ntraverse.cheap = function (node: t.Node, enter: (node: t.Node) => void) {\n  return traverseFast(node, enter);\n};\n\ntraverse.node = function (\n  node: t.Node,\n  opts: TraverseOptions,\n  scope?: Scope,\n  state?: any,\n  path?: NodePath,\n  skipKeys?: Record<string, boolean>,\n) {\n  traverseNode(node, opts, scope, state, path, skipKeys);\n  // traverse.node always returns undefined\n};\n\ntraverse.clearNode = function (node: t.Node, opts?: RemovePropertiesOptions) {\n  removeProperties(node, opts);\n\n  cache.path.delete(node);\n};\n\ntraverse.removeProperties = function (\n  tree: t.Node,\n  opts?: RemovePropertiesOptions,\n) {\n  traverseFast(tree, traverse.clearNode, opts);\n  return tree;\n};\n\ntype HasDenylistedTypeState = {\n  has: boolean;\n  type: t.Node[\"type\"];\n};\nfunction hasDenylistedType(path: NodePath, state: HasDenylistedTypeState) {\n  if (path.node.type === state.type) {\n    state.has = true;\n    path.stop();\n  }\n}\n\ntraverse.hasType = function (\n  tree: t.Node,\n  type: t.Node[\"type\"],\n  denylistTypes?: Array<string>,\n): boolean {\n  // the node we're searching in is denylisted\n  if (denylistTypes?.includes(tree.type)) return false;\n\n  // the type we're looking for is the same as the passed node\n  if (tree.type === type) return true;\n\n  const state: HasDenylistedTypeState = {\n    has: false,\n    type: type,\n  };\n\n  traverse(\n    tree,\n    {\n      noScope: true,\n      denylist: denylistTypes,\n      enter: hasDenylistedType,\n    },\n    null,\n    state,\n  );\n\n  return state.has;\n};\n\ntraverse.cache = cache;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;AACA;;AAOA;;AAIA;;AAGA;;AACA;;AACA;;;EAfEA,Y;EACAC,gB;EAEAC;;;AAuCF,SAASC,QAAT,CACEC,MADF,EAGEC,IAAa,GAAG,EAHlB,EAIEC,KAJF,EAKEC,KALF,EAMEC,UANF,EAOE;EACA,IAAI,CAACJ,MAAL,EAAa;;EAEb,IAAI,CAACC,IAAI,CAACI,OAAN,IAAiB,CAACH,KAAtB,EAA6B;IAC3B,IAAIF,MAAM,CAACM,IAAP,KAAgB,SAAhB,IAA6BN,MAAM,CAACM,IAAP,KAAgB,MAAjD,EAAyD;MACvD,MAAM,IAAIC,KAAJ,CACJ,4EACG,2CAA0CP,MAAM,CAACM,IAAK,gBADzD,GAEE,+BAHE,CAAN;IAKD;EACF;;EAED,IAAI,CAACV,YAAY,CAACI,MAAM,CAACM,IAAR,CAAjB,EAAgC;IAC9B;EACD;;EAEDE,QAAQ,CAACC,OAAT,CAAiBR,IAAjB;EAEA,IAAAS,0BAAA,EAAaV,MAAb,EAAqBC,IAArB,EAA2BC,KAA3B,EAAkCC,KAAlC,EAAyCC,UAAzC;AACD;;eAEcL,Q;;AAEfA,QAAQ,CAACS,QAAT,GAAoBA,QAApB;AACAT,QAAQ,CAACY,MAAT,GAAkBH,QAAQ,CAACG,MAA3B;AACAZ,QAAQ,CAACU,OAAT,GAAmBD,QAAQ,CAACC,OAA5B;;AAEAV,QAAQ,CAACa,KAAT,GAAiB,UAAUC,IAAV,EAAwBC,KAAxB,EAAuD;EACtE,OAAOhB,YAAY,CAACe,IAAD,EAAOC,KAAP,CAAnB;AACD,CAFD;;AAIAf,QAAQ,CAACc,IAAT,GAAgB,UACdA,IADc,EAEdZ,IAFc,EAGdC,KAHc,EAIdC,KAJc,EAKdY,IALc,EAMdC,QANc,EAOd;EACA,IAAAN,0BAAA,EAAaG,IAAb,EAAmBZ,IAAnB,EAAyBC,KAAzB,EAAgCC,KAAhC,EAAuCY,IAAvC,EAA6CC,QAA7C;AAED,CAVD;;AAYAjB,QAAQ,CAACkB,SAAT,GAAqB,UAAUJ,IAAV,EAAwBZ,IAAxB,EAAwD;EAC3EJ,gBAAgB,CAACgB,IAAD,EAAOZ,IAAP,CAAhB;EAEAiB,KAAK,CAACH,IAAN,CAAWI,MAAX,CAAkBN,IAAlB;AACD,CAJD;;AAMAd,QAAQ,CAACF,gBAAT,GAA4B,UAC1BuB,IAD0B,EAE1BnB,IAF0B,EAG1B;EACAH,YAAY,CAACsB,IAAD,EAAOrB,QAAQ,CAACkB,SAAhB,EAA2BhB,IAA3B,CAAZ;EACA,OAAOmB,IAAP;AACD,CAND;;AAYA,SAASC,iBAAT,CAA2BN,IAA3B,EAA2CZ,KAA3C,EAA0E;EACxE,IAAIY,IAAI,CAACF,IAAL,CAAUP,IAAV,KAAmBH,KAAK,CAACG,IAA7B,EAAmC;IACjCH,KAAK,CAACmB,GAAN,GAAY,IAAZ;IACAP,IAAI,CAACQ,IAAL;EACD;AACF;;AAEDxB,QAAQ,CAACyB,OAAT,GAAmB,UACjBJ,IADiB,EAEjBd,IAFiB,EAGjBmB,aAHiB,EAIR;EAET,IAAIA,aAAJ,YAAIA,aAAa,CAAEC,QAAf,CAAwBN,IAAI,CAACd,IAA7B,CAAJ,EAAwC,OAAO,KAAP;EAGxC,IAAIc,IAAI,CAACd,IAAL,KAAcA,IAAlB,EAAwB,OAAO,IAAP;EAExB,MAAMH,KAA6B,GAAG;IACpCmB,GAAG,EAAE,KAD+B;IAEpChB,IAAI,EAAEA;EAF8B,CAAtC;EAKAP,QAAQ,CACNqB,IADM,EAEN;IACEf,OAAO,EAAE,IADX;IAEEsB,QAAQ,EAAEF,aAFZ;IAGEX,KAAK,EAAEO;EAHT,CAFM,EAON,IAPM,EAQNlB,KARM,CAAR;EAWA,OAAOA,KAAK,CAACmB,GAAb;AACD,CA5BD;;AA8BAvB,QAAQ,CAACmB,KAAT,GAAiBA,KAAjB"}