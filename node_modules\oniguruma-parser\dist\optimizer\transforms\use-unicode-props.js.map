{"version": 3, "sources": ["../../../src/optimizer/transforms/use-unicode-props.ts"], "sourcesContent": ["import type {CharacterSetNode} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\nimport {createUnicodeProperty} from '../../parser/parse.js';\nimport {isRange} from './use-shorthands.js';\n\n/**\nUse Unicode properties when possible.\n- `\\p{Any}` from `[\\0-\\x{10FFFF}]`\n- `\\p{Cc}` from POSIX `\\p{cntrl}`, `[[:cntrl:]]`\nSee also `useShorthands`.\n*/\nconst useUnicodeProps: Visitor = {\n  CharacterSet({node, root, replaceWith}) {\n    const {kind, negate, value} = node;\n    let newNode: CharacterSetNode | null = null;\n    if (\n      kind === 'posix' &&\n      value === 'cntrl' &&\n      // TODO: Also check local context when the parser supports this flag on pattern modifiers\n      !root.flags.posixIsAscii\n    ) {\n      newNode = createUnicodeProperty('Cc', {negate});\n    }\n    if (newNode) {\n      replaceWith(newNode);\n    }\n  },\n\n  CharacterClassRange({node, replaceWith}) {\n    if (isRange(node, 0, 0x10FFFF)) {\n      replaceWith(createUnicodeProperty('Any'));\n    }\n  },\n};\n\nexport {\n  useUnicodeProps,\n};\n"], "mappings": "aAEA,OAAQ,yBAAAA,MAA4B,wBACpC,OAAQ,WAAAC,MAAc,sBAQtB,MAAMC,EAA2B,CAC/B,aAAa,CAAC,KAAAC,EAAM,KAAAC,EAAM,YAAAC,CAAW,EAAG,CACtC,KAAM,CAAC,KAAAC,EAAM,OAAAC,EAAQ,MAAAC,CAAK,EAAIL,EAC9B,IAAIM,EAAmC,KAErCH,IAAS,SACTE,IAAU,SAEV,CAACJ,EAAK,MAAM,eAEZK,EAAUT,EAAsB,KAAM,CAAC,OAAAO,CAAM,CAAC,GAE5CE,GACFJ,EAAYI,CAAO,CAEvB,EAEA,oBAAoB,CAAC,KAAAN,EAAM,YAAAE,CAAW,EAAG,CACnCJ,EAAQE,EAAM,EAAG,OAAQ,GAC3BE,EAAYL,EAAsB,KAAK,CAAC,CAE5C,CACF,EAEA,OACEE,KAAA", "names": ["createUnicodeProperty", "isRange", "useUnicodeProps", "node", "root", "replaceWith", "kind", "negate", "value", "newNode"]}