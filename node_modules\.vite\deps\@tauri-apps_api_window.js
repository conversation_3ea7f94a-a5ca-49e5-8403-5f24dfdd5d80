import {
  o as o3
} from "./chunk-B2ZHVKHC.js";
import {
  a,
  n,
  o,
  o2,
  r
} from "./chunk-IFV3GWMF.js";
import "./chunk-FZ7BPVOU.js";

// node_modules/@tauri-apps/api/event-86d4e8b3.js
function e(r2, e2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o3({ __tauriModule: "Event", message: { cmd: "unlisten", event: r2, eventId: e2 } })];
    });
  });
}
function u(r2, e2, u2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      switch (t.label) {
        case 0:
          return [4, o3({ __tauriModule: "Event", message: { cmd: "emit", event: r2, windowLabel: e2, payload: "string" == typeof u2 ? u2 : JSON.stringify(u2) } })];
        case 1:
          return t.sent(), [2];
      }
    });
  });
}
function o4(u2, o5, a3) {
  return o(this, void 0, void 0, function() {
    var s2 = this;
    return a(this, function(c3) {
      return [2, o3({ __tauriModule: "Event", message: { cmd: "listen", event: u2, windowLabel: o5, handler: o2(a3) } }).then(function(i) {
        return function() {
          return o(s2, void 0, void 0, function() {
            return a(this, function(t) {
              return [2, e(u2, i)];
            });
          });
        };
      })];
    });
  });
}
function a2(i, r2, u2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o4(i, r2, function(t2) {
        u2(t2), e(i, t2.id).catch(function() {
        });
      })];
    });
  });
}
var s;
function c(i, r2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o4(i, null, r2)];
    });
  });
}
function d(i, r2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, a2(i, null, r2)];
    });
  });
}
function f(i, r2) {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, u(i, void 0, r2)];
    });
  });
}
!function(t) {
  t.WINDOW_RESIZED = "tauri://resize", t.WINDOW_MOVED = "tauri://move", t.WINDOW_CLOSE_REQUESTED = "tauri://close-requested", t.WINDOW_CREATED = "tauri://window-created", t.WINDOW_DESTROYED = "tauri://destroyed", t.WINDOW_FOCUS = "tauri://focus", t.WINDOW_BLUR = "tauri://blur", t.WINDOW_SCALE_FACTOR_CHANGED = "tauri://scale-change", t.WINDOW_THEME_CHANGED = "tauri://theme-changed", t.WINDOW_FILE_DROP = "tauri://file-drop", t.WINDOW_FILE_DROP_HOVER = "tauri://file-drop-hover", t.WINDOW_FILE_DROP_CANCELLED = "tauri://file-drop-cancelled", t.MENU = "tauri://menu", t.CHECK_UPDATE = "tauri://update", t.UPDATE_AVAILABLE = "tauri://update-available", t.INSTALL_UPDATE = "tauri://update-install", t.STATUS_UPDATE = "tauri://update-status", t.DOWNLOAD_PROGRESS = "tauri://update-download-progress";
}(s || (s = {}));
var _ = Object.freeze({ __proto__: null, get TauriEvent() {
  return s;
}, listen: c, once: d, emit: f });

// node_modules/@tauri-apps/api/window-25493f72.js
var d2;
var c2 = function(t, e2) {
  this.type = "Logical", this.width = t, this.height = e2;
};
var l = function() {
  function t(t2, e2) {
    this.type = "Physical", this.width = t2, this.height = e2;
  }
  return t.prototype.toLogical = function(t2) {
    return new c2(this.width / t2, this.height / t2);
  }, t;
}();
var h = function(t, e2) {
  this.type = "Logical", this.x = t, this.y = e2;
};
var p = function() {
  function t(t2, e2) {
    this.type = "Physical", this.x = t2, this.y = e2;
  }
  return t.prototype.toLogical = function(t2) {
    return new h(this.x / t2, this.y / t2);
  }, t;
}();
function f2() {
  return new w(window.__TAURI_METADATA__.__currentWindow.label, { skip: true });
}
function m() {
  return window.__TAURI_METADATA__.__windows.map(function(t) {
    return new w(t.label, { skip: true });
  });
}
!function(t) {
  t[t.Critical = 1] = "Critical", t[t.Informational = 2] = "Informational";
}(d2 || (d2 = {}));
var y;
var v = ["tauri://created", "tauri://error"];
var _2 = function() {
  function t(t2) {
    this.label = t2, this.listeners = /* @__PURE__ */ Object.create(null);
  }
  return t.prototype.listen = function(t2, n2) {
    return o(this, void 0, void 0, function() {
      var e2 = this;
      return a(this, function(i) {
        return this._handleTauriEvent(t2, n2) ? [2, Promise.resolve(function() {
          var i2 = e2.listeners[t2];
          i2.splice(i2.indexOf(n2), 1);
        })] : [2, o4(t2, this.label, n2)];
      });
    });
  }, t.prototype.once = function(t2, n2) {
    return o(this, void 0, void 0, function() {
      var e2 = this;
      return a(this, function(i) {
        return this._handleTauriEvent(t2, n2) ? [2, Promise.resolve(function() {
          var i2 = e2.listeners[t2];
          i2.splice(i2.indexOf(n2), 1);
        })] : [2, a2(t2, this.label, n2)];
      });
    });
  }, t.prototype.emit = function(t2, n2) {
    return o(this, void 0, void 0, function() {
      var e2, o5;
      return a(this, function(i) {
        if (v.includes(t2)) {
          for (e2 = 0, o5 = this.listeners[t2] || []; e2 < o5.length; e2++)
            (0, o5[e2])({ event: t2, id: -1, windowLabel: this.label, payload: n2 });
          return [2, Promise.resolve()];
        }
        return [2, u(t2, this.label, n2)];
      });
    });
  }, t.prototype._handleTauriEvent = function(t2, e2) {
    return !!v.includes(t2) && (t2 in this.listeners ? this.listeners[t2].push(e2) : this.listeners[t2] = [e2], true);
  }, t;
}();
var g = function(r2) {
  function a3() {
    return null !== r2 && r2.apply(this, arguments) || this;
  }
  return n(a3, r2), a3.prototype.scaleFactor = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "scaleFactor" } } } })];
      });
    });
  }, a3.prototype.innerPosition = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "innerPosition" } } } }).then(function(t2) {
          var e2 = t2.x, i = t2.y;
          return new p(e2, i);
        })];
      });
    });
  }, a3.prototype.outerPosition = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "outerPosition" } } } }).then(function(t2) {
          var e2 = t2.x, i = t2.y;
          return new p(e2, i);
        })];
      });
    });
  }, a3.prototype.innerSize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "innerSize" } } } }).then(function(t2) {
          var e2 = t2.width, i = t2.height;
          return new l(e2, i);
        })];
      });
    });
  }, a3.prototype.outerSize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "outerSize" } } } }).then(function(t2) {
          var e2 = t2.width, i = t2.height;
          return new l(e2, i);
        })];
      });
    });
  }, a3.prototype.isFullscreen = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "isFullscreen" } } } })];
      });
    });
  }, a3.prototype.isMaximized = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "isMaximized" } } } })];
      });
    });
  }, a3.prototype.isDecorated = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "isDecorated" } } } })];
      });
    });
  }, a3.prototype.isResizable = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "isResizable" } } } })];
      });
    });
  }, a3.prototype.isVisible = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "isVisible" } } } })];
      });
    });
  }, a3.prototype.theme = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "theme" } } } })];
      });
    });
  }, a3.prototype.center = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "center" } } } })];
      });
    });
  }, a3.prototype.requestUserAttention = function(t) {
    return o(this, void 0, void 0, function() {
      var e2;
      return a(this, function(i) {
        return e2 = null, t && (e2 = t === d2.Critical ? { type: "Critical" } : { type: "Informational" }), [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "requestUserAttention", payload: e2 } } } })];
      });
    });
  }, a3.prototype.setResizable = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setResizable", payload: t } } } })];
      });
    });
  }, a3.prototype.setTitle = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setTitle", payload: t } } } })];
      });
    });
  }, a3.prototype.maximize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "maximize" } } } })];
      });
    });
  }, a3.prototype.unmaximize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "unmaximize" } } } })];
      });
    });
  }, a3.prototype.toggleMaximize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "toggleMaximize" } } } })];
      });
    });
  }, a3.prototype.minimize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "minimize" } } } })];
      });
    });
  }, a3.prototype.unminimize = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "unminimize" } } } })];
      });
    });
  }, a3.prototype.show = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "show" } } } })];
      });
    });
  }, a3.prototype.hide = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "hide" } } } })];
      });
    });
  }, a3.prototype.close = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "close" } } } })];
      });
    });
  }, a3.prototype.setDecorations = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setDecorations", payload: t } } } })];
      });
    });
  }, a3.prototype.setAlwaysOnTop = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setAlwaysOnTop", payload: t } } } })];
      });
    });
  }, a3.prototype.setSize = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        if (!t || "Logical" !== t.type && "Physical" !== t.type)
          throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setSize", payload: { type: t.type, data: { width: t.width, height: t.height } } } } } })];
      });
    });
  }, a3.prototype.setMinSize = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        if (t && "Logical" !== t.type && "Physical" !== t.type)
          throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setMinSize", payload: t ? { type: t.type, data: { width: t.width, height: t.height } } : null } } } })];
      });
    });
  }, a3.prototype.setMaxSize = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        if (t && "Logical" !== t.type && "Physical" !== t.type)
          throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setMaxSize", payload: t ? { type: t.type, data: { width: t.width, height: t.height } } : null } } } })];
      });
    });
  }, a3.prototype.setPosition = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        if (!t || "Logical" !== t.type && "Physical" !== t.type)
          throw new Error("the `position` argument must be either a LogicalPosition or a PhysicalPosition instance");
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setPosition", payload: { type: t.type, data: { x: t.x, y: t.y } } } } } })];
      });
    });
  }, a3.prototype.setFullscreen = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setFullscreen", payload: t } } } })];
      });
    });
  }, a3.prototype.setFocus = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setFocus" } } } })];
      });
    });
  }, a3.prototype.setIcon = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setIcon", payload: { icon: "string" == typeof t ? t : Array.from(t) } } } } })];
      });
    });
  }, a3.prototype.setSkipTaskbar = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setSkipTaskbar", payload: t } } } })];
      });
    });
  }, a3.prototype.setCursorGrab = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setCursorGrab", payload: t } } } })];
      });
    });
  }, a3.prototype.setCursorVisible = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setCursorVisible", payload: t } } } })];
      });
    });
  }, a3.prototype.setCursorIcon = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setCursorIcon", payload: t } } } })];
      });
    });
  }, a3.prototype.setCursorPosition = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        if (!t || "Logical" !== t.type && "Physical" !== t.type)
          throw new Error("the `position` argument must be either a LogicalPosition or a PhysicalPosition instance");
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "setCursorPosition", payload: { type: t.type, data: { x: t.x, y: t.y } } } } } })];
      });
    });
  }, a3.prototype.startDragging = function() {
    return o(this, void 0, void 0, function() {
      return a(this, function(t) {
        return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { label: this.label, cmd: { type: "startDragging" } } } })];
      });
    });
  }, a3.prototype.onResized = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, this.listen(s.WINDOW_RESIZED, t)];
      });
    });
  }, a3.prototype.onMoved = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, this.listen(s.WINDOW_MOVED, t)];
      });
    });
  }, a3.prototype.onCloseRequested = function(t) {
    return o(this, void 0, void 0, function() {
      var e2 = this;
      return a(this, function(i) {
        return [2, this.listen(s.WINDOW_CLOSE_REQUESTED, function(i2) {
          var n2 = new b(i2);
          Promise.resolve(t(n2)).then(function() {
            if (!n2.isPreventDefault())
              return e2.close();
          });
        })];
      });
    });
  }, a3.prototype.onFocusChanged = function(t) {
    return o(this, void 0, void 0, function() {
      var e2, o5;
      return a(this, function(i) {
        switch (i.label) {
          case 0:
            return [4, this.listen(s.WINDOW_FOCUS, function(e3) {
              t(r(r({}, e3), { payload: true }));
            })];
          case 1:
            return e2 = i.sent(), [4, this.listen(s.WINDOW_BLUR, function(e3) {
              t(r(r({}, e3), { payload: false }));
            })];
          case 2:
            return o5 = i.sent(), [2, function() {
              e2(), o5();
            }];
        }
      });
    });
  }, a3.prototype.onScaleChanged = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, this.listen(s.WINDOW_SCALE_FACTOR_CHANGED, t)];
      });
    });
  }, a3.prototype.onMenuClicked = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, this.listen(s.MENU, t)];
      });
    });
  }, a3.prototype.onFileDropEvent = function(t) {
    return o(this, void 0, void 0, function() {
      var e2, o5, r3;
      return a(this, function(i) {
        switch (i.label) {
          case 0:
            return [4, this.listen(s.WINDOW_FILE_DROP, function(e3) {
              t(r(r({}, e3), { payload: { type: "drop", paths: e3.payload } }));
            })];
          case 1:
            return e2 = i.sent(), [4, this.listen(s.WINDOW_FILE_DROP_HOVER, function(e3) {
              t(r(r({}, e3), { payload: { type: "hover", paths: e3.payload } }));
            })];
          case 2:
            return o5 = i.sent(), [4, this.listen(s.WINDOW_FILE_DROP_CANCELLED, function(e3) {
              t(r(r({}, e3), { payload: { type: "cancel" } }));
            })];
          case 3:
            return r3 = i.sent(), [2, function() {
              e2(), o5(), r3();
            }];
        }
      });
    });
  }, a3.prototype.onThemeChanged = function(t) {
    return o(this, void 0, void 0, function() {
      return a(this, function(e2) {
        return [2, this.listen(s.WINDOW_THEME_CHANGED, t)];
      });
    });
  }, a3;
}(_2);
var b = function() {
  function t(t2) {
    this._preventDefault = false, this.event = t2.event, this.windowLabel = t2.windowLabel, this.id = t2.id;
  }
  return t.prototype.preventDefault = function() {
    this._preventDefault = true;
  }, t.prototype.isPreventDefault = function() {
    return this._preventDefault;
  }, t;
}();
var w = function(r2) {
  function a3(t, a4) {
    void 0 === a4 && (a4 = {});
    var u2 = r2.call(this, t) || this;
    return (null == a4 ? void 0 : a4.skip) || o3({ __tauriModule: "Window", message: { cmd: "createWebview", data: { options: r({ label: t }, a4) } } }).then(function() {
      return o(u2, void 0, void 0, function() {
        return a(this, function(t2) {
          return [2, this.emit("tauri://created")];
        });
      });
    }).catch(function(t2) {
      return o(u2, void 0, void 0, function() {
        return a(this, function(e2) {
          return [2, this.emit("tauri://error", t2)];
        });
      });
    }), u2;
  }
  return n(a3, r2), a3.getByLabel = function(t) {
    return m().some(function(e2) {
      return e2.label === t;
    }) ? new a3(t, { skip: true }) : null;
  }, a3;
}(g);
function W() {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { cmd: { type: "currentMonitor" } } } })];
    });
  });
}
function M() {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { cmd: { type: "primaryMonitor" } } } })];
    });
  });
}
function z() {
  return o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return [2, o3({ __tauriModule: "Window", message: { cmd: "manage", data: { cmd: { type: "availableMonitors" } } } })];
    });
  });
}
"__TAURI_METADATA__" in window ? y = new w(window.__TAURI_METADATA__.__currentWindow.label, { skip: true }) : (console.warn('Could not find "window.__TAURI_METADATA__". The "appWindow" value will reference the "main" window label.\nNote that this is not an issue if running this frontend on a browser instead of a Tauri window.'), y = new w("main", { skip: true }));
var P = Object.freeze({ __proto__: null, WebviewWindow: w, WebviewWindowHandle: _2, WindowManager: g, CloseRequestedEvent: b, getCurrent: f2, getAll: m, get appWindow() {
  return y;
}, LogicalSize: c2, PhysicalSize: l, LogicalPosition: h, PhysicalPosition: p, get UserAttentionType() {
  return d2;
}, currentMonitor: W, primaryMonitor: M, availableMonitors: z });
export {
  b as CloseRequestedEvent,
  h as LogicalPosition,
  c2 as LogicalSize,
  p as PhysicalPosition,
  l as PhysicalSize,
  d2 as UserAttentionType,
  w as WebviewWindow,
  _2 as WebviewWindowHandle,
  g as WindowManager,
  y as appWindow,
  z as availableMonitors,
  W as currentMonitor,
  m as getAll,
  f2 as getCurrent,
  M as primaryMonitor
};
//# sourceMappingURL=@tauri-apps_api_window.js.map
