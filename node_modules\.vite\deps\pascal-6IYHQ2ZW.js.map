{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/pascal/pascal.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/pascal/pascal.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"{\", \"}\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"<\", \">\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: \"<\", close: \">\" },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*\\\\{\\\\$REGION(\\\\s\\\\'.*\\\\')?\\\\}\"),\n      end: new RegExp(\"^\\\\s*\\\\{\\\\$ENDREGION\\\\}\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".pascal\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" },\n    { open: \"<\", close: \">\", token: \"delimiter.angle\" }\n  ],\n  keywords: [\n    \"absolute\",\n    \"abstract\",\n    \"all\",\n    \"and_then\",\n    \"array\",\n    \"as\",\n    \"asm\",\n    \"attribute\",\n    \"begin\",\n    \"bindable\",\n    \"case\",\n    \"class\",\n    \"const\",\n    \"contains\",\n    \"default\",\n    \"div\",\n    \"else\",\n    \"end\",\n    \"except\",\n    \"exports\",\n    \"external\",\n    \"far\",\n    \"file\",\n    \"finalization\",\n    \"finally\",\n    \"forward\",\n    \"generic\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"import\",\n    \"in\",\n    \"index\",\n    \"inherited\",\n    \"initialization\",\n    \"interrupt\",\n    \"is\",\n    \"label\",\n    \"library\",\n    \"mod\",\n    \"module\",\n    \"name\",\n    \"near\",\n    \"not\",\n    \"object\",\n    \"of\",\n    \"on\",\n    \"only\",\n    \"operator\",\n    \"or_else\",\n    \"otherwise\",\n    \"override\",\n    \"package\",\n    \"packed\",\n    \"pow\",\n    \"private\",\n    \"program\",\n    \"protected\",\n    \"public\",\n    \"published\",\n    \"interface\",\n    \"implementation\",\n    \"qualified\",\n    \"read\",\n    \"record\",\n    \"resident\",\n    \"requires\",\n    \"resourcestring\",\n    \"restricted\",\n    \"segment\",\n    \"set\",\n    \"shl\",\n    \"shr\",\n    \"specialize\",\n    \"stored\",\n    \"strict\",\n    \"then\",\n    \"threadvar\",\n    \"to\",\n    \"try\",\n    \"type\",\n    \"unit\",\n    \"uses\",\n    \"var\",\n    \"view\",\n    \"virtual\",\n    \"dynamic\",\n    \"overload\",\n    \"reintroduce\",\n    \"with\",\n    \"write\",\n    \"xor\",\n    \"true\",\n    \"false\",\n    \"procedure\",\n    \"function\",\n    \"constructor\",\n    \"destructor\",\n    \"property\",\n    \"break\",\n    \"continue\",\n    \"exit\",\n    \"abort\",\n    \"while\",\n    \"do\",\n    \"for\",\n    \"raise\",\n    \"repeat\",\n    \"until\"\n  ],\n  typeKeywords: [\n    \"boolean\",\n    \"double\",\n    \"byte\",\n    \"integer\",\n    \"shortint\",\n    \"char\",\n    \"longint\",\n    \"float\",\n    \"string\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \":\",\n    \":=\",\n    \"and\",\n    \"or\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"@\",\n    \"&\",\n    \"^\",\n    \"%\"\n  ],\n  symbols: /[=><:@\\^&|+\\-*\\/\\^%]+/,\n  tokenizer: {\n    root: [\n      [\n        /[a-zA-Z_][\\w]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\$[0-9a-fA-F]{1,16}/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/'/, \"string\", \"@string\"],\n      [/'[^\\\\']'/, \"string\"],\n      [/'/, \"string.invalid\"],\n      [/\\#\\d+/, \"string\"]\n    ],\n    comment: [\n      [/[^\\*\\}]+/, \"comment\"],\n      [/\\}/, \"comment\", \"@pop\"],\n      [/[\\{]/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\{/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,KAAK,GAAG;AAAA,EACzB;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,oCAAoC;AAAA,MACtD,KAAK,IAAI,OAAO,yBAAyB;AAAA,IAC3C;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,IACxD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,EACpD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,uBAAuB,YAAY;AAAA,MACpC,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,KAAK,gBAAgB;AAAA,MACtB,CAAC,SAAS,QAAQ;AAAA,IACpB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,YAAY,SAAS;AAAA,MACtB,CAAC,MAAM,WAAW,MAAM;AAAA,MACxB,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,MAAM,WAAW,UAAU;AAAA,MAC5B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,EACF;AACF;", "names": []}