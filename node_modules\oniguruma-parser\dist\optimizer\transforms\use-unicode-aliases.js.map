{"version": 3, "sources": ["../../../src/optimizer/transforms/use-unicode-aliases.ts"], "sourcesContent": ["import type {Visitor} from '../../traverser/traverse.js';\n\n/**\nUse Unicode property aliases.\n*/\nconst useUnicodeAliases: Visitor = {\n  CharacterSet({node}) {\n    if (node.kind !== 'property') {\n      return;\n    }\n    const alias = OnigUnicodeAliasMap.get(node.value);\n    if (alias) {\n      node.value = alias;\n    }\n  },\n};\n\n// Oniguruma doesn't include all Unicode property aliases; some are treated as POSIX class names\n// and are excluded (see `PosixClassNames`)\nconst OnigUnicodeAliasMap = /* @__PURE__ */ new Map([\n  // ## General category aliases\n  ['Other', 'C'],\n    ['Control', 'Cc'],\n    ['Format', 'Cf'],\n    ['Unassigned', 'Cn'],\n    ['Private_Use', 'Co'],\n    ['Surrogate', 'Cs'],\n  ['Letter', 'L'],\n    ['Cased_Letter', 'LC'],\n    ['Lowercase_Letter', 'Ll'],\n    ['Modifier_Letter', 'Lm'],\n    ['Other_Letter', 'Lo'],\n    ['Titlecase_Letter', 'Lt'],\n    ['Uppercase_Letter', 'Lu'],\n  ['Mark', 'M'],\n  ['Combining_Mark', 'M'],\n    ['Spacing_Mark', 'Mc'],\n    ['Enclosing_Mark', 'Me'],\n    ['Nonspacing_Mark', 'Mn'],\n  ['Number', 'N'],\n    ['Decimal_Number', 'Nd'],\n    ['Letter_Number', 'Nl'],\n    ['Other_Number', 'No'],\n  ['Punctuation', 'P'],\n  // `punct` is also a POSIX class name, but it's included in the Oniguruma property list since the\n  // POSIX class version uses a different value starting with Oniguruma 6.9.9\n  ['punct', 'P'],\n    ['Connector_Punctuation', 'Pc'],\n    ['Dash_Punctuation', 'Pd'],\n    ['Close_Punctuation', 'Pe'],\n    ['Final_Punctuation', 'Pf'],\n    ['Initial_Punctuation', 'Pi'],\n    ['Other_Punctuation', 'Po'],\n    ['Open_Punctuation', 'Ps'],\n  ['Symbol', 'S'],\n    ['Currency_Symbol', 'Sc'],\n    ['Modifier_Symbol', 'Sk'],\n    ['Math_Symbol', 'Sm'],\n    ['Other_Symbol', 'So'],\n  ['Separator', 'Z'],\n    ['Line_Separator', 'Zl'],\n    ['Paragraph_Separator', 'Zp'],\n    ['Space_Separator', 'Zs'],\n\n  // ## Binary property aliases\n  ['ASCII_Hex_Digit', 'AHex'],\n  ['Bidi_Control', 'Bidi_C'],\n  ['Case_Ignorable', 'CI'],\n  ['Changes_When_Casefolded', 'CWCF'],\n  ['Changes_When_Casemapped', 'CWCM'],\n  ['Changes_When_Lowercased', 'CWL'],\n  ['Changes_When_Titlecased', 'CWT'],\n  ['Changes_When_Uppercased', 'CWU'],\n  ['Default_Ignorable_Code_Point', 'DI'],\n  ['Deprecated', 'Dep'],\n  ['Diacritic', 'Dia'],\n  ['Emoji_Component', 'EComp'],\n  ['Emoji_Modifier', 'EMod'],\n  ['Emoji_Modifier_Base', 'EBase'],\n  ['Emoji_Presentation', 'EPres'],\n  ['Extended_Pictographic', 'ExtPict'],\n  ['Extender', 'Ext'],\n  ['Grapheme_Base', 'Gr_Base'],\n  ['Grapheme_Extend', 'Gr_Ext'],\n  ['Grapheme_Link', 'Gr_Link'],\n  ['Hex_Digit', 'Hex'],\n  ['IDS_Binary_Operator', 'IDSB'],\n  ['IDS_Trinary_Operator', 'IDST'],\n  ['IDS_Unary_Operator', 'IDSU'],\n  ['ID_Continue', 'IDC'],\n  ['ID_Start', 'IDS'],\n  ['Ideographic', 'Ideo'],\n  ['Join_Control', 'Join_C'],\n  ['Logical_Order_Exception', 'LOE'],\n  ['Noncharacter_Code_Point', 'NChar'],\n  ['Other_Alphabetic', 'OAlpha'],\n  ['Other_Default_Ignorable_Code_Point', 'ODI'],\n  ['Other_Grapheme_Extend', 'OGr_Ext'],\n  ['Other_ID_Continue', 'OIDC'],\n  ['Other_ID_Start', 'OIDS'],\n  ['Other_Lowercase', 'OLower'],\n  ['Other_Math', 'OMath'],\n  ['Other_Uppercase', 'OUpper'],\n  ['Pattern_Syntax', 'Pat_Syn'],\n  ['Pattern_White_Space', 'Pat_WS'],\n  ['Prepended_Concatenation_Mark', 'PCM'],\n  ['Quotation_Mark', 'QMark'],\n  ['Regional_Indicator', 'RI'],\n  ['Sentence_Terminal', 'STerm'],\n  ['Soft_Dotted', 'SD'],\n  ['Terminal_Punctuation', 'Term'],\n  ['Unified_Ideograph', 'UIdeo'],\n  ['Variation_Selector', 'VS'],\n  ['White_Space', 'WSpace'],\n  ['XID_Continue', 'XIDC'],\n  ['XID_Start', 'XIDS'],\n\n  // ## Script aliases\n  // TODO: Add script aliases\n]);\n\nexport {\n  useUnicodeAliases,\n};\n"], "mappings": "aAKA,MAAMA,EAA6B,CACjC,aAAa,CAAC,KAAAC,CAAI,EAAG,CACnB,GAAIA,EAAK,OAAS,WAChB,OAEF,MAAMC,EAAQC,EAAoB,IAAIF,EAAK,KAAK,EAC5CC,IACFD,EAAK,MAAQC,EAEjB,CACF,EAIMC,EAAsC,IAAI,IAAI,CAElD,CAAC,QAAS,GAAG,EACX,CAAC,UAAW,IAAI,EAChB,CAAC,SAAU,IAAI,EACf,CAAC,aAAc,IAAI,EACnB,CAAC,cAAe,IAAI,EACpB,CAAC,YAAa,IAAI,EACpB,CAAC,SAAU,GAAG,EACZ,CAAC,eAAgB,IAAI,EACrB,CAAC,mBAAoB,IAAI,EACzB,CAAC,kBAAmB,IAAI,EACxB,CAAC,eAAgB,IAAI,EACrB,CAAC,mBAAoB,IAAI,EACzB,CAAC,mBAAoB,IAAI,EAC3B,CAAC,OAAQ,GAAG,EACZ,CAAC,iBAAkB,GAAG,EACpB,CAAC,eAAgB,IAAI,EACrB,CAAC,iBAAkB,IAAI,EACvB,CAAC,kBAAmB,IAAI,EAC1B,CAAC,SAAU,GAAG,EACZ,CAAC,iBAAkB,IAAI,EACvB,CAAC,gBAAiB,IAAI,EACtB,CAAC,eAAgB,IAAI,EACvB,CAAC,cAAe,GAAG,EAGnB,CAAC,QAAS,GAAG,EACX,CAAC,wBAAyB,IAAI,EAC9B,CAAC,mBAAoB,IAAI,EACzB,CAAC,oBAAqB,IAAI,EAC1B,CAAC,oBAAqB,IAAI,EAC1B,CAAC,sBAAuB,IAAI,EAC5B,CAAC,oBAAqB,IAAI,EAC1B,CAAC,mBAAoB,IAAI,EAC3B,CAAC,SAAU,GAAG,EACZ,CAAC,kBAAmB,IAAI,EACxB,CAAC,kBAAmB,IAAI,EACxB,CAAC,cAAe,IAAI,EACpB,CAAC,eAAgB,IAAI,EACvB,CAAC,YAAa,GAAG,EACf,CAAC,iBAAkB,IAAI,EACvB,CAAC,sBAAuB,IAAI,EAC5B,CAAC,kBAAmB,IAAI,EAG1B,CAAC,kBAAmB,MAAM,EAC1B,CAAC,eAAgB,QAAQ,EACzB,CAAC,iBAAkB,IAAI,EACvB,CAAC,0BAA2B,MAAM,EAClC,CAAC,0BAA2B,MAAM,EAClC,CAAC,0BAA2B,KAAK,EACjC,CAAC,0BAA2B,KAAK,EACjC,CAAC,0BAA2B,KAAK,EACjC,CAAC,+BAAgC,IAAI,EACrC,CAAC,aAAc,KAAK,EACpB,CAAC,YAAa,KAAK,EACnB,CAAC,kBAAmB,OAAO,EAC3B,CAAC,iBAAkB,MAAM,EACzB,CAAC,sBAAuB,OAAO,EAC/B,CAAC,qBAAsB,OAAO,EAC9B,CAAC,wBAAyB,SAAS,EACnC,CAAC,WAAY,KAAK,EAClB,CAAC,gBAAiB,SAAS,EAC3B,CAAC,kBAAmB,QAAQ,EAC5B,CAAC,gBAAiB,SAAS,EAC3B,CAAC,YAAa,KAAK,EACnB,CAAC,sBAAuB,MAAM,EAC9B,CAAC,uBAAwB,MAAM,EAC/B,CAAC,qBAAsB,MAAM,EAC7B,CAAC,cAAe,KAAK,EACrB,CAAC,WAAY,KAAK,EAClB,CAAC,cAAe,MAAM,EACtB,CAAC,eAAgB,QAAQ,EACzB,CAAC,0BAA2B,KAAK,EACjC,CAAC,0BAA2B,OAAO,EACnC,CAAC,mBAAoB,QAAQ,EAC7B,CAAC,qCAAsC,KAAK,EAC5C,CAAC,wBAAyB,SAAS,EACnC,CAAC,oBAAqB,MAAM,EAC5B,CAAC,iBAAkB,MAAM,EACzB,CAAC,kBAAmB,QAAQ,EAC5B,CAAC,aAAc,OAAO,EACtB,CAAC,kBAAmB,QAAQ,EAC5B,CAAC,iBAAkB,SAAS,EAC5B,CAAC,sBAAuB,QAAQ,EAChC,CAAC,+BAAgC,KAAK,EACtC,CAAC,iBAAkB,OAAO,EAC1B,CAAC,qBAAsB,IAAI,EAC3B,CAAC,oBAAqB,OAAO,EAC7B,CAAC,cAAe,IAAI,EACpB,CAAC,uBAAwB,MAAM,EAC/B,CAAC,oBAAqB,OAAO,EAC7B,CAAC,qBAAsB,IAAI,EAC3B,CAAC,cAAe,QAAQ,EACxB,CAAC,eAAgB,MAAM,EACvB,CAAC,YAAa,MAAM,CAItB,CAAC,EAED,OACEH,KAAA", "names": ["useUnicodeAliases", "node", "alias", "OnigUnicodeAliasMap"]}