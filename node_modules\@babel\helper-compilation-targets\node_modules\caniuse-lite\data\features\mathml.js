module.exports={A:{A:{"2":"F A B 4B","8":"J D E"},B:{"2":"C K L G M N O","8":"P Q R S T U V W X Y Z a d e f g h","584":"i j k l m n o p b H"},C:{"1":"0 1 2 3 4 5 6 7 8 9 I q J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB","129":"5B pB 6B 7B"},D:{"1":"w","8":"0 1 2 3 4 5 6 7 8 9 I q J D E F A B C K L G M N O r s t u v x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h","584":"i j k l m n o p b H tB 8B 9B"},E:{"1":"A B C K L G vB mB nB wB FC GC xB yB zB 0B oB 1B HC","260":"I q J D E F AC uB BC CC DC EC"},F:{"2":"F","8":"0 1 2 3 4 5 6 7 8 9 G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB","584":"S T U V W X Y Z a","2052":"B C IC JC KC LC mB 2B MC nB"},G:{"1":"E OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B","8":"uB NC 3B"},H:{"8":"hC"},I:{"8":"pB I H iC jC kC lC 3B mC nC"},J:{"1":"A","8":"D"},K:{"8":"A B C c mB 2B nB"},L:{"8":"H"},M:{"1":"b"},N:{"2":"A B"},O:{"8":"oC"},P:{"8":"I pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"8":"wB"},R:{"8":"1C"},S:{"1":"2C"}},B:2,C:"MathML"};
