{"version": 3, "names": ["ChainFormatter", "Programmatic", "Config", "<PERSON><PERSON><PERSON>", "title", "type", "callerName", "filepath", "loc", "index", "envName", "optionsAndDescriptors", "opt", "content", "options", "overrides", "env", "pluginDescriptors", "plugins", "length", "map", "d", "descriptorToConfig", "presetDescriptors", "presets", "JSON", "stringify", "undefined", "name", "file", "request", "value", "toString", "slice", "ConfigPrinter", "_stack", "configure", "enabled", "push", "format", "config", "output", "configs", "gens<PERSON>", "all", "s", "join"], "sources": ["../../src/config/printer.ts"], "sourcesContent": ["import gensync from \"gensync\";\n\nimport type { Handler } from \"gensync\";\n\nimport type {\n  OptionsAndDescriptors,\n  UnloadedDescriptor,\n} from \"./config-descriptors\";\n\n// todo: Use flow enums when @babel/transform-flow-types supports it\nexport const ChainFormatter = {\n  Programmatic: 0,\n  Config: 1,\n};\n\ntype PrintableConfig = {\n  content: OptionsAndDescriptors;\n  type: typeof ChainFormatter[keyof typeof ChainFormatter];\n  callerName: string | undefined | null;\n  filepath: string | undefined | null;\n  index: number | undefined | null;\n  envName: string | undefined | null;\n};\n\nconst Formatter = {\n  title(\n    type: typeof ChainFormatter[keyof typeof ChainFormatter],\n    callerName?: string | null,\n    filepath?: string | null,\n  ): string {\n    let title = \"\";\n    if (type === ChainFormatter.Programmatic) {\n      title = \"programmatic options\";\n      if (callerName) {\n        title += \" from \" + callerName;\n      }\n    } else {\n      title = \"config \" + filepath;\n    }\n    return title;\n  },\n  loc(index?: number | null, envName?: string | null): string {\n    let loc = \"\";\n    if (index != null) {\n      loc += `.overrides[${index}]`;\n    }\n    if (envName != null) {\n      loc += `.env[\"${envName}\"]`;\n    }\n    return loc;\n  },\n\n  *optionsAndDescriptors(opt: OptionsAndDescriptors) {\n    const content = { ...opt.options };\n    // overrides and env will be printed as separated config items\n    delete content.overrides;\n    delete content.env;\n    // resolve to descriptors\n    const pluginDescriptors = [...(yield* opt.plugins())];\n    if (pluginDescriptors.length) {\n      content.plugins = pluginDescriptors.map(d => descriptorToConfig(d));\n    }\n    const presetDescriptors = [...(yield* opt.presets())];\n    if (presetDescriptors.length) {\n      content.presets = [...presetDescriptors].map(d => descriptorToConfig(d));\n    }\n    return JSON.stringify(content, undefined, 2);\n  },\n};\n\nfunction descriptorToConfig(\n  d: UnloadedDescriptor,\n): string | {} | Array<unknown> {\n  let name = d.file?.request;\n  if (name == null) {\n    if (typeof d.value === \"object\") {\n      name = d.value;\n    } else if (typeof d.value === \"function\") {\n      // If the unloaded descriptor is a function, i.e. `plugins: [ require(\"my-plugin\") ]`,\n      // we print the first 50 characters of the function source code and hopefully we can see\n      // `name: 'my-plugin'` in the source\n      name = `[Function: ${d.value.toString().slice(0, 50)} ... ]`;\n    }\n  }\n  if (name == null) {\n    name = \"[Unknown]\";\n  }\n  if (d.options === undefined) {\n    return name;\n  } else if (d.name == null) {\n    return [name, d.options];\n  } else {\n    return [name, d.options, d.name];\n  }\n}\n\nexport class ConfigPrinter {\n  _stack: Array<PrintableConfig> = [];\n  configure(\n    enabled: boolean,\n    type: typeof ChainFormatter[keyof typeof ChainFormatter],\n    {\n      callerName,\n      filepath,\n    }: {\n      callerName?: string;\n      filepath?: string;\n    },\n  ) {\n    if (!enabled) return () => {};\n    return (\n      content: OptionsAndDescriptors,\n      index?: number | null,\n      envName?: string | null,\n    ) => {\n      this._stack.push({\n        type,\n        callerName,\n        filepath,\n        content,\n        index,\n        envName,\n      });\n    };\n  }\n  static *format(config: PrintableConfig): Handler<string> {\n    let title = Formatter.title(\n      config.type,\n      config.callerName,\n      config.filepath,\n    );\n    const loc = Formatter.loc(config.index, config.envName);\n    if (loc) title += ` ${loc}`;\n    const content = yield* Formatter.optionsAndDescriptors(config.content);\n    return `${title}\\n${content}`;\n  }\n\n  *output(): Handler<string> {\n    if (this._stack.length === 0) return \"\";\n    const configs = yield* gensync.all(\n      this._stack.map(s => ConfigPrinter.format(s)),\n    );\n    return configs.join(\"\\n\\n\");\n  }\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAUO,MAAMA,cAAc,GAAG;EAC5BC,YAAY,EAAE,CADc;EAE5BC,MAAM,EAAE;AAFoB,CAAvB;;AAcP,MAAMC,SAAS,GAAG;EAChBC,KAAK,CACHC,IADG,EAEHC,UAFG,EAGHC,QAHG,EAIK;IACR,IAAIH,KAAK,GAAG,EAAZ;;IACA,IAAIC,IAAI,KAAKL,cAAc,CAACC,YAA5B,EAA0C;MACxCG,KAAK,GAAG,sBAAR;;MACA,IAAIE,UAAJ,EAAgB;QACdF,KAAK,IAAI,WAAWE,UAApB;MACD;IACF,CALD,MAKO;MACLF,KAAK,GAAG,YAAYG,QAApB;IACD;;IACD,OAAOH,KAAP;EACD,CAhBe;;EAiBhBI,GAAG,CAACC,KAAD,EAAwBC,OAAxB,EAAyD;IAC1D,IAAIF,GAAG,GAAG,EAAV;;IACA,IAAIC,KAAK,IAAI,IAAb,EAAmB;MACjBD,GAAG,IAAK,cAAaC,KAAM,GAA3B;IACD;;IACD,IAAIC,OAAO,IAAI,IAAf,EAAqB;MACnBF,GAAG,IAAK,SAAQE,OAAQ,IAAxB;IACD;;IACD,OAAOF,GAAP;EACD,CA1Be;;EA4BhB,CAACG,qBAAD,CAAuBC,GAAvB,EAAmD;IACjD,MAAMC,OAAO,qBAAQD,GAAG,CAACE,OAAZ,CAAb;IAEA,OAAOD,OAAO,CAACE,SAAf;IACA,OAAOF,OAAO,CAACG,GAAf;IAEA,MAAMC,iBAAiB,GAAG,CAAC,IAAI,OAAOL,GAAG,CAACM,OAAJ,EAAX,CAAD,CAA1B;;IACA,IAAID,iBAAiB,CAACE,MAAtB,EAA8B;MAC5BN,OAAO,CAACK,OAAR,GAAkBD,iBAAiB,CAACG,GAAlB,CAAsBC,CAAC,IAAIC,kBAAkB,CAACD,CAAD,CAA7C,CAAlB;IACD;;IACD,MAAME,iBAAiB,GAAG,CAAC,IAAI,OAAOX,GAAG,CAACY,OAAJ,EAAX,CAAD,CAA1B;;IACA,IAAID,iBAAiB,CAACJ,MAAtB,EAA8B;MAC5BN,OAAO,CAACW,OAAR,GAAkB,CAAC,GAAGD,iBAAJ,EAAuBH,GAAvB,CAA2BC,CAAC,IAAIC,kBAAkB,CAACD,CAAD,CAAlD,CAAlB;IACD;;IACD,OAAOI,IAAI,CAACC,SAAL,CAAeb,OAAf,EAAwBc,SAAxB,EAAmC,CAAnC,CAAP;EACD;;AA3Ce,CAAlB;;AA8CA,SAASL,kBAAT,CACED,CADF,EAEgC;EAAA;;EAC9B,IAAIO,IAAI,cAAGP,CAAC,CAACQ,IAAL,qBAAG,QAAQC,OAAnB;;EACA,IAAIF,IAAI,IAAI,IAAZ,EAAkB;IAChB,IAAI,OAAOP,CAAC,CAACU,KAAT,KAAmB,QAAvB,EAAiC;MAC/BH,IAAI,GAAGP,CAAC,CAACU,KAAT;IACD,CAFD,MAEO,IAAI,OAAOV,CAAC,CAACU,KAAT,KAAmB,UAAvB,EAAmC;MAIxCH,IAAI,GAAI,cAAaP,CAAC,CAACU,KAAF,CAAQC,QAAR,GAAmBC,KAAnB,CAAyB,CAAzB,EAA4B,EAA5B,CAAgC,QAArD;IACD;EACF;;EACD,IAAIL,IAAI,IAAI,IAAZ,EAAkB;IAChBA,IAAI,GAAG,WAAP;EACD;;EACD,IAAIP,CAAC,CAACP,OAAF,KAAca,SAAlB,EAA6B;IAC3B,OAAOC,IAAP;EACD,CAFD,MAEO,IAAIP,CAAC,CAACO,IAAF,IAAU,IAAd,EAAoB;IACzB,OAAO,CAACA,IAAD,EAAOP,CAAC,CAACP,OAAT,CAAP;EACD,CAFM,MAEA;IACL,OAAO,CAACc,IAAD,EAAOP,CAAC,CAACP,OAAT,EAAkBO,CAAC,CAACO,IAApB,CAAP;EACD;AACF;;AAEM,MAAMM,aAAN,CAAoB;EAAA;IAAA,KACzBC,MADyB,GACQ,EADR;EAAA;;EAEzBC,SAAS,CACPC,OADO,EAEPhC,IAFO,EAGP;IACEC,UADF;IAEEC;EAFF,CAHO,EAUP;IACA,IAAI,CAAC8B,OAAL,EAAc,OAAO,MAAM,CAAE,CAAf;IACd,OAAO,CACLxB,OADK,EAELJ,KAFK,EAGLC,OAHK,KAIF;MACH,KAAKyB,MAAL,CAAYG,IAAZ,CAAiB;QACfjC,IADe;QAEfC,UAFe;QAGfC,QAHe;QAIfM,OAJe;QAKfJ,KALe;QAMfC;MANe,CAAjB;IAQD,CAbD;EAcD;;EACa,QAAN6B,MAAM,CAACC,MAAD,EAA2C;IACvD,IAAIpC,KAAK,GAAGD,SAAS,CAACC,KAAV,CACVoC,MAAM,CAACnC,IADG,EAEVmC,MAAM,CAAClC,UAFG,EAGVkC,MAAM,CAACjC,QAHG,CAAZ;IAKA,MAAMC,GAAG,GAAGL,SAAS,CAACK,GAAV,CAAcgC,MAAM,CAAC/B,KAArB,EAA4B+B,MAAM,CAAC9B,OAAnC,CAAZ;IACA,IAAIF,GAAJ,EAASJ,KAAK,IAAK,IAAGI,GAAI,EAAjB;IACT,MAAMK,OAAO,GAAG,OAAOV,SAAS,CAACQ,qBAAV,CAAgC6B,MAAM,CAAC3B,OAAvC,CAAvB;IACA,OAAQ,GAAET,KAAM,KAAIS,OAAQ,EAA5B;EACD;;EAEM,CAAN4B,MAAM,GAAoB;IACzB,IAAI,KAAKN,MAAL,CAAYhB,MAAZ,KAAuB,CAA3B,EAA8B,OAAO,EAAP;IAC9B,MAAMuB,OAAO,GAAG,OAAOC,UAAA,CAAQC,GAAR,CACrB,KAAKT,MAAL,CAAYf,GAAZ,CAAgByB,CAAC,IAAIX,aAAa,CAACK,MAAd,CAAqBM,CAArB,CAArB,CADqB,CAAvB;IAGA,OAAOH,OAAO,CAACI,IAAR,CAAa,MAAb,CAAP;EACD;;AA/CwB"}