{"version": 3, "sources": ["../../../src/optimizer/transforms/remove-useless-flags.ts"], "sourcesContent": ["import type {DirectiveNode, GroupNode} from '../../parser/parse.js';\nimport type {FlagGroupModifiers} from '../../tokenizer/tokenize.js';\nimport type {Visitor} from '../../traverser/traverse.js';\n\n/**\nRemove flags (from top-level and modifiers) that have no effect.\n*/\nconst removeUselessFlags: Visitor = {\n  // TODO: Support removing additional flags\n\n  Flags({node}) {\n    // Effects of flag x are already applied during parsing\n    node.extended = false;\n    // Text segment handling uses grapheme mode by default, so it doesn't need to be set\n    if (node.textSegmentMode === 'grapheme') {\n      node.textSegmentMode = null;\n    }\n  },\n\n  Directive({node, remove}) {\n    if (node.kind !== 'flags') {\n      return;\n    }\n    removeFlagX(node);\n    if (removeEmptyFlagsObj(node)) {\n      remove();\n    }\n  },\n\n  Group({node}) {\n    if (!node.flags) {\n      return;\n    }\n    removeFlagX(node);\n    removeEmptyFlagsObj(node);\n  },\n};\n\nfunction removeEmptyFlagsObj(node: DirectiveNode | GroupNode): boolean {\n  const {flags} = node;\n  if (flags && !flags.enable && !flags.disable) {\n    delete node.flags;\n    return true;\n  }\n  return false;\n}\n\nfunction removeFlagX({flags}: DirectiveNode | GroupNode) {\n  if (!flags) {\n    throw new Error('Expected flags');\n  }\n  flags.enable && delete flags.enable.extended;\n  flags.disable && delete flags.disable.extended;\n  cleanupFlagsObj(flags);\n}\n\nfunction cleanupFlagsObj(flags: FlagGroupModifiers) {\n  flags.enable && !Object.keys(flags.enable).length && delete flags.enable;\n  flags.disable && !Object.keys(flags.disable).length && delete flags.disable;\n}\n\nexport {\n  removeUselessFlags,\n};\n"], "mappings": "aAOA,MAAMA,EAA8B,CAGlC,MAAM,CAAC,KAAAC,CAAI,EAAG,CAEZA,EAAK,SAAW,GAEZA,EAAK,kBAAoB,aAC3BA,EAAK,gBAAkB,KAE3B,EAEA,UAAU,CAAC,KAAAA,EAAM,OAAAC,CAAM,EAAG,CACpBD,EAAK,OAAS,UAGlBE,EAAYF,CAAI,EACZG,EAAoBH,CAAI,GAC1BC,EAAO,EAEX,EAEA,MAAM,CAAC,KAAAD,CAAI,EAAG,CACPA,EAAK,QAGVE,EAAYF,CAAI,EAChBG,EAAoBH,CAAI,EAC1B,CACF,EAEA,SAASG,EAAoBH,EAA0C,CACrE,KAAM,CAAC,MAAAI,CAAK,EAAIJ,EAChB,OAAII,GAAS,CAACA,EAAM,QAAU,CAACA,EAAM,SACnC,OAAOJ,EAAK,MACL,IAEF,EACT,CAEA,SAASE,EAAY,CAAC,MAAAE,CAAK,EAA8B,CACvD,GAAI,CAACA,EACH,MAAM,IAAI,MAAM,gBAAgB,EAElCA,EAAM,QAAU,OAAOA,EAAM,OAAO,SACpCA,EAAM,SAAW,OAAOA,EAAM,QAAQ,SACtCC,EAAgBD,CAAK,CACvB,CAEA,SAASC,EAAgBD,EAA2B,CAClDA,EAAM,QAAU,CAAC,OAAO,KAAKA,EAAM,MAAM,EAAE,QAAU,OAAOA,EAAM,OAClEA,EAAM,SAAW,CAAC,OAAO,KAAKA,EAAM,OAAO,EAAE,QAAU,OAAOA,EAAM,OACtE,CAEA,OACEL,KAAA", "names": ["removeUselessFlags", "node", "remove", "removeFlagX", "removeEmptyFlagsObj", "flags", "cleanupFlagsObj"]}