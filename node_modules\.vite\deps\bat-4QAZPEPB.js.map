{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/bat/bat.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/bat/bat.ts\nvar conf = {\n  comments: {\n    lineComment: \"REM\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  surroundingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*(::\\\\s*|REM\\\\s+)#region\"),\n      end: new RegExp(\"^\\\\s*(::\\\\s*|REM\\\\s+)#endregion\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".bat\",\n  brackets: [\n    { token: \"delimiter.bracket\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: /call|defined|echo|errorlevel|exist|for|goto|if|pause|set|shift|start|title|not|pushd|popd/,\n  symbols: /[=><!~?&|+\\-*\\/\\^;\\.,]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/^(\\s*)(rem(?:\\s.*|))$/, [\"\", \"comment\"]],\n      [/(\\@?)(@keywords)(?!\\w)/, [{ token: \"keyword\" }, { token: \"keyword.$2\" }]],\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/setlocal(?!\\w)/, \"keyword.tag-setlocal\"],\n      [/endlocal(?!\\w)/, \"keyword.tag-setlocal\"],\n      [/[a-zA-Z_]\\w*/, \"\"],\n      [/:\\w*/, \"metatag\"],\n      [/%[^%]+%/, \"variable\"],\n      [/%%[\\w]+(?!\\w)/, \"variable\"],\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"/, \"string\", '@string.\"'],\n      [/'/, \"string\", \"@string.'\"]\n    ],\n    string: [\n      [\n        /[^\\\\\"'%]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/%[\\w ]+%/, \"variable\"],\n      [/%%[\\w]+(?!\\w)/, \"variable\"],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/$/, \"string\", \"@popall\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,8BAA8B;AAAA,MAChD,KAAK,IAAI,OAAO,iCAAiC;AAAA,IACnD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,OAAO,qBAAqB,MAAM,KAAK,OAAO,IAAI;AAAA,IACpD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,yBAAyB,CAAC,IAAI,SAAS,CAAC;AAAA,MACzC,CAAC,0BAA0B,CAAC,EAAE,OAAO,UAAU,GAAG,EAAE,OAAO,aAAa,CAAC,CAAC;AAAA,MAC1E,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,kBAAkB,sBAAsB;AAAA,MACzC,CAAC,kBAAkB,sBAAsB;AAAA,MACzC,CAAC,gBAAgB,EAAE;AAAA,MACnB,CAAC,QAAQ,SAAS;AAAA,MAClB,CAAC,WAAW,UAAU;AAAA,MACtB,CAAC,iBAAiB,UAAU;AAAA,MAC5B,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA,MACxB,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,KAAK,UAAU,WAAW;AAAA,MAC3B,CAAC,KAAK,UAAU,WAAW;AAAA,IAC7B;AAAA,IACA,QAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,YAAY,UAAU;AAAA,MACvB,CAAC,iBAAiB,UAAU;AAAA,MAC5B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,EACF;AACF;", "names": []}