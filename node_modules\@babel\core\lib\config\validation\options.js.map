{"version": 3, "names": ["ROOT_VALIDATORS", "cwd", "assertString", "root", "rootMode", "assertRootMode", "configFile", "assertConfigFileSearch", "caller", "assertCallerMetadata", "filename", "filenameRelative", "code", "assertBoolean", "ast", "cloneInputAst", "envName", "BABELRC_VALIDATORS", "babelrc", "babelrcRoots", "assertBabelrcSearch", "NONPRESET_VALIDATORS", "extends", "ignore", "assertIgnoreList", "only", "targets", "assertTargets", "browserslistConfigFile", "browserslistEnv", "COMMON_VALIDATORS", "inputSourceMap", "assertInputSourceMap", "presets", "assertPluginList", "plugins", "passPerPreset", "assumptions", "assertAssumptions", "env", "assertEnvSet", "overrides", "assertOverridesList", "test", "assertConfigApplicableTest", "include", "exclude", "retainLines", "comments", "shouldPrintComment", "assertFunction", "compact", "assertCompact", "minified", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "sourceType", "assertSourceType", "wrapPluginVisitorMethod", "highlightCode", "sourceMaps", "assertSourceMaps", "sourceMap", "sourceFileName", "sourceRoot", "parserOpts", "assertObject", "generatorOpts", "Object", "assign", "getModuleId", "moduleRoot", "moduleIds", "moduleId", "knownAssumptions", "assumptionsNames", "Set", "getSource", "loc", "type", "source", "parent", "validate", "opts", "validateNested", "error", "config<PERSON><PERSON>r", "ConfigError", "message", "assertNoDuplicateSourcemap", "keys", "for<PERSON>ach", "key", "optLoc", "name", "Error", "msg", "validator", "throwUnknownError", "removed", "version", "unknownOptErr", "has", "obj", "prototype", "hasOwnProperty", "call", "value", "access", "envLoc", "arr", "assertArray", "index", "item", "entries", "objLoc", "overridesLoc", "checkNoUnwrappedItemOptionPairs", "items", "e", "lastItem", "thisItem", "file", "options", "undefined", "request", "JSON", "stringify"], "sources": ["../../../src/config/validation/options.ts"], "sourcesContent": ["import type { InputTargets, Targets } from \"@babel/helper-compilation-targets\";\n\nimport type { ConfigItem } from \"../item\";\nimport type Plugin from \"../plugin\";\n\nimport removed from \"./removed\";\nimport {\n  msg,\n  access,\n  assertString,\n  assertBoolean,\n  assertObject,\n  assertArray,\n  assertCallerMetadata,\n  assertInputSourceMap,\n  assertIgnoreList,\n  assertPluginList,\n  assertConfigApplicableTest,\n  assertConfigFileSearch,\n  assertBabelrcSearch,\n  assertFunction,\n  assertRootMode,\n  assertSourceMaps,\n  assertCompact,\n  assertSourceType,\n  assertTargets,\n  assertAssumptions,\n} from \"./option-assertions\";\nimport type { ValidatorSet, Validator, OptionPath } from \"./option-assertions\";\nimport type { UnloadedDescriptor } from \"../config-descriptors\";\nimport type { ParserOptions } from \"@babel/parser\";\nimport type { GeneratorOptions } from \"@babel/generator\";\nimport ConfigError from \"../../errors/config-error\";\n\nconst ROOT_VALIDATORS: ValidatorSet = {\n  cwd: assertString as Validator<ValidatedOptions[\"cwd\"]>,\n  root: assertString as Validator<ValidatedOptions[\"root\"]>,\n  rootMode: assertRootMode as Validator<ValidatedOptions[\"rootMode\"]>,\n  configFile: assertConfigFileSearch as Validator<\n    ValidatedOptions[\"configFile\"]\n  >,\n\n  caller: assertCallerMetadata as Validator<ValidatedOptions[\"caller\"]>,\n  filename: assertString as Validator<ValidatedOptions[\"filename\"]>,\n  filenameRelative: assertString as Validator<\n    ValidatedOptions[\"filenameRelative\"]\n  >,\n  code: assertBoolean as Validator<ValidatedOptions[\"code\"]>,\n  ast: assertBoolean as Validator<ValidatedOptions[\"ast\"]>,\n\n  cloneInputAst: assertBoolean as Validator<ValidatedOptions[\"cloneInputAst\"]>,\n\n  envName: assertString as Validator<ValidatedOptions[\"envName\"]>,\n};\n\nconst BABELRC_VALIDATORS: ValidatorSet = {\n  babelrc: assertBoolean as Validator<ValidatedOptions[\"babelrc\"]>,\n  babelrcRoots: assertBabelrcSearch as Validator<\n    ValidatedOptions[\"babelrcRoots\"]\n  >,\n};\n\nconst NONPRESET_VALIDATORS: ValidatorSet = {\n  extends: assertString as Validator<ValidatedOptions[\"extends\"]>,\n  ignore: assertIgnoreList as Validator<ValidatedOptions[\"ignore\"]>,\n  only: assertIgnoreList as Validator<ValidatedOptions[\"only\"]>,\n\n  targets: assertTargets as Validator<ValidatedOptions[\"targets\"]>,\n  browserslistConfigFile: assertConfigFileSearch as Validator<\n    ValidatedOptions[\"browserslistConfigFile\"]\n  >,\n  browserslistEnv: assertString as Validator<\n    ValidatedOptions[\"browserslistEnv\"]\n  >,\n};\n\nconst COMMON_VALIDATORS: ValidatorSet = {\n  // TODO: Should 'inputSourceMap' be moved to be a root-only option?\n  // We may want a boolean-only version to be a common option, with the\n  // object only allowed as a root config argument.\n  inputSourceMap: assertInputSourceMap as Validator<\n    ValidatedOptions[\"inputSourceMap\"]\n  >,\n  presets: assertPluginList as Validator<ValidatedOptions[\"presets\"]>,\n  plugins: assertPluginList as Validator<ValidatedOptions[\"plugins\"]>,\n  passPerPreset: assertBoolean as Validator<ValidatedOptions[\"passPerPreset\"]>,\n  assumptions: assertAssumptions as Validator<ValidatedOptions[\"assumptions\"]>,\n\n  env: assertEnvSet as Validator<ValidatedOptions[\"env\"]>,\n  overrides: assertOverridesList as Validator<ValidatedOptions[\"overrides\"]>,\n\n  // We could limit these to 'overrides' blocks, but it's not clear why we'd\n  // bother, when the ability to limit a config to a specific set of files\n  // is a fairly general useful feature.\n  test: assertConfigApplicableTest as Validator<ValidatedOptions[\"test\"]>,\n  include: assertConfigApplicableTest as Validator<ValidatedOptions[\"include\"]>,\n  exclude: assertConfigApplicableTest as Validator<ValidatedOptions[\"exclude\"]>,\n\n  retainLines: assertBoolean as Validator<ValidatedOptions[\"retainLines\"]>,\n  comments: assertBoolean as Validator<ValidatedOptions[\"comments\"]>,\n  shouldPrintComment: assertFunction as Validator<\n    ValidatedOptions[\"shouldPrintComment\"]\n  >,\n  compact: assertCompact as Validator<ValidatedOptions[\"compact\"]>,\n  minified: assertBoolean as Validator<ValidatedOptions[\"minified\"]>,\n  auxiliaryCommentBefore: assertString as Validator<\n    ValidatedOptions[\"auxiliaryCommentBefore\"]\n  >,\n  auxiliaryCommentAfter: assertString as Validator<\n    ValidatedOptions[\"auxiliaryCommentAfter\"]\n  >,\n  sourceType: assertSourceType as Validator<ValidatedOptions[\"sourceType\"]>,\n  wrapPluginVisitorMethod: assertFunction as Validator<\n    ValidatedOptions[\"wrapPluginVisitorMethod\"]\n  >,\n  highlightCode: assertBoolean as Validator<ValidatedOptions[\"highlightCode\"]>,\n  sourceMaps: assertSourceMaps as Validator<ValidatedOptions[\"sourceMaps\"]>,\n  sourceMap: assertSourceMaps as Validator<ValidatedOptions[\"sourceMap\"]>,\n  sourceFileName: assertString as Validator<ValidatedOptions[\"sourceFileName\"]>,\n  sourceRoot: assertString as Validator<ValidatedOptions[\"sourceRoot\"]>,\n  parserOpts: assertObject as Validator<ValidatedOptions[\"parserOpts\"]>,\n  generatorOpts: assertObject as Validator<ValidatedOptions[\"generatorOpts\"]>,\n};\nif (!process.env.BABEL_8_BREAKING) {\n  Object.assign(COMMON_VALIDATORS, {\n    getModuleId: assertFunction,\n    moduleRoot: assertString,\n    moduleIds: assertBoolean,\n    moduleId: assertString,\n  });\n}\n\nexport type InputOptions = ValidatedOptions;\n\nexport type ValidatedOptions = {\n  cwd?: string;\n  filename?: string;\n  filenameRelative?: string;\n  babelrc?: boolean;\n  babelrcRoots?: BabelrcSearch;\n  configFile?: ConfigFileSearch;\n  root?: string;\n  rootMode?: RootMode;\n  code?: boolean;\n  ast?: boolean;\n  cloneInputAst?: boolean;\n  inputSourceMap?: RootInputSourceMapOption;\n  envName?: string;\n  caller?: CallerMetadata;\n  extends?: string;\n  env?: EnvSet<ValidatedOptions>;\n  ignore?: IgnoreList;\n  only?: IgnoreList;\n  overrides?: OverridesList;\n  // Generally verify if a given config object should be applied to the given file.\n  test?: ConfigApplicableTest;\n  include?: ConfigApplicableTest;\n  exclude?: ConfigApplicableTest;\n  presets?: PluginList;\n  plugins?: PluginList;\n  passPerPreset?: boolean;\n  assumptions?: {\n    [name: string]: boolean;\n  };\n  // browserslists-related options\n  targets?: TargetsListOrObject;\n  browserslistConfigFile?: ConfigFileSearch;\n  browserslistEnv?: string;\n  // Options for @babel/generator\n  retainLines?: boolean;\n  comments?: boolean;\n  shouldPrintComment?: Function;\n  compact?: CompactOption;\n  minified?: boolean;\n  auxiliaryCommentBefore?: string;\n  auxiliaryCommentAfter?: string;\n  // Parser\n  sourceType?: SourceTypeOption;\n  wrapPluginVisitorMethod?: Function;\n  highlightCode?: boolean;\n  // Sourcemap generation options.\n  sourceMaps?: SourceMapsOption;\n  sourceMap?: SourceMapsOption;\n  sourceFileName?: string;\n  sourceRoot?: string;\n  // Deprecate top level parserOpts\n  parserOpts?: ParserOptions;\n  // Deprecate top level generatorOpts\n  generatorOpts?: GeneratorOptions;\n};\n\nexport type NormalizedOptions = {\n  readonly targets: Targets;\n} & Omit<ValidatedOptions, \"targets\">;\n\nexport type CallerMetadata = {\n  // If 'caller' is specified, require that the name is given for debugging\n  // messages.\n  name: string;\n};\nexport type EnvSet<T> = {\n  [x: string]: T;\n};\nexport type IgnoreItem =\n  | string\n  | RegExp\n  | ((\n      path: string | undefined,\n      context: { dirname: string; caller: CallerMetadata; envName: string },\n    ) => unknown);\nexport type IgnoreList = ReadonlyArray<IgnoreItem>;\n\nexport type PluginOptions = object | void | false;\nexport type PluginTarget = string | object | Function;\nexport type PluginItem =\n  | ConfigItem\n  | Plugin\n  | PluginTarget\n  | [PluginTarget, PluginOptions]\n  | [PluginTarget, PluginOptions, string | void];\nexport type PluginList = ReadonlyArray<PluginItem>;\n\nexport type OverridesList = Array<ValidatedOptions>;\nexport type ConfigApplicableTest = IgnoreItem | Array<IgnoreItem>;\n\nexport type ConfigFileSearch = string | boolean;\nexport type BabelrcSearch = boolean | IgnoreItem | IgnoreList;\nexport type SourceMapsOption = boolean | \"inline\" | \"both\";\nexport type SourceTypeOption = \"module\" | \"script\" | \"unambiguous\";\nexport type CompactOption = boolean | \"auto\";\nexport type RootInputSourceMapOption = {} | boolean;\nexport type RootMode = \"root\" | \"upward\" | \"upward-optional\";\n\nexport type TargetsListOrObject =\n  | Targets\n  | InputTargets\n  | InputTargets[\"browsers\"];\n\nexport type OptionsSource =\n  | \"arguments\"\n  | \"configfile\"\n  | \"babelrcfile\"\n  | \"extendsfile\"\n  | \"preset\"\n  | \"plugin\";\n\nexport type RootPath = Readonly<{\n  type: \"root\";\n  source: OptionsSource;\n}>;\n\ntype OverridesPath = Readonly<{\n  type: \"overrides\";\n  index: number;\n  parent: RootPath;\n}>;\n\ntype EnvPath = Readonly<{\n  type: \"env\";\n  name: string;\n  parent: RootPath | OverridesPath;\n}>;\n\nexport type NestingPath = RootPath | OverridesPath | EnvPath;\n\nconst knownAssumptions = [\n  \"arrayLikeIsIterable\",\n  \"constantReexports\",\n  \"constantSuper\",\n  \"enumerableModuleMeta\",\n  \"ignoreFunctionLength\",\n  \"ignoreToPrimitiveHint\",\n  \"iterableIsArray\",\n  \"mutableTemplateObject\",\n  \"noClassCalls\",\n  \"noDocumentAll\",\n  \"noIncompleteNsImportDetection\",\n  \"noNewArrows\",\n  \"objectRestNoSymbols\",\n  \"privateFieldsAsProperties\",\n  \"pureGetters\",\n  \"setClassMethods\",\n  \"setComputedProperties\",\n  \"setPublicClassFields\",\n  \"setSpreadProperties\",\n  \"skipForOfIteratorClosing\",\n  \"superIsCallableConstructor\",\n] as const;\nexport type AssumptionName = typeof knownAssumptions[number];\nexport const assumptionsNames = new Set(knownAssumptions);\n\nfunction getSource(loc: NestingPath): OptionsSource {\n  return loc.type === \"root\" ? loc.source : getSource(loc.parent);\n}\n\nexport function validate(\n  type: OptionsSource,\n  opts: {},\n  filename?: string,\n): ValidatedOptions {\n  try {\n    return validateNested(\n      {\n        type: \"root\",\n        source: type,\n      },\n      opts,\n    );\n  } catch (error) {\n    const configError = new ConfigError(error.message, filename);\n    // @ts-expect-error TODO: .code is not defined on ConfigError or Error\n    if (error.code) configError.code = error.code;\n    throw configError;\n  }\n}\n\nfunction validateNested(loc: NestingPath, opts: { [key: string]: unknown }) {\n  const type = getSource(loc);\n\n  assertNoDuplicateSourcemap(opts);\n\n  Object.keys(opts).forEach((key: string) => {\n    const optLoc = {\n      type: \"option\",\n      name: key,\n      parent: loc,\n    } as const;\n\n    if (type === \"preset\" && NONPRESET_VALIDATORS[key]) {\n      throw new Error(`${msg(optLoc)} is not allowed in preset options`);\n    }\n    if (type !== \"arguments\" && ROOT_VALIDATORS[key]) {\n      throw new Error(\n        `${msg(optLoc)} is only allowed in root programmatic options`,\n      );\n    }\n    if (\n      type !== \"arguments\" &&\n      type !== \"configfile\" &&\n      BABELRC_VALIDATORS[key]\n    ) {\n      if (type === \"babelrcfile\" || type === \"extendsfile\") {\n        throw new Error(\n          `${msg(\n            optLoc,\n          )} is not allowed in .babelrc or \"extends\"ed files, only in root programmatic options, ` +\n            `or babel.config.js/config file options`,\n        );\n      }\n\n      throw new Error(\n        `${msg(\n          optLoc,\n        )} is only allowed in root programmatic options, or babel.config.js/config file options`,\n      );\n    }\n\n    const validator =\n      COMMON_VALIDATORS[key] ||\n      NONPRESET_VALIDATORS[key] ||\n      BABELRC_VALIDATORS[key] ||\n      ROOT_VALIDATORS[key] ||\n      (throwUnknownError as Validator<void>);\n\n    validator(optLoc, opts[key]);\n  });\n\n  return opts;\n}\n\nfunction throwUnknownError(loc: OptionPath) {\n  const key = loc.name;\n\n  if (removed[key]) {\n    const { message, version = 5 } = removed[key];\n\n    throw new Error(\n      `Using removed Babel ${version} option: ${msg(loc)} - ${message}`,\n    );\n  } else {\n    // eslint-disable-next-line max-len\n    const unknownOptErr = new Error(\n      `Unknown option: ${msg(\n        loc,\n      )}. Check out https://babeljs.io/docs/en/babel-core/#options for more information about options.`,\n    );\n    // @ts-expect-error todo(flow->ts): consider creating something like BabelConfigError with code field in it\n    unknownOptErr.code = \"BABEL_UNKNOWN_OPTION\";\n\n    throw unknownOptErr;\n  }\n}\n\nfunction has(obj: {}, key: string) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction assertNoDuplicateSourcemap(opts: {}): void {\n  if (has(opts, \"sourceMap\") && has(opts, \"sourceMaps\")) {\n    throw new Error(\".sourceMap is an alias for .sourceMaps, cannot use both\");\n  }\n}\n\nfunction assertEnvSet(\n  loc: OptionPath,\n  value: unknown,\n): void | EnvSet<ValidatedOptions> {\n  if (loc.parent.type === \"env\") {\n    throw new Error(`${msg(loc)} is not allowed inside of another .env block`);\n  }\n  const parent: RootPath | OverridesPath = loc.parent;\n\n  const obj = assertObject(loc, value);\n  if (obj) {\n    // Validate but don't copy the .env object in order to preserve\n    // object identity for use during config chain processing.\n    for (const envName of Object.keys(obj)) {\n      const env = assertObject(access(loc, envName), obj[envName]);\n      if (!env) continue;\n\n      const envLoc = {\n        type: \"env\",\n        name: envName,\n        parent,\n      } as const;\n      validateNested(envLoc, env);\n    }\n  }\n  return obj;\n}\n\nfunction assertOverridesList(\n  loc: OptionPath,\n  value: unknown[],\n): undefined | OverridesList {\n  if (loc.parent.type === \"env\") {\n    throw new Error(`${msg(loc)} is not allowed inside an .env block`);\n  }\n  if (loc.parent.type === \"overrides\") {\n    throw new Error(`${msg(loc)} is not allowed inside an .overrides block`);\n  }\n  const parent: RootPath = loc.parent;\n\n  const arr = assertArray(loc, value);\n  if (arr) {\n    for (const [index, item] of arr.entries()) {\n      const objLoc = access(loc, index);\n      const env = assertObject(objLoc, item);\n      if (!env) throw new Error(`${msg(objLoc)} must be an object`);\n\n      const overridesLoc = {\n        type: \"overrides\",\n        index,\n        parent,\n      } as const;\n      validateNested(overridesLoc, env);\n    }\n  }\n  return arr as OverridesList;\n}\n\nexport function checkNoUnwrappedItemOptionPairs(\n  items: Array<UnloadedDescriptor>,\n  index: number,\n  type: \"plugin\" | \"preset\",\n  e: Error,\n): void {\n  if (index === 0) return;\n\n  const lastItem = items[index - 1];\n  const thisItem = items[index];\n\n  if (\n    lastItem.file &&\n    lastItem.options === undefined &&\n    typeof thisItem.value === \"object\"\n  ) {\n    e.message +=\n      `\\n- Maybe you meant to use\\n` +\n      `\"${type}s\": [\\n  [\"${lastItem.file.request}\", ${JSON.stringify(\n        thisItem.value,\n        undefined,\n        2,\n      )}]\\n]\\n` +\n      `To be a valid ${type}, its name and options should be wrapped in a pair of brackets`;\n  }\n}\n"], "mappings": ";;;;;;;;;AAKA;;AACA;;AA0BA;;AAEA,MAAMA,eAA6B,GAAG;EACpCC,GAAG,EAAEC,8BAD+B;EAEpCC,IAAI,EAAED,8BAF8B;EAGpCE,QAAQ,EAAEC,gCAH0B;EAIpCC,UAAU,EAAEC,wCAJwB;EAQpCC,MAAM,EAAEC,sCAR4B;EASpCC,QAAQ,EAAER,8BAT0B;EAUpCS,gBAAgB,EAAET,8BAVkB;EAapCU,IAAI,EAAEC,+BAb8B;EAcpCC,GAAG,EAAED,+BAd+B;EAgBpCE,aAAa,EAAEF,+BAhBqB;EAkBpCG,OAAO,EAAEd;AAlB2B,CAAtC;AAqBA,MAAMe,kBAAgC,GAAG;EACvCC,OAAO,EAAEL,+BAD8B;EAEvCM,YAAY,EAAEC;AAFyB,CAAzC;AAOA,MAAMC,oBAAkC,GAAG;EACzCC,OAAO,EAAEpB,8BADgC;EAEzCqB,MAAM,EAAEC,kCAFiC;EAGzCC,IAAI,EAAED,kCAHmC;EAKzCE,OAAO,EAAEC,+BALgC;EAMzCC,sBAAsB,EAAErB,wCANiB;EASzCsB,eAAe,EAAE3B;AATwB,CAA3C;AAcA,MAAM4B,iBAA+B,GAAG;EAItCC,cAAc,EAAEC,sCAJsB;EAOtCC,OAAO,EAAEC,kCAP6B;EAQtCC,OAAO,EAAED,kCAR6B;EAStCE,aAAa,EAAEvB,+BATuB;EAUtCwB,WAAW,EAAEC,mCAVyB;EAYtCC,GAAG,EAAEC,YAZiC;EAatCC,SAAS,EAAEC,mBAb2B;EAkBtCC,IAAI,EAAEC,4CAlBgC;EAmBtCC,OAAO,EAAED,4CAnB6B;EAoBtCE,OAAO,EAAEF,4CApB6B;EAsBtCG,WAAW,EAAElC,+BAtByB;EAuBtCmC,QAAQ,EAAEnC,+BAvB4B;EAwBtCoC,kBAAkB,EAAEC,gCAxBkB;EA2BtCC,OAAO,EAAEC,+BA3B6B;EA4BtCC,QAAQ,EAAExC,+BA5B4B;EA6BtCyC,sBAAsB,EAAEpD,8BA7Bc;EAgCtCqD,qBAAqB,EAAErD,8BAhCe;EAmCtCsD,UAAU,EAAEC,kCAnC0B;EAoCtCC,uBAAuB,EAAER,gCApCa;EAuCtCS,aAAa,EAAE9C,+BAvCuB;EAwCtC+C,UAAU,EAAEC,kCAxC0B;EAyCtCC,SAAS,EAAED,kCAzC2B;EA0CtCE,cAAc,EAAE7D,8BA1CsB;EA2CtC8D,UAAU,EAAE9D,8BA3C0B;EA4CtC+D,UAAU,EAAEC,8BA5C0B;EA6CtCC,aAAa,EAAED;AA7CuB,CAAxC;AA+CmC;EACjCE,MAAM,CAACC,MAAP,CAAcvC,iBAAd,EAAiC;IAC/BwC,WAAW,EAAEpB,gCADkB;IAE/BqB,UAAU,EAAErE,8BAFmB;IAG/BsE,SAAS,EAAE3D,+BAHoB;IAI/B4D,QAAQ,EAAEvE;EAJqB,CAAjC;AAMD;AAuID,MAAMwE,gBAAgB,GAAG,CACvB,qBADuB,EAEvB,mBAFuB,EAGvB,eAHuB,EAIvB,sBAJuB,EAKvB,sBALuB,EAMvB,uBANuB,EAOvB,iBAPuB,EAQvB,uBARuB,EASvB,cATuB,EAUvB,eAVuB,EAWvB,+BAXuB,EAYvB,aAZuB,EAavB,qBAbuB,EAcvB,2BAduB,EAevB,aAfuB,EAgBvB,iBAhBuB,EAiBvB,uBAjBuB,EAkBvB,sBAlBuB,EAmBvB,qBAnBuB,EAoBvB,0BApBuB,EAqBvB,4BArBuB,CAAzB;AAwBO,MAAMC,gBAAgB,GAAG,IAAIC,GAAJ,CAAQF,gBAAR,CAAzB;;;AAEP,SAASG,SAAT,CAAmBC,GAAnB,EAAoD;EAClD,OAAOA,GAAG,CAACC,IAAJ,KAAa,MAAb,GAAsBD,GAAG,CAACE,MAA1B,GAAmCH,SAAS,CAACC,GAAG,CAACG,MAAL,CAAnD;AACD;;AAEM,SAASC,QAAT,CACLH,IADK,EAELI,IAFK,EAGLzE,QAHK,EAIa;EAClB,IAAI;IACF,OAAO0E,cAAc,CACnB;MACEL,IAAI,EAAE,MADR;MAEEC,MAAM,EAAED;IAFV,CADmB,EAKnBI,IALmB,CAArB;EAOD,CARD,CAQE,OAAOE,KAAP,EAAc;IACd,MAAMC,WAAW,GAAG,IAAIC,oBAAJ,CAAgBF,KAAK,CAACG,OAAtB,EAA+B9E,QAA/B,CAApB;IAEA,IAAI2E,KAAK,CAACzE,IAAV,EAAgB0E,WAAW,CAAC1E,IAAZ,GAAmByE,KAAK,CAACzE,IAAzB;IAChB,MAAM0E,WAAN;EACD;AACF;;AAED,SAASF,cAAT,CAAwBN,GAAxB,EAA0CK,IAA1C,EAA4E;EAC1E,MAAMJ,IAAI,GAAGF,SAAS,CAACC,GAAD,CAAtB;EAEAW,0BAA0B,CAACN,IAAD,CAA1B;EAEAf,MAAM,CAACsB,IAAP,CAAYP,IAAZ,EAAkBQ,OAAlB,CAA2BC,GAAD,IAAiB;IACzC,MAAMC,MAAM,GAAG;MACbd,IAAI,EAAE,QADO;MAEbe,IAAI,EAAEF,GAFO;MAGbX,MAAM,EAAEH;IAHK,CAAf;;IAMA,IAAIC,IAAI,KAAK,QAAT,IAAqB1D,oBAAoB,CAACuE,GAAD,CAA7C,EAAoD;MAClD,MAAM,IAAIG,KAAJ,CAAW,GAAE,IAAAC,qBAAA,EAAIH,MAAJ,CAAY,mCAAzB,CAAN;IACD;;IACD,IAAId,IAAI,KAAK,WAAT,IAAwB/E,eAAe,CAAC4F,GAAD,CAA3C,EAAkD;MAChD,MAAM,IAAIG,KAAJ,CACH,GAAE,IAAAC,qBAAA,EAAIH,MAAJ,CAAY,+CADX,CAAN;IAGD;;IACD,IACEd,IAAI,KAAK,WAAT,IACAA,IAAI,KAAK,YADT,IAEA9D,kBAAkB,CAAC2E,GAAD,CAHpB,EAIE;MACA,IAAIb,IAAI,KAAK,aAAT,IAA0BA,IAAI,KAAK,aAAvC,EAAsD;QACpD,MAAM,IAAIgB,KAAJ,CACH,GAAE,IAAAC,qBAAA,EACDH,MADC,CAED,uFAFF,GAGG,wCAJC,CAAN;MAMD;;MAED,MAAM,IAAIE,KAAJ,CACH,GAAE,IAAAC,qBAAA,EACDH,MADC,CAED,uFAHE,CAAN;IAKD;;IAED,MAAMI,SAAS,GACbnE,iBAAiB,CAAC8D,GAAD,CAAjB,IACAvE,oBAAoB,CAACuE,GAAD,CADpB,IAEA3E,kBAAkB,CAAC2E,GAAD,CAFlB,IAGA5F,eAAe,CAAC4F,GAAD,CAHf,IAICM,iBALH;IAOAD,SAAS,CAACJ,MAAD,EAASV,IAAI,CAACS,GAAD,CAAb,CAAT;EACD,CA5CD;EA8CA,OAAOT,IAAP;AACD;;AAED,SAASe,iBAAT,CAA2BpB,GAA3B,EAA4C;EAC1C,MAAMc,GAAG,GAAGd,GAAG,CAACgB,IAAhB;;EAEA,IAAIK,gBAAA,CAAQP,GAAR,CAAJ,EAAkB;IAChB,MAAM;MAAEJ,OAAF;MAAWY,OAAO,GAAG;IAArB,IAA2BD,gBAAA,CAAQP,GAAR,CAAjC;IAEA,MAAM,IAAIG,KAAJ,CACH,uBAAsBK,OAAQ,YAAW,IAAAJ,qBAAA,EAAIlB,GAAJ,CAAS,MAAKU,OAAQ,EAD5D,CAAN;EAGD,CAND,MAMO;IAEL,MAAMa,aAAa,GAAG,IAAIN,KAAJ,CACnB,mBAAkB,IAAAC,qBAAA,EACjBlB,GADiB,CAEjB,gGAHkB,CAAtB;IAMAuB,aAAa,CAACzF,IAAd,GAAqB,sBAArB;IAEA,MAAMyF,aAAN;EACD;AACF;;AAED,SAASC,GAAT,CAAaC,GAAb,EAAsBX,GAAtB,EAAmC;EACjC,OAAOxB,MAAM,CAACoC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCH,GAArC,EAA0CX,GAA1C,CAAP;AACD;;AAED,SAASH,0BAAT,CAAoCN,IAApC,EAAoD;EAClD,IAAImB,GAAG,CAACnB,IAAD,EAAO,WAAP,CAAH,IAA0BmB,GAAG,CAACnB,IAAD,EAAO,YAAP,CAAjC,EAAuD;IACrD,MAAM,IAAIY,KAAJ,CAAU,yDAAV,CAAN;EACD;AACF;;AAED,SAASvD,YAAT,CACEsC,GADF,EAEE6B,KAFF,EAGmC;EACjC,IAAI7B,GAAG,CAACG,MAAJ,CAAWF,IAAX,KAAoB,KAAxB,EAA+B;IAC7B,MAAM,IAAIgB,KAAJ,CAAW,GAAE,IAAAC,qBAAA,EAAIlB,GAAJ,CAAS,8CAAtB,CAAN;EACD;;EACD,MAAMG,MAAgC,GAAGH,GAAG,CAACG,MAA7C;EAEA,MAAMsB,GAAG,GAAG,IAAArC,8BAAA,EAAaY,GAAb,EAAkB6B,KAAlB,CAAZ;;EACA,IAAIJ,GAAJ,EAAS;IAGP,KAAK,MAAMvF,OAAX,IAAsBoD,MAAM,CAACsB,IAAP,CAAYa,GAAZ,CAAtB,EAAwC;MACtC,MAAMhE,GAAG,GAAG,IAAA2B,8BAAA,EAAa,IAAA0C,wBAAA,EAAO9B,GAAP,EAAY9D,OAAZ,CAAb,EAAmCuF,GAAG,CAACvF,OAAD,CAAtC,CAAZ;MACA,IAAI,CAACuB,GAAL,EAAU;MAEV,MAAMsE,MAAM,GAAG;QACb9B,IAAI,EAAE,KADO;QAEbe,IAAI,EAAE9E,OAFO;QAGbiE;MAHa,CAAf;MAKAG,cAAc,CAACyB,MAAD,EAAStE,GAAT,CAAd;IACD;EACF;;EACD,OAAOgE,GAAP;AACD;;AAED,SAAS7D,mBAAT,CACEoC,GADF,EAEE6B,KAFF,EAG6B;EAC3B,IAAI7B,GAAG,CAACG,MAAJ,CAAWF,IAAX,KAAoB,KAAxB,EAA+B;IAC7B,MAAM,IAAIgB,KAAJ,CAAW,GAAE,IAAAC,qBAAA,EAAIlB,GAAJ,CAAS,sCAAtB,CAAN;EACD;;EACD,IAAIA,GAAG,CAACG,MAAJ,CAAWF,IAAX,KAAoB,WAAxB,EAAqC;IACnC,MAAM,IAAIgB,KAAJ,CAAW,GAAE,IAAAC,qBAAA,EAAIlB,GAAJ,CAAS,4CAAtB,CAAN;EACD;;EACD,MAAMG,MAAgB,GAAGH,GAAG,CAACG,MAA7B;EAEA,MAAM6B,GAAG,GAAG,IAAAC,6BAAA,EAAYjC,GAAZ,EAAiB6B,KAAjB,CAAZ;;EACA,IAAIG,GAAJ,EAAS;IACP,KAAK,MAAM,CAACE,KAAD,EAAQC,IAAR,CAAX,IAA4BH,GAAG,CAACI,OAAJ,EAA5B,EAA2C;MACzC,MAAMC,MAAM,GAAG,IAAAP,wBAAA,EAAO9B,GAAP,EAAYkC,KAAZ,CAAf;MACA,MAAMzE,GAAG,GAAG,IAAA2B,8BAAA,EAAaiD,MAAb,EAAqBF,IAArB,CAAZ;MACA,IAAI,CAAC1E,GAAL,EAAU,MAAM,IAAIwD,KAAJ,CAAW,GAAE,IAAAC,qBAAA,EAAImB,MAAJ,CAAY,oBAAzB,CAAN;MAEV,MAAMC,YAAY,GAAG;QACnBrC,IAAI,EAAE,WADa;QAEnBiC,KAFmB;QAGnB/B;MAHmB,CAArB;MAKAG,cAAc,CAACgC,YAAD,EAAe7E,GAAf,CAAd;IACD;EACF;;EACD,OAAOuE,GAAP;AACD;;AAEM,SAASO,+BAAT,CACLC,KADK,EAELN,KAFK,EAGLjC,IAHK,EAILwC,CAJK,EAKC;EACN,IAAIP,KAAK,KAAK,CAAd,EAAiB;EAEjB,MAAMQ,QAAQ,GAAGF,KAAK,CAACN,KAAK,GAAG,CAAT,CAAtB;EACA,MAAMS,QAAQ,GAAGH,KAAK,CAACN,KAAD,CAAtB;;EAEA,IACEQ,QAAQ,CAACE,IAAT,IACAF,QAAQ,CAACG,OAAT,KAAqBC,SADrB,IAEA,OAAOH,QAAQ,CAACd,KAAhB,KAA0B,QAH5B,EAIE;IACAY,CAAC,CAAC/B,OAAF,IACG,8BAAD,GACC,IAAGT,IAAK,cAAayC,QAAQ,CAACE,IAAT,CAAcG,OAAQ,MAAKC,IAAI,CAACC,SAAL,CAC/CN,QAAQ,CAACd,KADsC,EAE/CiB,SAF+C,EAG/C,CAH+C,CAI/C,QALF,GAMC,iBAAgB7C,IAAK,gEAPxB;EAQD;AACF"}