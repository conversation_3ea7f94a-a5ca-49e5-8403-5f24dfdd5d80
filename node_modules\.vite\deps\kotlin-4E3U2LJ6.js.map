{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/kotlin/kotlin.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n// src/basic-languages/kotlin/kotlin.ts\nvar conf = {\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\#\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"<\", close: \">\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".kt\",\n  keywords: [\n    \"as\",\n    \"as?\",\n    \"break\",\n    \"class\",\n    \"continue\",\n    \"do\",\n    \"else\",\n    \"false\",\n    \"for\",\n    \"fun\",\n    \"if\",\n    \"in\",\n    \"!in\",\n    \"interface\",\n    \"is\",\n    \"!is\",\n    \"null\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"super\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typealias\",\n    \"val\",\n    \"var\",\n    \"when\",\n    \"while\",\n    \"by\",\n    \"catch\",\n    \"constructor\",\n    \"delegate\",\n    \"dynamic\",\n    \"field\",\n    \"file\",\n    \"finally\",\n    \"get\",\n    \"import\",\n    \"init\",\n    \"param\",\n    \"property\",\n    \"receiver\",\n    \"set\",\n    \"setparam\",\n    \"where\",\n    \"actual\",\n    \"abstract\",\n    \"annotation\",\n    \"companion\",\n    \"const\",\n    \"crossinline\",\n    \"data\",\n    \"enum\",\n    \"expect\",\n    \"external\",\n    \"final\",\n    \"infix\",\n    \"inline\",\n    \"inner\",\n    \"internal\",\n    \"lateinit\",\n    \"noinline\",\n    \"open\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"reified\",\n    \"sealed\",\n    \"suspend\",\n    \"tailrec\",\n    \"vararg\",\n    \"field\",\n    \"it\"\n  ],\n  operators: [\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"=\",\n    \"+=\",\n    \"-=\",\n    \"*=\",\n    \"/=\",\n    \"%=\",\n    \"++\",\n    \"--\",\n    \"&&\",\n    \"||\",\n    \"!\",\n    \"==\",\n    \"!=\",\n    \"===\",\n    \"!==\",\n    \">\",\n    \"<\",\n    \"<=\",\n    \">=\",\n    \"[\",\n    \"]\",\n    \"!!\",\n    \"?.\",\n    \"?:\",\n    \"::\",\n    \"..\",\n    \":\",\n    \"?\",\n    \"->\",\n    \"@\",\n    \";\",\n    \"$\",\n    \"_\"\n  ],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+(_+\\d+)*/,\n  octaldigits: /[0-7]+(_+[0-7]+)*/,\n  binarydigits: /[0-1]+(_+[0-1]+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  tokenizer: {\n    root: [\n      [/[A-Z][\\w\\$]*/, \"type.identifier\"],\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/[<>](?!@symbols)/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ],\n      [/@\\s*[a-zA-Z_\\$][\\w\\$]*/, \"annotation\"],\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\"],\n      [/0(@octaldigits)[Ll]?/, \"number.octal\"],\n      [/0[bB](@binarydigits)[Ll]?/, \"number.binary\"],\n      [/(@digits)[fFdD]/, \"number.float\"],\n      [/(@digits)[lL]?/, \"number\"],\n      [/[;,.]/, \"delimiter\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      [/\"\"\"/, \"string\", \"@multistring\"],\n      [/\"/, \"string\", \"@string\"],\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/\\/\\*\\*(?!\\/)/, \"comment.doc\", \"@javadoc\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    javadoc: [\n      [/[^\\/*]+/, \"comment.doc\"],\n      [/\\/\\*/, \"comment.doc\", \"@push\"],\n      [/\\/\\*/, \"comment.doc.invalid\"],\n      [/\\*\\//, \"comment.doc\", \"@pop\"],\n      [/[\\/*]/, \"comment.doc\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    multistring: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"\"\"/, \"string\", \"@pop\"],\n      [/./, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AAQA,IAAI,OAAO;AAAA,EACT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,oDAAoD;AAAA,MACtE,KAAK,IAAI,OAAO,sDAAsD;AAAA,IACxE;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,gBAAgB,iBAAiB;AAAA,MAClC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,oBAAoB,WAAW;AAAA,MAChC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,0BAA0B,YAAY;AAAA,MACvC,CAAC,0CAA0C,cAAc;AAAA,MACzD,CAAC,qDAAqD,cAAc;AAAA,MACpE,CAAC,0BAA0B,YAAY;AAAA,MACvC,CAAC,wBAAwB,cAAc;AAAA,MACvC,CAAC,6BAA6B,eAAe;AAAA,MAC7C,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,kBAAkB,QAAQ;AAAA,MAC3B,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,mBAAmB,gBAAgB;AAAA,MACpC,CAAC,OAAO,UAAU,cAAc;AAAA,MAChC,CAAC,KAAK,UAAU,SAAS;AAAA,MACzB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,gBAAgB,eAAe,UAAU;AAAA,MAC1C,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,aAAa;AAAA,MACzB,CAAC,QAAQ,eAAe,OAAO;AAAA,MAC/B,CAAC,QAAQ,qBAAqB;AAAA,MAC9B,CAAC,QAAQ,eAAe,MAAM;AAAA,MAC9B,CAAC,SAAS,aAAa;AAAA,IACzB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,IACA,aAAa;AAAA,MACX,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,OAAO,UAAU,MAAM;AAAA,MACxB,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,EACF;AACF;", "names": []}