"use strict";import{isAlternativeContainer as I}from"../../parser/node-utils.js";import{createAlternative as d,createGroup as x}from"../../parser/parse.js";import{isAllowedSimpleNode as g,isNodeEqual as v}from"./extract-prefix.js";const E={"*"({node:o}){if(!I(o))return;const t=2,i=o.body.length;if(i<t*2||i%t)return;const y=[...o.body.slice(0,t).map(e=>e.body)],s=Array.from({length:t},()=>[]),l=Array(t).fill(!1),p=Math.max(...y.map(e=>e.length));for(let e=0;e<p;e++)for(let r=0;r<t;r++)if(!l[r]){const n=y[r][e];!n||!g(n)||!j(n,o.body,r,e,t)?l[r]=!0:s[r].push(n)}if(!s.some(e=>e.length))return;const f=[];let a=0;for(let e=0;e<i;e++)f.push(d({body:o.body[e].body.slice(s[a].length)})),a=a<t-1?a+1:0;for(let e=0;e<i/t;e++){const r=f.slice(e*t,e*t+t);for(let n=1;n<r.length;n++){const h=r[n].body;if(h.length!==r[0].body.length||!h.every((A,N)=>g(A)&&v(A,r[0].body[N])))return}}const b=[];for(let e=0;e<t;e++)b.push(d({body:s[e]}));const c=x({body:b}),m=d({body:[c]}),u=x({body:f.filter((e,r)=>r%t)});u.body.every(e=>!e.body.length)?o.body=c.body:(m.body.push(u),o.body=[m])}};function j(o,t,i,y,s){for(let l=i;l<t.length;l+=s){const f=t[l].body[y];if(!f||!v(f,o))return!1}return!0}export{E as extractPrefix2};
//# sourceMappingURL=extract-prefix-2.js.map
