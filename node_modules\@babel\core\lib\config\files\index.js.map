{"version": 3, "names": ["resolvePlugin", "gens<PERSON>", "plugins", "sync", "resolvePreset"], "sources": ["../../../src/config/files/index.ts"], "sourcesContent": ["type indexBrowserType = typeof import(\"./index-browser\");\ntype indexType = typeof import(\"./index\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of index-browser, since this file may be replaced at bundle time with index-browser.\n({} as any as indexBrowserType as indexType);\n\nexport { findPackageData } from \"./package\";\n\nexport {\n  findConfigUpwards,\n  findRelativeConfig,\n  findRootConfig,\n  loadConfig,\n  resolveShowConfigPath,\n  ROOT_CONFIG_FILENAMES,\n} from \"./configuration\";\nexport type {\n  ConfigFile,\n  IgnoreFile,\n  RelativeConfig,\n  FilePackageData,\n} from \"./types\";\nexport { loadPlugin, loadPreset } from \"./plugins\";\n\nimport gensync from \"gensync\";\nimport * as plugins from \"./plugins\";\n\nexport const resolvePlugin = gensync(plugins.resolvePlugin).sync;\nexport const resolvePreset = gensync(plugins.resolvePreset).sync;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;;AAEA;;AAcA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AApBA,CAAC,EAAD;;AAuBO,MAAMA,aAAa,GAAGC,UAAA,CAAQC,OAAO,CAACF,aAAhB,EAA+BG,IAArD;;;;AACA,MAAMC,aAAa,GAAGH,UAAA,CAAQC,OAAO,CAACE,aAAhB,EAA+BD,IAArD"}