{"version": 3, "names": ["versionRegExp", "v", "OptionValidator", "semverMin", "first", "second", "semver", "lt", "semverify", "version", "valid", "invariant", "test", "split", "toString", "length", "push", "join", "isUnreleasedVersion", "env", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "getLowestUnreleased", "a", "b", "getHighestUnreleased", "getLowestImplementedVersion", "plugin", "environment", "result", "chrome"], "sources": ["../src/utils.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\nimport { unreleasedLabels } from \"./targets\";\nimport type { Target, Targets } from \"./types\";\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\n\nconst versionRegExp = /^(\\d+|\\d+.\\d+)$/;\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nexport function semverMin(\n  first: string | undefined | null,\n  second: string,\n): string {\n  return first && semver.lt(first, second) ? first : second;\n}\n\n// Convert version to a semver value.\n// 2.5 -> 2.5.0; 1 -> 1.0.0;\nexport function semverify(version: number | string): string {\n  if (typeof version === \"string\" && semver.valid(version)) {\n    return version;\n  }\n\n  v.invariant(\n    typeof version === \"number\" ||\n      (typeof version === \"string\" && versionRegExp.test(version)),\n    `'${version}' is not a valid version`,\n  );\n\n  const split = version.toString().split(\".\");\n  while (split.length < 3) {\n    split.push(\"0\");\n  }\n  return split.join(\".\");\n}\n\nexport function isUnreleasedVersion(\n  version: string | number,\n  env: Target,\n): boolean {\n  const unreleasedLabel =\n    // @ts-expect-error unreleasedLabel will be guarded later\n    unreleasedLabels[env];\n  return (\n    !!unreleasedLabel && unreleasedLabel === version.toString().toLowerCase()\n  );\n}\n\nexport function getLowestUnreleased(a: string, b: string, env: Target): string {\n  const unreleasedLabel:\n    | typeof unreleasedLabels[keyof typeof unreleasedLabels]\n    | undefined =\n    // @ts-expect-error unreleasedLabel is undefined when env is not safari\n    unreleasedLabels[env];\n  if (a === unreleasedLabel) {\n    return b;\n  }\n  if (b === unreleasedLabel) {\n    return a;\n  }\n  return semverMin(a, b);\n}\n\nexport function getHighestUnreleased(\n  a: string,\n  b: string,\n  env: Target,\n): string {\n  return getLowestUnreleased(a, b, env) === a ? b : a;\n}\n\nexport function getLowestImplementedVersion(\n  plugin: Targets,\n  environment: Target,\n): string {\n  const result = plugin[environment];\n  // When Android support data is absent, use Chrome data as fallback\n  if (!result && environment === \"android\") {\n    return plugin.chrome;\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;;AACA;;AACA;;AAKA,MAAMA,aAAa,GAAG,iBAAtB;AAEA,MAAMC,CAAC,GAAG,IAAIC,sCAAJ,qCAAV;;AAEO,SAASC,SAAT,CACLC,KADK,EAELC,MAFK,EAGG;EACR,OAAOD,KAAK,IAAIE,OAAM,CAACC,EAAP,CAAUH,KAAV,EAAiBC,MAAjB,CAAT,GAAoCD,KAApC,GAA4CC,MAAnD;AACD;;AAIM,SAASG,SAAT,CAAmBC,OAAnB,EAAqD;EAC1D,IAAI,OAAOA,OAAP,KAAmB,QAAnB,IAA+BH,OAAM,CAACI,KAAP,CAAaD,OAAb,CAAnC,EAA0D;IACxD,OAAOA,OAAP;EACD;;EAEDR,CAAC,CAACU,SAAF,CACE,OAAOF,OAAP,KAAmB,QAAnB,IACG,OAAOA,OAAP,KAAmB,QAAnB,IAA+BT,aAAa,CAACY,IAAd,CAAmBH,OAAnB,CAFpC,EAGG,IAAGA,OAAQ,0BAHd;EAMA,MAAMI,KAAK,GAAGJ,OAAO,CAACK,QAAR,GAAmBD,KAAnB,CAAyB,GAAzB,CAAd;;EACA,OAAOA,KAAK,CAACE,MAAN,GAAe,CAAtB,EAAyB;IACvBF,KAAK,CAACG,IAAN,CAAW,GAAX;EACD;;EACD,OAAOH,KAAK,CAACI,IAAN,CAAW,GAAX,CAAP;AACD;;AAEM,SAASC,mBAAT,CACLT,OADK,EAELU,GAFK,EAGI;EACT,MAAMC,eAAe,GAEnBC,yBAAA,CAAiBF,GAAjB,CAFF;EAGA,OACE,CAAC,CAACC,eAAF,IAAqBA,eAAe,KAAKX,OAAO,CAACK,QAAR,GAAmBQ,WAAnB,EAD3C;AAGD;;AAEM,SAASC,mBAAT,CAA6BC,CAA7B,EAAwCC,CAAxC,EAAmDN,GAAnD,EAAwE;EAC7E,MAAMC,eAEO,GAEXC,yBAAA,CAAiBF,GAAjB,CAJF;;EAKA,IAAIK,CAAC,KAAKJ,eAAV,EAA2B;IACzB,OAAOK,CAAP;EACD;;EACD,IAAIA,CAAC,KAAKL,eAAV,EAA2B;IACzB,OAAOI,CAAP;EACD;;EACD,OAAOrB,SAAS,CAACqB,CAAD,EAAIC,CAAJ,CAAhB;AACD;;AAEM,SAASC,oBAAT,CACLF,CADK,EAELC,CAFK,EAGLN,GAHK,EAIG;EACR,OAAOI,mBAAmB,CAACC,CAAD,EAAIC,CAAJ,EAAON,GAAP,CAAnB,KAAmCK,CAAnC,GAAuCC,CAAvC,GAA2CD,CAAlD;AACD;;AAEM,SAASG,2BAAT,CACLC,MADK,EAELC,WAFK,EAGG;EACR,MAAMC,MAAM,GAAGF,MAAM,CAACC,WAAD,CAArB;;EAEA,IAAI,CAACC,MAAD,IAAWD,WAAW,KAAK,SAA/B,EAA0C;IACxC,OAAOD,MAAM,CAACG,MAAd;EACD;;EACD,OAAOD,MAAP;AACD"}