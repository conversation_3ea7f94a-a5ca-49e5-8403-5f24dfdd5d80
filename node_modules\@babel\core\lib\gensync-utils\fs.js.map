{"version": 3, "names": ["readFile", "gens<PERSON>", "sync", "fs", "readFileSync", "errback", "stat", "statSync"], "sources": ["../../src/gensync-utils/fs.ts"], "sourcesContent": ["import fs from \"fs\";\nimport gensync from \"gensync\";\n\nexport const readFile = gensync<[filepath: string, encoding: \"utf8\"], string>({\n  sync: fs.readFileSync,\n  errback: fs.readFile,\n});\n\nexport const stat = gensync({\n  sync: fs.statSync,\n  errback: fs.stat,\n});\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEO,MAAMA,QAAQ,GAAGC,UAAA,CAAsD;EAC5EC,IAAI,EAAEC,KAAA,CAAGC,YADmE;EAE5EC,OAAO,EAAEF,KAAA,CAAGH;AAFgE,CAAtD,CAAjB;;;;AAKA,MAAMM,IAAI,GAAGL,UAAA,CAAQ;EAC1BC,IAAI,EAAEC,KAAA,CAAGI,QADiB;EAE1BF,OAAO,EAAEF,KAAA,CAAGG;AAFc,CAAR,CAAb"}