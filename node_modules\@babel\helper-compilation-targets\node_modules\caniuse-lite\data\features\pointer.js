module.exports={A:{A:{"1":"B","2":"J D E F 4B","164":"A"},B:{"1":"C K L G M N O P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H"},C:{"1":"qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB","2":"5B pB I q 6B 7B","8":"0 1 2 3 4 5 6 7 8 9 J D E F A B C K L G M N O r s t u v w x y z AB BB CB","328":"DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB"},D:{"1":"RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B","2":"I q J D E F A B C K L G M N O r s t","8":"0 1 2 3 4 5 6 7 8 9 u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB","584":"OB PB QB"},E:{"1":"K L G wB FC GC xB yB zB 0B oB 1B HC","2":"I q J AC uB BC","8":"D E F A B C CC DC EC vB mB","1096":"nB"},F:{"1":"EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a","2":"F B C IC JC KC LC mB 2B MC nB","8":"0 1 2 3 4 5 6 7 8 9 G M N O r s t u v w x y z AB","584":"BB CB DB"},G:{"1":"bC cC dC eC fC gC xB yB zB 0B oB 1B","8":"E uB NC 3B OC PC QC RC SC TC UC VC WC XC YC ZC","6148":"aC"},H:{"2":"hC"},I:{"1":"H","8":"pB I iC jC kC lC 3B mC nC"},J:{"8":"D A"},K:{"1":"c","2":"A","8":"B C mB 2B nB"},L:{"1":"H"},M:{"1":"b"},N:{"1":"B","36":"A"},O:{"1":"oC"},P:{"1":"qC rC sC tC vB uC vC wC xC yC oB zC 0C","2":"pC","8":"I"},Q:{"1":"wB"},R:{"1":"1C"},S:{"328":"2C"}},B:2,C:"Pointer events"};
