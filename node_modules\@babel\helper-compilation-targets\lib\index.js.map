{"version": 3, "names": ["ESM_SUPPORT", "browserModulesData", "v", "OptionValidator", "validateTargetNames", "targets", "validTargets", "Object", "keys", "TargetNames", "target", "Error", "formatMessage", "findSuggestion", "isBrowsersQueryValid", "browsers", "Array", "isArray", "every", "b", "validateBrowsers", "invariant", "undefined", "String", "getLowestVersions", "reduce", "all", "browser", "browserName", "browserVersion", "split", "browserNameMap", "splitVersion", "toLowerCase", "isSplitUnreleased", "isUnreleasedVersion", "semverify", "version", "isUnreleased", "getLowestUnreleased", "parsedBrowserVersion", "semverMin", "e", "outputDecimalWarning", "decimalTargets", "length", "console", "warn", "for<PERSON>ach", "value", "semverifyTarget", "error", "node<PERSON>arget<PERSON><PERSON><PERSON>", "parsed", "process", "versions", "node", "defaultTargetParser", "generateTargets", "inputTargets", "input", "<PERSON><PERSON><PERSON><PERSON>", "resolveTargets", "queries", "env", "resolved", "browserslist", "mobileToDesktop", "getTargets", "options", "config<PERSON><PERSON>", "shouldParseBrowsers", "hasTargets", "shouldSearchForConfig", "ignoreBrowserslistConfig", "loadConfig", "config", "configFile", "path", "browserslistEnv", "map", "join", "queryBrowsers", "esmSupportVersion", "getHighestUnreleased", "assign", "result", "decimalWarnings", "sort", "push", "parsed<PERSON><PERSON>get", "parsedValue"], "sources": ["../src/index.ts"], "sourcesContent": ["import browserslist from \"browserslist\";\nimport { findSuggestion } from \"@babel/helper-validator-option\";\nimport browserModulesData from \"@babel/compat-data/native-modules\";\n\nimport {\n  semverify,\n  semverMin,\n  isUnreleasedVersion,\n  getLowestUnreleased,\n  getHighestUnreleased,\n} from \"./utils\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\nimport { browserNameMap } from \"./targets\";\nimport { TargetNames } from \"./options\";\nimport type {\n  Target,\n  Targets,\n  InputTargets,\n  Browsers,\n  BrowserslistBrowserName,\n  TargetsTuple,\n} from \"./types\";\n\nexport type { Target, Targets, InputTargets };\n\nexport { prettifyTargets } from \"./pretty\";\nexport { getInclusionReasons } from \"./debug\";\nexport { default as filterItems, isRequired } from \"./filter-items\";\nexport { unreleasedLabels } from \"./targets\";\nexport { TargetNames };\n\nconst ESM_SUPPORT = browserModulesData[\"es6.module\"];\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nfunction validateTargetNames(targets: Targets): TargetsTuple {\n  const validTargets = Object.keys(TargetNames);\n  for (const target of Object.keys(targets)) {\n    if (!(target in TargetNames)) {\n      throw new Error(\n        v.formatMessage(`'${target}' is not a valid target\n- Did you mean '${findSuggestion(target, validTargets)}'?`),\n      );\n    }\n  }\n\n  return targets as any;\n}\n\nexport function isBrowsersQueryValid(browsers: unknown): boolean {\n  return (\n    typeof browsers === \"string\" ||\n    (Array.isArray(browsers) && browsers.every(b => typeof b === \"string\"))\n  );\n}\n\nfunction validateBrowsers(browsers: Browsers | undefined) {\n  v.invariant(\n    browsers === undefined || isBrowsersQueryValid(browsers),\n    `'${String(browsers)}' is not a valid browserslist query`,\n  );\n\n  return browsers;\n}\n\nfunction getLowestVersions(browsers: Array<string>): Targets {\n  return browsers.reduce((all, browser) => {\n    const [browserName, browserVersion] = browser.split(\" \") as [\n      BrowserslistBrowserName,\n      string,\n    ];\n    const target: Target = browserNameMap[browserName];\n\n    if (!target) {\n      return all;\n    }\n\n    try {\n      // Browser version can return as \"10.0-10.2\"\n      const splitVersion = browserVersion.split(\"-\")[0].toLowerCase();\n      const isSplitUnreleased = isUnreleasedVersion(splitVersion, target);\n\n      if (!all[target]) {\n        all[target] = isSplitUnreleased\n          ? splitVersion\n          : semverify(splitVersion);\n        return all;\n      }\n\n      const version = all[target];\n      const isUnreleased = isUnreleasedVersion(version, target);\n\n      if (isUnreleased && isSplitUnreleased) {\n        all[target] = getLowestUnreleased(version, splitVersion, target);\n      } else if (isUnreleased) {\n        all[target] = semverify(splitVersion);\n      } else if (!isUnreleased && !isSplitUnreleased) {\n        const parsedBrowserVersion = semverify(splitVersion);\n\n        all[target] = semverMin(version, parsedBrowserVersion);\n      }\n    } catch (e) {}\n\n    return all;\n  }, {} as Record<Target, string>);\n}\n\nfunction outputDecimalWarning(\n  decimalTargets: Array<{ target: string; value: number }>,\n): void {\n  if (!decimalTargets.length) {\n    return;\n  }\n\n  console.warn(\"Warning, the following targets are using a decimal version:\\n\");\n  decimalTargets.forEach(({ target, value }) =>\n    console.warn(`  ${target}: ${value}`),\n  );\n  console.warn(`\nWe recommend using a string for minor/patch versions to avoid numbers like 6.10\ngetting parsed as 6.1, which can lead to unexpected behavior.\n`);\n}\n\nfunction semverifyTarget(target: keyof Targets, value: string) {\n  try {\n    return semverify(value);\n  } catch (error) {\n    throw new Error(\n      v.formatMessage(\n        `'${value}' is not a valid value for 'targets.${target}'.`,\n      ),\n    );\n  }\n}\n\n// Parse `node: true` and `node: \"current\"` to version\nfunction nodeTargetParser(value: true | string) {\n  const parsed =\n    value === true || value === \"current\"\n      ? process.versions.node\n      : semverifyTarget(\"node\", value);\n  return [\"node\" as const, parsed] as const;\n}\n\nfunction defaultTargetParser(\n  target: Exclude<Target, \"node\">,\n  value: string,\n): readonly [Exclude<Target, \"node\">, string] {\n  const version = isUnreleasedVersion(value, target)\n    ? value.toLowerCase()\n    : semverifyTarget(target, value);\n  return [target, version] as const;\n}\n\nfunction generateTargets(inputTargets: InputTargets): Targets {\n  const input = { ...inputTargets };\n  delete input.esmodules;\n  delete input.browsers;\n  return input as any as Targets;\n}\n\nfunction resolveTargets(queries: Browsers, env?: string): Targets {\n  const resolved = browserslist(queries, {\n    mobileToDesktop: true,\n    env,\n  });\n  return getLowestVersions(resolved);\n}\n\ntype GetTargetsOption = {\n  // This is not the path of the config file, but the path where start searching it from\n  configPath?: string;\n  // The path of the config file\n  configFile?: string;\n  // The env to pass to browserslist\n  browserslistEnv?: string;\n  // true to disable config loading\n  ignoreBrowserslistConfig?: boolean;\n};\n\nexport default function getTargets(\n  inputTargets: InputTargets = {} as InputTargets,\n  options: GetTargetsOption = {},\n): Targets {\n  let { browsers, esmodules } = inputTargets;\n  const { configPath = \".\" } = options;\n\n  validateBrowsers(browsers);\n\n  const input = generateTargets(inputTargets);\n  let targets: TargetsTuple = validateTargetNames(input);\n\n  const shouldParseBrowsers = !!browsers;\n  const hasTargets = shouldParseBrowsers || Object.keys(targets).length > 0;\n  const shouldSearchForConfig =\n    !options.ignoreBrowserslistConfig && !hasTargets;\n\n  if (!browsers && shouldSearchForConfig) {\n    browsers = browserslist.loadConfig({\n      config: options.configFile,\n      path: configPath,\n      env: options.browserslistEnv,\n    });\n    if (browsers == null) {\n      if (process.env.BABEL_8_BREAKING) {\n        // In Babel 8, if no targets are passed, we use browserslist's defaults\n        // and exclude IE 11.\n        browsers = [\"defaults, not ie 11\"];\n      } else {\n        // If no targets are passed, we need to overwrite browserslist's defaults\n        // so that we enable all transforms (acting like the now deprecated\n        // preset-latest).\n        browsers = [];\n      }\n    }\n  }\n\n  // `esmodules` as a target indicates the specific set of browsers supporting ES Modules.\n  // These values OVERRIDE the `browsers` field.\n  if (esmodules && (esmodules !== \"intersect\" || !browsers?.length)) {\n    browsers = Object.keys(ESM_SUPPORT)\n      .map(\n        (browser: keyof typeof ESM_SUPPORT) =>\n          `${browser} >= ${ESM_SUPPORT[browser]}`,\n      )\n      .join(\", \");\n    esmodules = false;\n  }\n\n  // If current value of `browsers` is undefined (`ignoreBrowserslistConfig` should be `false`)\n  // or an empty array (without any user config, use default config),\n  // we don't need to call `resolveTargets` to execute the related methods of `browserslist` library.\n  if (browsers?.length) {\n    const queryBrowsers = resolveTargets(browsers, options.browserslistEnv);\n\n    if (esmodules === \"intersect\") {\n      for (const browser of Object.keys(queryBrowsers) as Target[]) {\n        const version = queryBrowsers[browser];\n        const esmSupportVersion =\n          // @ts-expect-error ie is not in ESM_SUPPORT\n          ESM_SUPPORT[browser];\n\n        if (esmSupportVersion) {\n          queryBrowsers[browser] = getHighestUnreleased(\n            version,\n            semverify(esmSupportVersion),\n            browser,\n          );\n        } else {\n          delete queryBrowsers[browser];\n        }\n      }\n    }\n\n    targets = Object.assign(queryBrowsers, targets);\n  }\n\n  // Parse remaining targets\n  const result: Targets = {} as Targets;\n  const decimalWarnings = [];\n  for (const target of Object.keys(targets).sort() as Target[]) {\n    const value = targets[target];\n\n    // Warn when specifying minor/patch as a decimal\n    if (typeof value === \"number\" && value % 1 !== 0) {\n      decimalWarnings.push({ target, value });\n    }\n\n    const [parsedTarget, parsedValue] =\n      target === \"node\"\n        ? nodeTargetParser(value)\n        : defaultTargetParser(target, value as string);\n\n    if (parsedValue) {\n      // Merge (lowest wins)\n      result[parsedTarget] = parsedValue;\n    }\n  }\n\n  outputDecimalWarning(decimalWarnings);\n\n  return result;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AAEA;;AAQA;;AACA;;AAYA;;AACA;;AACA;;AAIA,MAAMA,WAAW,GAAGC,cAAkB,CAAC,YAAD,CAAtC;AAGA,MAAMC,CAAC,GAAG,IAAIC,sCAAJ,qCAAV;;AAEA,SAASC,mBAAT,CAA6BC,OAA7B,EAA6D;EAC3D,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAP,CAAYC,oBAAZ,CAArB;;EACA,KAAK,MAAMC,MAAX,IAAqBH,MAAM,CAACC,IAAP,CAAYH,OAAZ,CAArB,EAA2C;IACzC,IAAI,EAAEK,MAAM,IAAID,oBAAZ,CAAJ,EAA8B;MAC5B,MAAM,IAAIE,KAAJ,CACJT,CAAC,CAACU,aAAF,CAAiB,IAAGF,MAAO;AACnC,kBAAkB,IAAAG,qCAAA,EAAeH,MAAf,EAAuBJ,YAAvB,CAAqC,IAD/C,CADI,CAAN;IAID;EACF;;EAED,OAAOD,OAAP;AACD;;AAEM,SAASS,oBAAT,CAA8BC,QAA9B,EAA0D;EAC/D,OACE,OAAOA,QAAP,KAAoB,QAApB,IACCC,KAAK,CAACC,OAAN,CAAcF,QAAd,KAA2BA,QAAQ,CAACG,KAAT,CAAeC,CAAC,IAAI,OAAOA,CAAP,KAAa,QAAjC,CAF9B;AAID;;AAED,SAASC,gBAAT,CAA0BL,QAA1B,EAA0D;EACxDb,CAAC,CAACmB,SAAF,CACEN,QAAQ,KAAKO,SAAb,IAA0BR,oBAAoB,CAACC,QAAD,CADhD,EAEG,IAAGQ,MAAM,CAACR,QAAD,CAAW,qCAFvB;EAKA,OAAOA,QAAP;AACD;;AAED,SAASS,iBAAT,CAA2BT,QAA3B,EAA6D;EAC3D,OAAOA,QAAQ,CAACU,MAAT,CAAgB,CAACC,GAAD,EAAMC,OAAN,KAAkB;IACvC,MAAM,CAACC,WAAD,EAAcC,cAAd,IAAgCF,OAAO,CAACG,KAAR,CAAc,GAAd,CAAtC;IAIA,MAAMpB,MAAc,GAAGqB,uBAAA,CAAeH,WAAf,CAAvB;;IAEA,IAAI,CAAClB,MAAL,EAAa;MACX,OAAOgB,GAAP;IACD;;IAED,IAAI;MAEF,MAAMM,YAAY,GAAGH,cAAc,CAACC,KAAf,CAAqB,GAArB,EAA0B,CAA1B,EAA6BG,WAA7B,EAArB;MACA,MAAMC,iBAAiB,GAAG,IAAAC,0BAAA,EAAoBH,YAApB,EAAkCtB,MAAlC,CAA1B;;MAEA,IAAI,CAACgB,GAAG,CAAChB,MAAD,CAAR,EAAkB;QAChBgB,GAAG,CAAChB,MAAD,CAAH,GAAcwB,iBAAiB,GAC3BF,YAD2B,GAE3B,IAAAI,gBAAA,EAAUJ,YAAV,CAFJ;QAGA,OAAON,GAAP;MACD;;MAED,MAAMW,OAAO,GAAGX,GAAG,CAAChB,MAAD,CAAnB;MACA,MAAM4B,YAAY,GAAG,IAAAH,0BAAA,EAAoBE,OAApB,EAA6B3B,MAA7B,CAArB;;MAEA,IAAI4B,YAAY,IAAIJ,iBAApB,EAAuC;QACrCR,GAAG,CAAChB,MAAD,CAAH,GAAc,IAAA6B,0BAAA,EAAoBF,OAApB,EAA6BL,YAA7B,EAA2CtB,MAA3C,CAAd;MACD,CAFD,MAEO,IAAI4B,YAAJ,EAAkB;QACvBZ,GAAG,CAAChB,MAAD,CAAH,GAAc,IAAA0B,gBAAA,EAAUJ,YAAV,CAAd;MACD,CAFM,MAEA,IAAI,CAACM,YAAD,IAAiB,CAACJ,iBAAtB,EAAyC;QAC9C,MAAMM,oBAAoB,GAAG,IAAAJ,gBAAA,EAAUJ,YAAV,CAA7B;QAEAN,GAAG,CAAChB,MAAD,CAAH,GAAc,IAAA+B,gBAAA,EAAUJ,OAAV,EAAmBG,oBAAnB,CAAd;MACD;IACF,CAxBD,CAwBE,OAAOE,CAAP,EAAU,CAAE;;IAEd,OAAOhB,GAAP;EACD,CAtCM,EAsCJ,EAtCI,CAAP;AAuCD;;AAED,SAASiB,oBAAT,CACEC,cADF,EAEQ;EACN,IAAI,CAACA,cAAc,CAACC,MAApB,EAA4B;IAC1B;EACD;;EAEDC,OAAO,CAACC,IAAR,CAAa,+DAAb;EACAH,cAAc,CAACI,OAAf,CAAuB,CAAC;IAAEtC,MAAF;IAAUuC;EAAV,CAAD,KACrBH,OAAO,CAACC,IAAR,CAAc,KAAIrC,MAAO,KAAIuC,KAAM,EAAnC,CADF;EAGAH,OAAO,CAACC,IAAR,CAAc;AAChB;AACA;AACA,CAHE;AAID;;AAED,SAASG,eAAT,CAAyBxC,MAAzB,EAAgDuC,KAAhD,EAA+D;EAC7D,IAAI;IACF,OAAO,IAAAb,gBAAA,EAAUa,KAAV,CAAP;EACD,CAFD,CAEE,OAAOE,KAAP,EAAc;IACd,MAAM,IAAIxC,KAAJ,CACJT,CAAC,CAACU,aAAF,CACG,IAAGqC,KAAM,uCAAsCvC,MAAO,IADzD,CADI,CAAN;EAKD;AACF;;AAGD,SAAS0C,gBAAT,CAA0BH,KAA1B,EAAgD;EAC9C,MAAMI,MAAM,GACVJ,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,SAA5B,GACIK,OAAO,CAACC,QAAR,CAAiBC,IADrB,GAEIN,eAAe,CAAC,MAAD,EAASD,KAAT,CAHrB;EAIA,OAAO,CAAC,MAAD,EAAkBI,MAAlB,CAAP;AACD;;AAED,SAASI,mBAAT,CACE/C,MADF,EAEEuC,KAFF,EAG8C;EAC5C,MAAMZ,OAAO,GAAG,IAAAF,0BAAA,EAAoBc,KAApB,EAA2BvC,MAA3B,IACZuC,KAAK,CAAChB,WAAN,EADY,GAEZiB,eAAe,CAACxC,MAAD,EAASuC,KAAT,CAFnB;EAGA,OAAO,CAACvC,MAAD,EAAS2B,OAAT,CAAP;AACD;;AAED,SAASqB,eAAT,CAAyBC,YAAzB,EAA8D;EAC5D,MAAMC,KAAK,qBAAQD,YAAR,CAAX;EACA,OAAOC,KAAK,CAACC,SAAb;EACA,OAAOD,KAAK,CAAC7C,QAAb;EACA,OAAO6C,KAAP;AACD;;AAED,SAASE,cAAT,CAAwBC,OAAxB,EAA2CC,GAA3C,EAAkE;EAChE,MAAMC,QAAQ,GAAGC,aAAY,CAACH,OAAD,EAAU;IACrCI,eAAe,EAAE,IADoB;IAErCH;EAFqC,CAAV,CAA7B;;EAIA,OAAOxC,iBAAiB,CAACyC,QAAD,CAAxB;AACD;;AAac,SAASG,UAAT,CACbT,YAA0B,GAAG,EADhB,EAEbU,OAAyB,GAAG,EAFf,EAGJ;EAAA;;EACT,IAAI;IAAEtD,QAAF;IAAY8C;EAAZ,IAA0BF,YAA9B;EACA,MAAM;IAAEW,UAAU,GAAG;EAAf,IAAuBD,OAA7B;EAEAjD,gBAAgB,CAACL,QAAD,CAAhB;EAEA,MAAM6C,KAAK,GAAGF,eAAe,CAACC,YAAD,CAA7B;EACA,IAAItD,OAAqB,GAAGD,mBAAmB,CAACwD,KAAD,CAA/C;EAEA,MAAMW,mBAAmB,GAAG,CAAC,CAACxD,QAA9B;EACA,MAAMyD,UAAU,GAAGD,mBAAmB,IAAIhE,MAAM,CAACC,IAAP,CAAYH,OAAZ,EAAqBwC,MAArB,GAA8B,CAAxE;EACA,MAAM4B,qBAAqB,GACzB,CAACJ,OAAO,CAACK,wBAAT,IAAqC,CAACF,UADxC;;EAGA,IAAI,CAACzD,QAAD,IAAa0D,qBAAjB,EAAwC;IACtC1D,QAAQ,GAAGmD,aAAY,CAACS,UAAb,CAAwB;MACjCC,MAAM,EAAEP,OAAO,CAACQ,UADiB;MAEjCC,IAAI,EAAER,UAF2B;MAGjCN,GAAG,EAAEK,OAAO,CAACU;IAHoB,CAAxB,CAAX;;IAKA,IAAIhE,QAAQ,IAAI,IAAhB,EAAsB;MAKb;QAILA,QAAQ,GAAG,EAAX;MACD;IACF;EACF;;EAID,IAAI8C,SAAS,KAAKA,SAAS,KAAK,WAAd,IAA6B,eAAC9C,QAAD,aAAC,UAAU8B,MAAX,CAAlC,CAAb,EAAmE;IACjE9B,QAAQ,GAAGR,MAAM,CAACC,IAAP,CAAYR,WAAZ,EACRgF,GADQ,CAENrD,OAAD,IACG,GAAEA,OAAQ,OAAM3B,WAAW,CAAC2B,OAAD,CAAU,EAHjC,EAKRsD,IALQ,CAKH,IALG,CAAX;IAMApB,SAAS,GAAG,KAAZ;EACD;;EAKD,kBAAI9C,QAAJ,aAAI,WAAU8B,MAAd,EAAsB;IACpB,MAAMqC,aAAa,GAAGpB,cAAc,CAAC/C,QAAD,EAAWsD,OAAO,CAACU,eAAnB,CAApC;;IAEA,IAAIlB,SAAS,KAAK,WAAlB,EAA+B;MAC7B,KAAK,MAAMlC,OAAX,IAAsBpB,MAAM,CAACC,IAAP,CAAY0E,aAAZ,CAAtB,EAA8D;QAC5D,MAAM7C,OAAO,GAAG6C,aAAa,CAACvD,OAAD,CAA7B;QACA,MAAMwD,iBAAiB,GAErBnF,WAAW,CAAC2B,OAAD,CAFb;;QAIA,IAAIwD,iBAAJ,EAAuB;UACrBD,aAAa,CAACvD,OAAD,CAAb,GAAyB,IAAAyD,2BAAA,EACvB/C,OADuB,EAEvB,IAAAD,gBAAA,EAAU+C,iBAAV,CAFuB,EAGvBxD,OAHuB,CAAzB;QAKD,CAND,MAMO;UACL,OAAOuD,aAAa,CAACvD,OAAD,CAApB;QACD;MACF;IACF;;IAEDtB,OAAO,GAAGE,MAAM,CAAC8E,MAAP,CAAcH,aAAd,EAA6B7E,OAA7B,CAAV;EACD;;EAGD,MAAMiF,MAAe,GAAG,EAAxB;EACA,MAAMC,eAAe,GAAG,EAAxB;;EACA,KAAK,MAAM7E,MAAX,IAAqBH,MAAM,CAACC,IAAP,CAAYH,OAAZ,EAAqBmF,IAArB,EAArB,EAA8D;IAC5D,MAAMvC,KAAK,GAAG5C,OAAO,CAACK,MAAD,CAArB;;IAGA,IAAI,OAAOuC,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,GAAG,CAAR,KAAc,CAA/C,EAAkD;MAChDsC,eAAe,CAACE,IAAhB,CAAqB;QAAE/E,MAAF;QAAUuC;MAAV,CAArB;IACD;;IAED,MAAM,CAACyC,YAAD,EAAeC,WAAf,IACJjF,MAAM,KAAK,MAAX,GACI0C,gBAAgB,CAACH,KAAD,CADpB,GAEIQ,mBAAmB,CAAC/C,MAAD,EAASuC,KAAT,CAHzB;;IAKA,IAAI0C,WAAJ,EAAiB;MAEfL,MAAM,CAACI,YAAD,CAAN,GAAuBC,WAAvB;IACD;EACF;;EAEDhD,oBAAoB,CAAC4C,eAAD,CAApB;EAEA,OAAOD,MAAP;AACD"}