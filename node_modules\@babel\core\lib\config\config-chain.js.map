{"version": 3, "names": ["debug", "buildDebug", "buildPresetChain", "arg", "context", "chain", "buildPresetChain<PERSON>er", "plugins", "dedupDescriptors", "presets", "options", "map", "o", "normalizeOptions", "files", "Set", "<PERSON><PERSON><PERSON><PERSON>", "root", "preset", "loadPresetDescriptors", "env", "envName", "loadPresetEnvDescriptors", "overrides", "index", "loadPresetOverridesDescriptors", "overridesEnv", "loadPresetOverridesEnvDescriptors", "createLogger", "makeWeakCacheSync", "buildRootDescriptors", "alias", "createUncachedDescriptors", "makeStrongCacheSync", "buildEnvDescriptors", "buildOverrideDescriptors", "buildOverrideEnvDescriptors", "buildRootChain", "opts", "configReport", "babelRcReport", "programmaticLogger", "ConfigPrinter", "programmatic<PERSON><PERSON><PERSON>", "loadProgrammaticChain", "dirname", "cwd", "undefined", "programmaticReport", "output", "configFile", "loadConfig", "caller", "findRootConfig", "babelrc", "babelrcRoots", "babelrcRootsDirectory", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "validatedFile", "validateConfigFile", "result", "loadFileChain", "mergeChain", "ignoreFile", "babelrcFile", "isIgnored", "file<PERSON>hain", "filename", "pkgData", "findPackageData", "babelrcLoadEnabled", "ignore", "config", "findRelativeConfig", "add", "filepath", "shouldIgnore", "validateBabelrcFile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showConfig", "console", "log", "filter", "x", "join", "fileHandling", "absoluteRoot", "directories", "indexOf", "babelrcPatterns", "Array", "isArray", "pat", "path", "resolve", "length", "some", "pathPatternToRegex", "directory", "matchPattern", "file", "validate", "validateExtendFile", "input", "createCachedDescriptors", "baseLogger", "buildProgrammaticLogger", "loadFile<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadFileDescriptors", "loadFileEnvDescriptors", "loadFileOverridesDescriptors", "loadFileOverridesEnvDescriptors", "buildFileLogger", "configure", "ChainFormatter", "Config", "descriptors", "_", "Programmatic", "callerName", "name", "Error", "override", "<PERSON><PERSON><PERSON><PERSON>", "flattenedConfigs", "rootOpts", "configIsApplicable", "push", "envOpts", "for<PERSON>ach", "overrideOps", "overrideEnvOpts", "only", "logger", "mergeExtendsChain", "mergeChainOpts", "extends", "has", "from", "delete", "target", "source", "passPerPreset", "test", "include", "exclude", "Object", "prototype", "hasOwnProperty", "call", "sourceMaps", "sourceMap", "items", "Map", "item", "value", "fnKey", "nameMap", "get", "set", "desc", "ownPass", "reduce", "acc", "config<PERSON><PERSON>", "configFieldIsApplicable", "patterns", "matchesPatterns", "ignoreListReplacer", "_key", "RegExp", "String", "message", "JSON", "stringify", "pattern", "pathToTest", "endHiddenCallStack", "ConfigError"], "sources": ["../../src/config/config-chain.ts"], "sourcesContent": ["import path from \"path\";\nimport buildDebug from \"debug\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { validate } from \"./validation/options\";\nimport type {\n  ValidatedOptions,\n  IgnoreList,\n  ConfigApplicableTest,\n  BabelrcSearch,\n  CallerMetadata,\n  IgnoreItem,\n} from \"./validation/options\";\nimport pathPatternToRegex from \"./pattern-to-regex\";\nimport { ConfigPrinter, ChainFormatter } from \"./printer\";\nimport type { ReadonlyDeepArray } from \"./helpers/deep-array\";\n\nimport { endHiddenCallStack } from \"../errors/rewrite-stack-trace\";\nimport ConfigError from \"../errors/config-error\";\n\nconst debug = buildDebug(\"babel:config:config-chain\");\n\nimport {\n  findPackageData,\n  findRelativeConfig,\n  findRootConfig,\n  loadConfig,\n} from \"./files\";\nimport type { ConfigFile, IgnoreFile, FilePackageData } from \"./files\";\n\nimport { makeWeakCacheSync, makeStrongCacheSync } from \"./caching\";\n\nimport {\n  createCachedDescriptors,\n  createUncachedDescriptors,\n} from \"./config-descriptors\";\nimport type {\n  UnloadedDescriptor,\n  OptionsAndDescriptors,\n  ValidatedFile,\n} from \"./config-descriptors\";\n\nexport type ConfigChain = {\n  plugins: Array<UnloadedDescriptor>;\n  presets: Array<UnloadedDescriptor>;\n  options: Array<ValidatedOptions>;\n  files: Set<string>;\n};\n\nexport type PresetInstance = {\n  options: ValidatedOptions;\n  alias: string;\n  dirname: string;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type ConfigContext = {\n  filename: string | undefined;\n  cwd: string;\n  root: string;\n  envName: string;\n  caller: CallerMetadata | undefined;\n  showConfig: boolean;\n};\n\n/**\n * Build a config chain for a given preset.\n */\nexport function* buildPresetChain(\n  arg: PresetInstance,\n  context: any,\n): Handler<ConfigChain | null> {\n  const chain = yield* buildPresetChainWalker(arg, context);\n  if (!chain) return null;\n\n  return {\n    plugins: dedupDescriptors(chain.plugins),\n    presets: dedupDescriptors(chain.presets),\n    options: chain.options.map(o => normalizeOptions(o)),\n    files: new Set(),\n  };\n}\n\nexport const buildPresetChainWalker = makeChainWalker<PresetInstance>({\n  root: preset => loadPresetDescriptors(preset),\n  env: (preset, envName) => loadPresetEnvDescriptors(preset)(envName),\n  overrides: (preset, index) => loadPresetOverridesDescriptors(preset)(index),\n  overridesEnv: (preset, index, envName) =>\n    loadPresetOverridesEnvDescriptors(preset)(index)(envName),\n  createLogger: () => () => {}, // Currently we don't support logging how preset is expanded\n});\nconst loadPresetDescriptors = makeWeakCacheSync((preset: PresetInstance) =>\n  buildRootDescriptors(preset, preset.alias, createUncachedDescriptors),\n);\nconst loadPresetEnvDescriptors = makeWeakCacheSync((preset: PresetInstance) =>\n  makeStrongCacheSync((envName: string) =>\n    buildEnvDescriptors(\n      preset,\n      preset.alias,\n      createUncachedDescriptors,\n      envName,\n    ),\n  ),\n);\nconst loadPresetOverridesDescriptors = makeWeakCacheSync(\n  (preset: PresetInstance) =>\n    makeStrongCacheSync((index: number) =>\n      buildOverrideDescriptors(\n        preset,\n        preset.alias,\n        createUncachedDescriptors,\n        index,\n      ),\n    ),\n);\nconst loadPresetOverridesEnvDescriptors = makeWeakCacheSync(\n  (preset: PresetInstance) =>\n    makeStrongCacheSync((index: number) =>\n      makeStrongCacheSync((envName: string) =>\n        buildOverrideEnvDescriptors(\n          preset,\n          preset.alias,\n          createUncachedDescriptors,\n          index,\n          envName,\n        ),\n      ),\n    ),\n);\n\nexport type FileHandling = \"transpile\" | \"ignored\" | \"unsupported\";\nexport type RootConfigChain = ConfigChain & {\n  babelrc: ConfigFile | void;\n  config: ConfigFile | void;\n  ignore: IgnoreFile | void;\n  fileHandling: FileHandling;\n  files: Set<string>;\n};\n\n/**\n * Build a config chain for Babel's full root configuration.\n */\nexport function* buildRootChain(\n  opts: ValidatedOptions,\n  context: ConfigContext,\n): Handler<RootConfigChain | null> {\n  let configReport, babelRcReport;\n  const programmaticLogger = new ConfigPrinter();\n  const programmaticChain = yield* loadProgrammaticChain(\n    {\n      options: opts,\n      dirname: context.cwd,\n    },\n    context,\n    undefined,\n    programmaticLogger,\n  );\n  if (!programmaticChain) return null;\n  const programmaticReport = yield* programmaticLogger.output();\n\n  let configFile;\n  if (typeof opts.configFile === \"string\") {\n    configFile = yield* loadConfig(\n      opts.configFile,\n      context.cwd,\n      context.envName,\n      context.caller,\n    );\n  } else if (opts.configFile !== false) {\n    configFile = yield* findRootConfig(\n      context.root,\n      context.envName,\n      context.caller,\n    );\n  }\n\n  let { babelrc, babelrcRoots } = opts;\n  let babelrcRootsDirectory = context.cwd;\n\n  const configFileChain = emptyChain();\n  const configFileLogger = new ConfigPrinter();\n  if (configFile) {\n    const validatedFile = validateConfigFile(configFile);\n    const result = yield* loadFileChain(\n      validatedFile,\n      context,\n      undefined,\n      configFileLogger,\n    );\n    if (!result) return null;\n    configReport = yield* configFileLogger.output();\n\n    // Allow config files to toggle `.babelrc` resolution on and off and\n    // specify where the roots are.\n    if (babelrc === undefined) {\n      babelrc = validatedFile.options.babelrc;\n    }\n    if (babelrcRoots === undefined) {\n      babelrcRootsDirectory = validatedFile.dirname;\n      babelrcRoots = validatedFile.options.babelrcRoots;\n    }\n\n    mergeChain(configFileChain, result);\n  }\n\n  let ignoreFile, babelrcFile;\n  let isIgnored = false;\n  const fileChain = emptyChain();\n  // resolve all .babelrc files\n  if (\n    (babelrc === true || babelrc === undefined) &&\n    typeof context.filename === \"string\"\n  ) {\n    const pkgData = yield* findPackageData(context.filename);\n\n    if (\n      pkgData &&\n      babelrcLoadEnabled(context, pkgData, babelrcRoots, babelrcRootsDirectory)\n    ) {\n      ({ ignore: ignoreFile, config: babelrcFile } = yield* findRelativeConfig(\n        pkgData,\n        context.envName,\n        context.caller,\n      ));\n\n      if (ignoreFile) {\n        fileChain.files.add(ignoreFile.filepath);\n      }\n\n      if (\n        ignoreFile &&\n        shouldIgnore(context, ignoreFile.ignore, null, ignoreFile.dirname)\n      ) {\n        isIgnored = true;\n      }\n\n      if (babelrcFile && !isIgnored) {\n        const validatedFile = validateBabelrcFile(babelrcFile);\n        const babelrcLogger = new ConfigPrinter();\n        const result = yield* loadFileChain(\n          validatedFile,\n          context,\n          undefined,\n          babelrcLogger,\n        );\n        if (!result) {\n          isIgnored = true;\n        } else {\n          babelRcReport = yield* babelrcLogger.output();\n          mergeChain(fileChain, result);\n        }\n      }\n\n      if (babelrcFile && isIgnored) {\n        fileChain.files.add(babelrcFile.filepath);\n      }\n    }\n  }\n\n  if (context.showConfig) {\n    console.log(\n      `Babel configs on \"${context.filename}\" (ascending priority):\\n` +\n        // print config by the order of ascending priority\n        [configReport, babelRcReport, programmaticReport]\n          .filter(x => !!x)\n          .join(\"\\n\\n\") +\n        \"\\n-----End Babel configs-----\",\n    );\n  }\n  // Insert file chain in front so programmatic options have priority\n  // over configuration file chain items.\n  const chain = mergeChain(\n    mergeChain(mergeChain(emptyChain(), configFileChain), fileChain),\n    programmaticChain,\n  );\n\n  return {\n    plugins: isIgnored ? [] : dedupDescriptors(chain.plugins),\n    presets: isIgnored ? [] : dedupDescriptors(chain.presets),\n    options: isIgnored ? [] : chain.options.map(o => normalizeOptions(o)),\n    fileHandling: isIgnored ? \"ignored\" : \"transpile\",\n    ignore: ignoreFile || undefined,\n    babelrc: babelrcFile || undefined,\n    config: configFile || undefined,\n    files: chain.files,\n  };\n}\n\nfunction babelrcLoadEnabled(\n  context: ConfigContext,\n  pkgData: FilePackageData,\n  babelrcRoots: BabelrcSearch | undefined,\n  babelrcRootsDirectory: string,\n): boolean {\n  if (typeof babelrcRoots === \"boolean\") return babelrcRoots;\n\n  const absoluteRoot = context.root;\n\n  // Fast path to avoid having to match patterns if the babelrc is just\n  // loading in the standard root directory.\n  if (babelrcRoots === undefined) {\n    return pkgData.directories.indexOf(absoluteRoot) !== -1;\n  }\n\n  let babelrcPatterns = babelrcRoots;\n  if (!Array.isArray(babelrcPatterns)) {\n    babelrcPatterns = [babelrcPatterns as IgnoreItem];\n  }\n  babelrcPatterns = babelrcPatterns.map(pat => {\n    return typeof pat === \"string\"\n      ? path.resolve(babelrcRootsDirectory, pat)\n      : pat;\n  });\n\n  // Fast path to avoid having to match patterns if the babelrc is just\n  // loading in the standard root directory.\n  if (babelrcPatterns.length === 1 && babelrcPatterns[0] === absoluteRoot) {\n    return pkgData.directories.indexOf(absoluteRoot) !== -1;\n  }\n\n  return babelrcPatterns.some(pat => {\n    if (typeof pat === \"string\") {\n      pat = pathPatternToRegex(pat, babelrcRootsDirectory);\n    }\n\n    return pkgData.directories.some(directory => {\n      return matchPattern(pat, babelrcRootsDirectory, directory, context);\n    });\n  });\n}\n\nconst validateConfigFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"configfile\", file.options, file.filepath),\n  }),\n);\n\nconst validateBabelrcFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"babelrcfile\", file.options, file.filepath),\n  }),\n);\n\nconst validateExtendFile = makeWeakCacheSync(\n  (file: ConfigFile): ValidatedFile => ({\n    filepath: file.filepath,\n    dirname: file.dirname,\n    options: validate(\"extendsfile\", file.options, file.filepath),\n  }),\n);\n\n/**\n * Build a config chain for just the programmatic options passed into Babel.\n */\nconst loadProgrammaticChain = makeChainWalker({\n  root: input => buildRootDescriptors(input, \"base\", createCachedDescriptors),\n  env: (input, envName) =>\n    buildEnvDescriptors(input, \"base\", createCachedDescriptors, envName),\n  overrides: (input, index) =>\n    buildOverrideDescriptors(input, \"base\", createCachedDescriptors, index),\n  overridesEnv: (input, index, envName) =>\n    buildOverrideEnvDescriptors(\n      input,\n      \"base\",\n      createCachedDescriptors,\n      index,\n      envName,\n    ),\n  createLogger: (input, context, baseLogger) =>\n    buildProgrammaticLogger(input, context, baseLogger),\n});\n\n/**\n * Build a config chain for a given file.\n */\nconst loadFileChainWalker = makeChainWalker<ValidatedFile>({\n  root: file => loadFileDescriptors(file),\n  env: (file, envName) => loadFileEnvDescriptors(file)(envName),\n  overrides: (file, index) => loadFileOverridesDescriptors(file)(index),\n  overridesEnv: (file, index, envName) =>\n    loadFileOverridesEnvDescriptors(file)(index)(envName),\n  createLogger: (file, context, baseLogger) =>\n    buildFileLogger(file.filepath, context, baseLogger),\n});\n\nfunction* loadFileChain(\n  input: ValidatedFile,\n  context: ConfigContext,\n  files: Set<ConfigFile>,\n  baseLogger: ConfigPrinter,\n) {\n  const chain = yield* loadFileChainWalker(input, context, files, baseLogger);\n  if (chain) {\n    chain.files.add(input.filepath);\n  }\n\n  return chain;\n}\n\nconst loadFileDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  buildRootDescriptors(file, file.filepath, createUncachedDescriptors),\n);\nconst loadFileEnvDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  makeStrongCacheSync((envName: string) =>\n    buildEnvDescriptors(\n      file,\n      file.filepath,\n      createUncachedDescriptors,\n      envName,\n    ),\n  ),\n);\nconst loadFileOverridesDescriptors = makeWeakCacheSync((file: ValidatedFile) =>\n  makeStrongCacheSync((index: number) =>\n    buildOverrideDescriptors(\n      file,\n      file.filepath,\n      createUncachedDescriptors,\n      index,\n    ),\n  ),\n);\nconst loadFileOverridesEnvDescriptors = makeWeakCacheSync(\n  (file: ValidatedFile) =>\n    makeStrongCacheSync((index: number) =>\n      makeStrongCacheSync((envName: string) =>\n        buildOverrideEnvDescriptors(\n          file,\n          file.filepath,\n          createUncachedDescriptors,\n          index,\n          envName,\n        ),\n      ),\n    ),\n);\n\nfunction buildFileLogger(\n  filepath: string,\n  context: ConfigContext,\n  baseLogger: ConfigPrinter | void,\n) {\n  if (!baseLogger) {\n    return () => {};\n  }\n  return baseLogger.configure(context.showConfig, ChainFormatter.Config, {\n    filepath,\n  });\n}\n\nfunction buildRootDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n) {\n  return descriptors(dirname, options, alias);\n}\n\nfunction buildProgrammaticLogger(\n  _: unknown,\n  context: ConfigContext,\n  baseLogger: ConfigPrinter | void,\n) {\n  if (!baseLogger) {\n    return () => {};\n  }\n  return baseLogger.configure(context.showConfig, ChainFormatter.Programmatic, {\n    callerName: context.caller?.name,\n  });\n}\n\nfunction buildEnvDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  envName: string,\n) {\n  const opts = options.env && options.env[envName];\n  return opts ? descriptors(dirname, opts, `${alias}.env[\"${envName}\"]`) : null;\n}\n\nfunction buildOverrideDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  index: number,\n) {\n  const opts = options.overrides && options.overrides[index];\n  if (!opts) throw new Error(\"Assertion failure - missing override\");\n\n  return descriptors(dirname, opts, `${alias}.overrides[${index}]`);\n}\n\nfunction buildOverrideEnvDescriptors(\n  { dirname, options }: Partial<ValidatedFile>,\n  alias: string,\n  descriptors: (\n    dirname: string,\n    options: ValidatedOptions,\n    alias: string,\n  ) => OptionsAndDescriptors,\n  index: number,\n  envName: string,\n) {\n  const override = options.overrides && options.overrides[index];\n  if (!override) throw new Error(\"Assertion failure - missing override\");\n\n  const opts = override.env && override.env[envName];\n  return opts\n    ? descriptors(\n        dirname,\n        opts,\n        `${alias}.overrides[${index}].env[\"${envName}\"]`,\n      )\n    : null;\n}\n\nfunction makeChainWalker<\n  ArgT extends {\n    options: ValidatedOptions;\n    dirname: string;\n    filepath?: string;\n  },\n>({\n  root,\n  env,\n  overrides,\n  overridesEnv,\n  createLogger,\n}: {\n  root: (configEntry: ArgT) => OptionsAndDescriptors;\n  env: (configEntry: ArgT, env: string) => OptionsAndDescriptors | null;\n  overrides: (configEntry: ArgT, index: number) => OptionsAndDescriptors;\n  overridesEnv: (\n    configEntry: ArgT,\n    index: number,\n    env: string,\n  ) => OptionsAndDescriptors | null;\n  createLogger: (\n    configEntry: ArgT,\n    context: ConfigContext,\n    printer: ConfigPrinter | void,\n  ) => (\n    opts: OptionsAndDescriptors,\n    index?: number | null,\n    env?: string | null,\n  ) => void;\n}): (\n  configEntry: ArgT,\n  context: ConfigContext,\n  files?: Set<ConfigFile>,\n  baseLogger?: ConfigPrinter,\n) => Handler<ConfigChain | null> {\n  return function* chainWalker(input, context, files = new Set(), baseLogger) {\n    const { dirname } = input;\n\n    const flattenedConfigs: Array<{\n      config: OptionsAndDescriptors;\n      index: number | undefined | null;\n      envName: string | undefined | null;\n    }> = [];\n\n    const rootOpts = root(input);\n    if (configIsApplicable(rootOpts, dirname, context, input.filepath)) {\n      flattenedConfigs.push({\n        config: rootOpts,\n        envName: undefined,\n        index: undefined,\n      });\n\n      const envOpts = env(input, context.envName);\n      if (\n        envOpts &&\n        configIsApplicable(envOpts, dirname, context, input.filepath)\n      ) {\n        flattenedConfigs.push({\n          config: envOpts,\n          envName: context.envName,\n          index: undefined,\n        });\n      }\n\n      (rootOpts.options.overrides || []).forEach((_, index) => {\n        const overrideOps = overrides(input, index);\n        if (configIsApplicable(overrideOps, dirname, context, input.filepath)) {\n          flattenedConfigs.push({\n            config: overrideOps,\n            index,\n            envName: undefined,\n          });\n\n          const overrideEnvOpts = overridesEnv(input, index, context.envName);\n          if (\n            overrideEnvOpts &&\n            configIsApplicable(\n              overrideEnvOpts,\n              dirname,\n              context,\n              input.filepath,\n            )\n          ) {\n            flattenedConfigs.push({\n              config: overrideEnvOpts,\n              index,\n              envName: context.envName,\n            });\n          }\n        }\n      });\n    }\n\n    // Process 'ignore' and 'only' before 'extends' items are processed so\n    // that we don't do extra work loading extended configs if a file is\n    // ignored.\n    if (\n      flattenedConfigs.some(\n        ({\n          config: {\n            options: { ignore, only },\n          },\n        }) => shouldIgnore(context, ignore, only, dirname),\n      )\n    ) {\n      return null;\n    }\n\n    const chain = emptyChain();\n    const logger = createLogger(input, context, baseLogger);\n\n    for (const { config, index, envName } of flattenedConfigs) {\n      if (\n        !(yield* mergeExtendsChain(\n          chain,\n          config.options,\n          dirname,\n          context,\n          files,\n          baseLogger,\n        ))\n      ) {\n        return null;\n      }\n\n      logger(config, index, envName);\n      yield* mergeChainOpts(chain, config);\n    }\n    return chain;\n  };\n}\n\nfunction* mergeExtendsChain(\n  chain: ConfigChain,\n  opts: ValidatedOptions,\n  dirname: string,\n  context: ConfigContext,\n  files: Set<ConfigFile>,\n  baseLogger?: ConfigPrinter,\n): Handler<boolean> {\n  if (opts.extends === undefined) return true;\n\n  const file = yield* loadConfig(\n    opts.extends,\n    dirname,\n    context.envName,\n    context.caller,\n  );\n\n  if (files.has(file)) {\n    throw new Error(\n      `Configuration cycle detected loading ${file.filepath}.\\n` +\n        `File already loaded following the config chain:\\n` +\n        Array.from(files, file => ` - ${file.filepath}`).join(\"\\n\"),\n    );\n  }\n\n  files.add(file);\n  const fileChain = yield* loadFileChain(\n    validateExtendFile(file),\n    context,\n    files,\n    baseLogger,\n  );\n  files.delete(file);\n\n  if (!fileChain) return false;\n\n  mergeChain(chain, fileChain);\n\n  return true;\n}\n\nfunction mergeChain(target: ConfigChain, source: ConfigChain): ConfigChain {\n  target.options.push(...source.options);\n  target.plugins.push(...source.plugins);\n  target.presets.push(...source.presets);\n  for (const file of source.files) {\n    target.files.add(file);\n  }\n\n  return target;\n}\n\nfunction* mergeChainOpts(\n  target: ConfigChain,\n  { options, plugins, presets }: OptionsAndDescriptors,\n): Handler<ConfigChain> {\n  target.options.push(options);\n  target.plugins.push(...(yield* plugins()));\n  target.presets.push(...(yield* presets()));\n\n  return target;\n}\n\nfunction emptyChain(): ConfigChain {\n  return {\n    options: [],\n    presets: [],\n    plugins: [],\n    files: new Set(),\n  };\n}\n\nfunction normalizeOptions(opts: ValidatedOptions): ValidatedOptions {\n  const options = {\n    ...opts,\n  };\n  delete options.extends;\n  delete options.env;\n  delete options.overrides;\n  delete options.plugins;\n  delete options.presets;\n  delete options.passPerPreset;\n  delete options.ignore;\n  delete options.only;\n  delete options.test;\n  delete options.include;\n  delete options.exclude;\n\n  // \"sourceMap\" is just aliased to sourceMap, so copy it over as\n  // we merge the options together.\n  if (Object.prototype.hasOwnProperty.call(options, \"sourceMap\")) {\n    options.sourceMaps = options.sourceMap;\n    delete options.sourceMap;\n  }\n  return options;\n}\n\nfunction dedupDescriptors(\n  items: Array<UnloadedDescriptor>,\n): Array<UnloadedDescriptor> {\n  const map: Map<\n    Function,\n    Map<string | void, { value: UnloadedDescriptor }>\n  > = new Map();\n\n  const descriptors = [];\n\n  for (const item of items) {\n    if (typeof item.value === \"function\") {\n      const fnKey = item.value;\n      let nameMap = map.get(fnKey);\n      if (!nameMap) {\n        nameMap = new Map();\n        map.set(fnKey, nameMap);\n      }\n      let desc = nameMap.get(item.name);\n      if (!desc) {\n        desc = { value: item };\n        descriptors.push(desc);\n\n        // Treat passPerPreset presets as unique, skipping them\n        // in the merge processing steps.\n        if (!item.ownPass) nameMap.set(item.name, desc);\n      } else {\n        desc.value = item;\n      }\n    } else {\n      descriptors.push({ value: item });\n    }\n  }\n\n  return descriptors.reduce((acc, desc) => {\n    acc.push(desc.value);\n    return acc;\n  }, []);\n}\n\nfunction configIsApplicable(\n  { options }: OptionsAndDescriptors,\n  dirname: string,\n  context: ConfigContext,\n  configName: string,\n): boolean {\n  return (\n    (options.test === undefined ||\n      configFieldIsApplicable(context, options.test, dirname, configName)) &&\n    (options.include === undefined ||\n      configFieldIsApplicable(context, options.include, dirname, configName)) &&\n    (options.exclude === undefined ||\n      !configFieldIsApplicable(context, options.exclude, dirname, configName))\n  );\n}\n\nfunction configFieldIsApplicable(\n  context: ConfigContext,\n  test: ConfigApplicableTest,\n  dirname: string,\n  configName: string,\n): boolean {\n  const patterns = Array.isArray(test) ? test : [test];\n\n  return matchesPatterns(context, patterns, dirname, configName);\n}\n\n/**\n * Print the ignoreList-values in a more helpful way than the default.\n */\nfunction ignoreListReplacer(\n  _key: string,\n  value: IgnoreList | IgnoreItem,\n): IgnoreList | IgnoreItem | string {\n  if (value instanceof RegExp) {\n    return String(value);\n  }\n\n  return value;\n}\n\n/**\n * Tests if a filename should be ignored based on \"ignore\" and \"only\" options.\n */\nfunction shouldIgnore(\n  context: ConfigContext,\n  ignore: IgnoreList | undefined | null,\n  only: IgnoreList | undefined | null,\n  dirname: string,\n): boolean {\n  if (ignore && matchesPatterns(context, ignore, dirname)) {\n    const message = `No config is applied to \"${\n      context.filename ?? \"(unknown)\"\n    }\" because it matches one of \\`ignore: ${JSON.stringify(\n      ignore,\n      ignoreListReplacer,\n    )}\\` from \"${dirname}\"`;\n    debug(message);\n    if (context.showConfig) {\n      console.log(message);\n    }\n    return true;\n  }\n\n  if (only && !matchesPatterns(context, only, dirname)) {\n    const message = `No config is applied to \"${\n      context.filename ?? \"(unknown)\"\n    }\" because it fails to match one of \\`only: ${JSON.stringify(\n      only,\n      ignoreListReplacer,\n    )}\\` from \"${dirname}\"`;\n    debug(message);\n    if (context.showConfig) {\n      console.log(message);\n    }\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Returns result of calling function with filename if pattern is a function.\n * Otherwise returns result of matching pattern Regex with filename.\n */\nfunction matchesPatterns(\n  context: ConfigContext,\n  patterns: IgnoreList,\n  dirname: string,\n  configName?: string,\n): boolean {\n  return patterns.some(pattern =>\n    matchPattern(pattern, dirname, context.filename, context, configName),\n  );\n}\n\nfunction matchPattern(\n  pattern: IgnoreItem,\n  dirname: string,\n  pathToTest: string | undefined,\n  context: ConfigContext,\n  configName?: string,\n): boolean {\n  if (typeof pattern === \"function\") {\n    return !!endHiddenCallStack(pattern)(pathToTest, {\n      dirname,\n      envName: context.envName,\n      caller: context.caller,\n    });\n  }\n\n  if (typeof pathToTest !== \"string\") {\n    throw new ConfigError(\n      `Configuration contains string/RegExp pattern, but no filename was passed to Babel`,\n      configName,\n    );\n  }\n\n  if (typeof pattern === \"string\") {\n    pattern = pathPatternToRegex(pattern, dirname);\n  }\n  return pattern.test(pathToTest);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AASA;;AACA;;AAGA;;AACA;;AAIA;;AAQA;;AAEA;;AAZA,MAAMA,KAAK,GAAGC,QAAA,CAAW,2BAAX,CAAd;;AAgDO,UAAUC,gBAAV,CACLC,GADK,EAELC,OAFK,EAGwB;EAC7B,MAAMC,KAAK,GAAG,OAAOC,sBAAsB,CAACH,GAAD,EAAMC,OAAN,CAA3C;EACA,IAAI,CAACC,KAAL,EAAY,OAAO,IAAP;EAEZ,OAAO;IACLE,OAAO,EAAEC,gBAAgB,CAACH,KAAK,CAACE,OAAP,CADpB;IAELE,OAAO,EAAED,gBAAgB,CAACH,KAAK,CAACI,OAAP,CAFpB;IAGLC,OAAO,EAAEL,KAAK,CAACK,OAAN,CAAcC,GAAd,CAAkBC,CAAC,IAAIC,gBAAgB,CAACD,CAAD,CAAvC,CAHJ;IAILE,KAAK,EAAE,IAAIC,GAAJ;EAJF,CAAP;AAMD;;AAEM,MAAMT,sBAAsB,GAAGU,eAAe,CAAiB;EACpEC,IAAI,EAAEC,MAAM,IAAIC,qBAAqB,CAACD,MAAD,CAD+B;EAEpEE,GAAG,EAAE,CAACF,MAAD,EAASG,OAAT,KAAqBC,wBAAwB,CAACJ,MAAD,CAAxB,CAAiCG,OAAjC,CAF0C;EAGpEE,SAAS,EAAE,CAACL,MAAD,EAASM,KAAT,KAAmBC,8BAA8B,CAACP,MAAD,CAA9B,CAAuCM,KAAvC,CAHsC;EAIpEE,YAAY,EAAE,CAACR,MAAD,EAASM,KAAT,EAAgBH,OAAhB,KACZM,iCAAiC,CAACT,MAAD,CAAjC,CAA0CM,KAA1C,EAAiDH,OAAjD,CALkE;EAMpEO,YAAY,EAAE,MAAM,MAAM,CAAE;AANwC,CAAjB,CAA9C;;AAQP,MAAMT,qBAAqB,GAAG,IAAAU,0BAAA,EAAmBX,MAAD,IAC9CY,oBAAoB,CAACZ,MAAD,EAASA,MAAM,CAACa,KAAhB,EAAuBC,4CAAvB,CADQ,CAA9B;AAGA,MAAMV,wBAAwB,GAAG,IAAAO,0BAAA,EAAmBX,MAAD,IACjD,IAAAe,4BAAA,EAAqBZ,OAAD,IAClBa,mBAAmB,CACjBhB,MADiB,EAEjBA,MAAM,CAACa,KAFU,EAGjBC,4CAHiB,EAIjBX,OAJiB,CADrB,CAD+B,CAAjC;AAUA,MAAMI,8BAA8B,GAAG,IAAAI,0BAAA,EACpCX,MAAD,IACE,IAAAe,4BAAA,EAAqBT,KAAD,IAClBW,wBAAwB,CACtBjB,MADsB,EAEtBA,MAAM,CAACa,KAFe,EAGtBC,4CAHsB,EAItBR,KAJsB,CAD1B,CAFmC,CAAvC;AAWA,MAAMG,iCAAiC,GAAG,IAAAE,0BAAA,EACvCX,MAAD,IACE,IAAAe,4BAAA,EAAqBT,KAAD,IAClB,IAAAS,4BAAA,EAAqBZ,OAAD,IAClBe,2BAA2B,CACzBlB,MADyB,EAEzBA,MAAM,CAACa,KAFkB,EAGzBC,4CAHyB,EAIzBR,KAJyB,EAKzBH,OALyB,CAD7B,CADF,CAFsC,CAA1C;;AA2BO,UAAUgB,cAAV,CACLC,IADK,EAELlC,OAFK,EAG4B;EACjC,IAAImC,YAAJ,EAAkBC,aAAlB;EACA,MAAMC,kBAAkB,GAAG,IAAIC,sBAAJ,EAA3B;EACA,MAAMC,iBAAiB,GAAG,OAAOC,qBAAqB,CACpD;IACElC,OAAO,EAAE4B,IADX;IAEEO,OAAO,EAAEzC,OAAO,CAAC0C;EAFnB,CADoD,EAKpD1C,OALoD,EAMpD2C,SANoD,EAOpDN,kBAPoD,CAAtD;EASA,IAAI,CAACE,iBAAL,EAAwB,OAAO,IAAP;EACxB,MAAMK,kBAAkB,GAAG,OAAOP,kBAAkB,CAACQ,MAAnB,EAAlC;EAEA,IAAIC,UAAJ;;EACA,IAAI,OAAOZ,IAAI,CAACY,UAAZ,KAA2B,QAA/B,EAAyC;IACvCA,UAAU,GAAG,OAAO,IAAAC,iBAAA,EAClBb,IAAI,CAACY,UADa,EAElB9C,OAAO,CAAC0C,GAFU,EAGlB1C,OAAO,CAACiB,OAHU,EAIlBjB,OAAO,CAACgD,MAJU,CAApB;EAMD,CAPD,MAOO,IAAId,IAAI,CAACY,UAAL,KAAoB,KAAxB,EAA+B;IACpCA,UAAU,GAAG,OAAO,IAAAG,qBAAA,EAClBjD,OAAO,CAACa,IADU,EAElBb,OAAO,CAACiB,OAFU,EAGlBjB,OAAO,CAACgD,MAHU,CAApB;EAKD;;EAED,IAAI;IAAEE,OAAF;IAAWC;EAAX,IAA4BjB,IAAhC;EACA,IAAIkB,qBAAqB,GAAGpD,OAAO,CAAC0C,GAApC;EAEA,MAAMW,eAAe,GAAGC,UAAU,EAAlC;EACA,MAAMC,gBAAgB,GAAG,IAAIjB,sBAAJ,EAAzB;;EACA,IAAIQ,UAAJ,EAAgB;IACd,MAAMU,aAAa,GAAGC,kBAAkB,CAACX,UAAD,CAAxC;IACA,MAAMY,MAAM,GAAG,OAAOC,aAAa,CACjCH,aADiC,EAEjCxD,OAFiC,EAGjC2C,SAHiC,EAIjCY,gBAJiC,CAAnC;IAMA,IAAI,CAACG,MAAL,EAAa,OAAO,IAAP;IACbvB,YAAY,GAAG,OAAOoB,gBAAgB,CAACV,MAAjB,EAAtB;;IAIA,IAAIK,OAAO,KAAKP,SAAhB,EAA2B;MACzBO,OAAO,GAAGM,aAAa,CAAClD,OAAd,CAAsB4C,OAAhC;IACD;;IACD,IAAIC,YAAY,KAAKR,SAArB,EAAgC;MAC9BS,qBAAqB,GAAGI,aAAa,CAACf,OAAtC;MACAU,YAAY,GAAGK,aAAa,CAAClD,OAAd,CAAsB6C,YAArC;IACD;;IAEDS,UAAU,CAACP,eAAD,EAAkBK,MAAlB,CAAV;EACD;;EAED,IAAIG,UAAJ,EAAgBC,WAAhB;EACA,IAAIC,SAAS,GAAG,KAAhB;EACA,MAAMC,SAAS,GAAGV,UAAU,EAA5B;;EAEA,IACE,CAACJ,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAKP,SAAjC,KACA,OAAO3C,OAAO,CAACiE,QAAf,KAA4B,QAF9B,EAGE;IACA,MAAMC,OAAO,GAAG,OAAO,IAAAC,sBAAA,EAAgBnE,OAAO,CAACiE,QAAxB,CAAvB;;IAEA,IACEC,OAAO,IACPE,kBAAkB,CAACpE,OAAD,EAAUkE,OAAV,EAAmBf,YAAnB,EAAiCC,qBAAjC,CAFpB,EAGE;MACA,CAAC;QAAEiB,MAAM,EAAER,UAAV;QAAsBS,MAAM,EAAER;MAA9B,IAA8C,OAAO,IAAAS,yBAAA,EACpDL,OADoD,EAEpDlE,OAAO,CAACiB,OAF4C,EAGpDjB,OAAO,CAACgD,MAH4C,CAAtD;;MAMA,IAAIa,UAAJ,EAAgB;QACdG,SAAS,CAACtD,KAAV,CAAgB8D,GAAhB,CAAoBX,UAAU,CAACY,QAA/B;MACD;;MAED,IACEZ,UAAU,IACVa,YAAY,CAAC1E,OAAD,EAAU6D,UAAU,CAACQ,MAArB,EAA6B,IAA7B,EAAmCR,UAAU,CAACpB,OAA9C,CAFd,EAGE;QACAsB,SAAS,GAAG,IAAZ;MACD;;MAED,IAAID,WAAW,IAAI,CAACC,SAApB,EAA+B;QAC7B,MAAMP,aAAa,GAAGmB,mBAAmB,CAACb,WAAD,CAAzC;QACA,MAAMc,aAAa,GAAG,IAAItC,sBAAJ,EAAtB;QACA,MAAMoB,MAAM,GAAG,OAAOC,aAAa,CACjCH,aADiC,EAEjCxD,OAFiC,EAGjC2C,SAHiC,EAIjCiC,aAJiC,CAAnC;;QAMA,IAAI,CAAClB,MAAL,EAAa;UACXK,SAAS,GAAG,IAAZ;QACD,CAFD,MAEO;UACL3B,aAAa,GAAG,OAAOwC,aAAa,CAAC/B,MAAd,EAAvB;UACAe,UAAU,CAACI,SAAD,EAAYN,MAAZ,CAAV;QACD;MACF;;MAED,IAAII,WAAW,IAAIC,SAAnB,EAA8B;QAC5BC,SAAS,CAACtD,KAAV,CAAgB8D,GAAhB,CAAoBV,WAAW,CAACW,QAAhC;MACD;IACF;EACF;;EAED,IAAIzE,OAAO,CAAC6E,UAAZ,EAAwB;IACtBC,OAAO,CAACC,GAAR,CACG,qBAAoB/E,OAAO,CAACiE,QAAS,2BAAtC,GAEE,CAAC9B,YAAD,EAAeC,aAAf,EAA8BQ,kBAA9B,EACGoC,MADH,CACUC,CAAC,IAAI,CAAC,CAACA,CADjB,EAEGC,IAFH,CAEQ,MAFR,CAFF,GAKE,+BANJ;EAQD;;EAGD,MAAMjF,KAAK,GAAG2D,UAAU,CACtBA,UAAU,CAACA,UAAU,CAACN,UAAU,EAAX,EAAeD,eAAf,CAAX,EAA4CW,SAA5C,CADY,EAEtBzB,iBAFsB,CAAxB;EAKA,OAAO;IACLpC,OAAO,EAAE4D,SAAS,GAAG,EAAH,GAAQ3D,gBAAgB,CAACH,KAAK,CAACE,OAAP,CADrC;IAELE,OAAO,EAAE0D,SAAS,GAAG,EAAH,GAAQ3D,gBAAgB,CAACH,KAAK,CAACI,OAAP,CAFrC;IAGLC,OAAO,EAAEyD,SAAS,GAAG,EAAH,GAAQ9D,KAAK,CAACK,OAAN,CAAcC,GAAd,CAAkBC,CAAC,IAAIC,gBAAgB,CAACD,CAAD,CAAvC,CAHrB;IAIL2E,YAAY,EAAEpB,SAAS,GAAG,SAAH,GAAe,WAJjC;IAKLM,MAAM,EAAER,UAAU,IAAIlB,SALjB;IAMLO,OAAO,EAAEY,WAAW,IAAInB,SANnB;IAOL2B,MAAM,EAAExB,UAAU,IAAIH,SAPjB;IAQLjC,KAAK,EAAET,KAAK,CAACS;EARR,CAAP;AAUD;;AAED,SAAS0D,kBAAT,CACEpE,OADF,EAEEkE,OAFF,EAGEf,YAHF,EAIEC,qBAJF,EAKW;EACT,IAAI,OAAOD,YAAP,KAAwB,SAA5B,EAAuC,OAAOA,YAAP;EAEvC,MAAMiC,YAAY,GAAGpF,OAAO,CAACa,IAA7B;;EAIA,IAAIsC,YAAY,KAAKR,SAArB,EAAgC;IAC9B,OAAOuB,OAAO,CAACmB,WAAR,CAAoBC,OAApB,CAA4BF,YAA5B,MAA8C,CAAC,CAAtD;EACD;;EAED,IAAIG,eAAe,GAAGpC,YAAtB;;EACA,IAAI,CAACqC,KAAK,CAACC,OAAN,CAAcF,eAAd,CAAL,EAAqC;IACnCA,eAAe,GAAG,CAACA,eAAD,CAAlB;EACD;;EACDA,eAAe,GAAGA,eAAe,CAAChF,GAAhB,CAAoBmF,GAAG,IAAI;IAC3C,OAAO,OAAOA,GAAP,KAAe,QAAf,GACHC,OAAA,CAAKC,OAAL,CAAaxC,qBAAb,EAAoCsC,GAApC,CADG,GAEHA,GAFJ;EAGD,CAJiB,CAAlB;;EAQA,IAAIH,eAAe,CAACM,MAAhB,KAA2B,CAA3B,IAAgCN,eAAe,CAAC,CAAD,CAAf,KAAuBH,YAA3D,EAAyE;IACvE,OAAOlB,OAAO,CAACmB,WAAR,CAAoBC,OAApB,CAA4BF,YAA5B,MAA8C,CAAC,CAAtD;EACD;;EAED,OAAOG,eAAe,CAACO,IAAhB,CAAqBJ,GAAG,IAAI;IACjC,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;MAC3BA,GAAG,GAAG,IAAAK,uBAAA,EAAmBL,GAAnB,EAAwBtC,qBAAxB,CAAN;IACD;;IAED,OAAOc,OAAO,CAACmB,WAAR,CAAoBS,IAApB,CAAyBE,SAAS,IAAI;MAC3C,OAAOC,YAAY,CAACP,GAAD,EAAMtC,qBAAN,EAA6B4C,SAA7B,EAAwChG,OAAxC,CAAnB;IACD,CAFM,CAAP;EAGD,CARM,CAAP;AASD;;AAED,MAAMyD,kBAAkB,GAAG,IAAAhC,0BAAA,EACxByE,IAAD,KAAsC;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QADqB;EAEpChC,OAAO,EAAEyD,IAAI,CAACzD,OAFsB;EAGpCnC,OAAO,EAAE,IAAA6F,iBAAA,EAAS,YAAT,EAAuBD,IAAI,CAAC5F,OAA5B,EAAqC4F,IAAI,CAACzB,QAA1C;AAH2B,CAAtC,CADyB,CAA3B;AAQA,MAAME,mBAAmB,GAAG,IAAAlD,0BAAA,EACzByE,IAAD,KAAsC;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QADqB;EAEpChC,OAAO,EAAEyD,IAAI,CAACzD,OAFsB;EAGpCnC,OAAO,EAAE,IAAA6F,iBAAA,EAAS,aAAT,EAAwBD,IAAI,CAAC5F,OAA7B,EAAsC4F,IAAI,CAACzB,QAA3C;AAH2B,CAAtC,CAD0B,CAA5B;AAQA,MAAM2B,kBAAkB,GAAG,IAAA3E,0BAAA,EACxByE,IAAD,KAAsC;EACpCzB,QAAQ,EAAEyB,IAAI,CAACzB,QADqB;EAEpChC,OAAO,EAAEyD,IAAI,CAACzD,OAFsB;EAGpCnC,OAAO,EAAE,IAAA6F,iBAAA,EAAS,aAAT,EAAwBD,IAAI,CAAC5F,OAA7B,EAAsC4F,IAAI,CAACzB,QAA3C;AAH2B,CAAtC,CADyB,CAA3B;AAWA,MAAMjC,qBAAqB,GAAG5B,eAAe,CAAC;EAC5CC,IAAI,EAAEwF,KAAK,IAAI3E,oBAAoB,CAAC2E,KAAD,EAAQ,MAAR,EAAgBC,0CAAhB,CADS;EAE5CtF,GAAG,EAAE,CAACqF,KAAD,EAAQpF,OAAR,KACHa,mBAAmB,CAACuE,KAAD,EAAQ,MAAR,EAAgBC,0CAAhB,EAAyCrF,OAAzC,CAHuB;EAI5CE,SAAS,EAAE,CAACkF,KAAD,EAAQjF,KAAR,KACTW,wBAAwB,CAACsE,KAAD,EAAQ,MAAR,EAAgBC,0CAAhB,EAAyClF,KAAzC,CALkB;EAM5CE,YAAY,EAAE,CAAC+E,KAAD,EAAQjF,KAAR,EAAeH,OAAf,KACZe,2BAA2B,CACzBqE,KADyB,EAEzB,MAFyB,EAGzBC,0CAHyB,EAIzBlF,KAJyB,EAKzBH,OALyB,CAPe;EAc5CO,YAAY,EAAE,CAAC6E,KAAD,EAAQrG,OAAR,EAAiBuG,UAAjB,KACZC,uBAAuB,CAACH,KAAD,EAAQrG,OAAR,EAAiBuG,UAAjB;AAfmB,CAAD,CAA7C;AAqBA,MAAME,mBAAmB,GAAG7F,eAAe,CAAgB;EACzDC,IAAI,EAAEqF,IAAI,IAAIQ,mBAAmB,CAACR,IAAD,CADwB;EAEzDlF,GAAG,EAAE,CAACkF,IAAD,EAAOjF,OAAP,KAAmB0F,sBAAsB,CAACT,IAAD,CAAtB,CAA6BjF,OAA7B,CAFiC;EAGzDE,SAAS,EAAE,CAAC+E,IAAD,EAAO9E,KAAP,KAAiBwF,4BAA4B,CAACV,IAAD,CAA5B,CAAmC9E,KAAnC,CAH6B;EAIzDE,YAAY,EAAE,CAAC4E,IAAD,EAAO9E,KAAP,EAAcH,OAAd,KACZ4F,+BAA+B,CAACX,IAAD,CAA/B,CAAsC9E,KAAtC,EAA6CH,OAA7C,CALuD;EAMzDO,YAAY,EAAE,CAAC0E,IAAD,EAAOlG,OAAP,EAAgBuG,UAAhB,KACZO,eAAe,CAACZ,IAAI,CAACzB,QAAN,EAAgBzE,OAAhB,EAAyBuG,UAAzB;AAPwC,CAAhB,CAA3C;;AAUA,UAAU5C,aAAV,CACE0C,KADF,EAEErG,OAFF,EAGEU,KAHF,EAIE6F,UAJF,EAKE;EACA,MAAMtG,KAAK,GAAG,OAAOwG,mBAAmB,CAACJ,KAAD,EAAQrG,OAAR,EAAiBU,KAAjB,EAAwB6F,UAAxB,CAAxC;;EACA,IAAItG,KAAJ,EAAW;IACTA,KAAK,CAACS,KAAN,CAAY8D,GAAZ,CAAgB6B,KAAK,CAAC5B,QAAtB;EACD;;EAED,OAAOxE,KAAP;AACD;;AAED,MAAMyG,mBAAmB,GAAG,IAAAjF,0BAAA,EAAmByE,IAAD,IAC5CxE,oBAAoB,CAACwE,IAAD,EAAOA,IAAI,CAACzB,QAAZ,EAAsB7C,4CAAtB,CADM,CAA5B;AAGA,MAAM+E,sBAAsB,GAAG,IAAAlF,0BAAA,EAAmByE,IAAD,IAC/C,IAAArE,4BAAA,EAAqBZ,OAAD,IAClBa,mBAAmB,CACjBoE,IADiB,EAEjBA,IAAI,CAACzB,QAFY,EAGjB7C,4CAHiB,EAIjBX,OAJiB,CADrB,CAD6B,CAA/B;AAUA,MAAM2F,4BAA4B,GAAG,IAAAnF,0BAAA,EAAmByE,IAAD,IACrD,IAAArE,4BAAA,EAAqBT,KAAD,IAClBW,wBAAwB,CACtBmE,IADsB,EAEtBA,IAAI,CAACzB,QAFiB,EAGtB7C,4CAHsB,EAItBR,KAJsB,CAD1B,CADmC,CAArC;AAUA,MAAMyF,+BAA+B,GAAG,IAAApF,0BAAA,EACrCyE,IAAD,IACE,IAAArE,4BAAA,EAAqBT,KAAD,IAClB,IAAAS,4BAAA,EAAqBZ,OAAD,IAClBe,2BAA2B,CACzBkE,IADyB,EAEzBA,IAAI,CAACzB,QAFoB,EAGzB7C,4CAHyB,EAIzBR,KAJyB,EAKzBH,OALyB,CAD7B,CADF,CAFoC,CAAxC;;AAeA,SAAS6F,eAAT,CACErC,QADF,EAEEzE,OAFF,EAGEuG,UAHF,EAIE;EACA,IAAI,CAACA,UAAL,EAAiB;IACf,OAAO,MAAM,CAAE,CAAf;EACD;;EACD,OAAOA,UAAU,CAACQ,SAAX,CAAqB/G,OAAO,CAAC6E,UAA7B,EAAyCmC,uBAAA,CAAeC,MAAxD,EAAgE;IACrExC;EADqE,CAAhE,CAAP;AAGD;;AAED,SAAS/C,oBAAT,CACE;EAAEe,OAAF;EAAWnC;AAAX,CADF,EAEEqB,KAFF,EAGEuF,WAHF,EAQE;EACA,OAAOA,WAAW,CAACzE,OAAD,EAAUnC,OAAV,EAAmBqB,KAAnB,CAAlB;AACD;;AAED,SAAS6E,uBAAT,CACEW,CADF,EAEEnH,OAFF,EAGEuG,UAHF,EAIE;EAAA;;EACA,IAAI,CAACA,UAAL,EAAiB;IACf,OAAO,MAAM,CAAE,CAAf;EACD;;EACD,OAAOA,UAAU,CAACQ,SAAX,CAAqB/G,OAAO,CAAC6E,UAA7B,EAAyCmC,uBAAA,CAAeI,YAAxD,EAAsE;IAC3EC,UAAU,qBAAErH,OAAO,CAACgD,MAAV,qBAAE,gBAAgBsE;EAD+C,CAAtE,CAAP;AAGD;;AAED,SAASxF,mBAAT,CACE;EAAEW,OAAF;EAAWnC;AAAX,CADF,EAEEqB,KAFF,EAGEuF,WAHF,EAQEjG,OARF,EASE;EACA,MAAMiB,IAAI,GAAG5B,OAAO,CAACU,GAAR,IAAeV,OAAO,CAACU,GAAR,CAAYC,OAAZ,CAA5B;EACA,OAAOiB,IAAI,GAAGgF,WAAW,CAACzE,OAAD,EAAUP,IAAV,EAAiB,GAAEP,KAAM,SAAQV,OAAQ,IAAzC,CAAd,GAA8D,IAAzE;AACD;;AAED,SAASc,wBAAT,CACE;EAAEU,OAAF;EAAWnC;AAAX,CADF,EAEEqB,KAFF,EAGEuF,WAHF,EAQE9F,KARF,EASE;EACA,MAAMc,IAAI,GAAG5B,OAAO,CAACa,SAAR,IAAqBb,OAAO,CAACa,SAAR,CAAkBC,KAAlB,CAAlC;EACA,IAAI,CAACc,IAAL,EAAW,MAAM,IAAIqF,KAAJ,CAAU,sCAAV,CAAN;EAEX,OAAOL,WAAW,CAACzE,OAAD,EAAUP,IAAV,EAAiB,GAAEP,KAAM,cAAaP,KAAM,GAA5C,CAAlB;AACD;;AAED,SAASY,2BAAT,CACE;EAAES,OAAF;EAAWnC;AAAX,CADF,EAEEqB,KAFF,EAGEuF,WAHF,EAQE9F,KARF,EASEH,OATF,EAUE;EACA,MAAMuG,QAAQ,GAAGlH,OAAO,CAACa,SAAR,IAAqBb,OAAO,CAACa,SAAR,CAAkBC,KAAlB,CAAtC;EACA,IAAI,CAACoG,QAAL,EAAe,MAAM,IAAID,KAAJ,CAAU,sCAAV,CAAN;EAEf,MAAMrF,IAAI,GAAGsF,QAAQ,CAACxG,GAAT,IAAgBwG,QAAQ,CAACxG,GAAT,CAAaC,OAAb,CAA7B;EACA,OAAOiB,IAAI,GACPgF,WAAW,CACTzE,OADS,EAETP,IAFS,EAGR,GAAEP,KAAM,cAAaP,KAAM,UAASH,OAAQ,IAHpC,CADJ,GAMP,IANJ;AAOD;;AAED,SAASL,eAAT,CAME;EACAC,IADA;EAEAG,GAFA;EAGAG,SAHA;EAIAG,YAJA;EAKAE;AALA,CANF,EAmCiC;EAC/B,OAAO,UAAUiG,WAAV,CAAsBpB,KAAtB,EAA6BrG,OAA7B,EAAsCU,KAAK,GAAG,IAAIC,GAAJ,EAA9C,EAAyD4F,UAAzD,EAAqE;IAC1E,MAAM;MAAE9D;IAAF,IAAc4D,KAApB;IAEA,MAAMqB,gBAIJ,GAAG,EAJL;IAMA,MAAMC,QAAQ,GAAG9G,IAAI,CAACwF,KAAD,CAArB;;IACA,IAAIuB,kBAAkB,CAACD,QAAD,EAAWlF,OAAX,EAAoBzC,OAApB,EAA6BqG,KAAK,CAAC5B,QAAnC,CAAtB,EAAoE;MAClEiD,gBAAgB,CAACG,IAAjB,CAAsB;QACpBvD,MAAM,EAAEqD,QADY;QAEpB1G,OAAO,EAAE0B,SAFW;QAGpBvB,KAAK,EAAEuB;MAHa,CAAtB;MAMA,MAAMmF,OAAO,GAAG9G,GAAG,CAACqF,KAAD,EAAQrG,OAAO,CAACiB,OAAhB,CAAnB;;MACA,IACE6G,OAAO,IACPF,kBAAkB,CAACE,OAAD,EAAUrF,OAAV,EAAmBzC,OAAnB,EAA4BqG,KAAK,CAAC5B,QAAlC,CAFpB,EAGE;QACAiD,gBAAgB,CAACG,IAAjB,CAAsB;UACpBvD,MAAM,EAAEwD,OADY;UAEpB7G,OAAO,EAAEjB,OAAO,CAACiB,OAFG;UAGpBG,KAAK,EAAEuB;QAHa,CAAtB;MAKD;;MAED,CAACgF,QAAQ,CAACrH,OAAT,CAAiBa,SAAjB,IAA8B,EAA/B,EAAmC4G,OAAnC,CAA2C,CAACZ,CAAD,EAAI/F,KAAJ,KAAc;QACvD,MAAM4G,WAAW,GAAG7G,SAAS,CAACkF,KAAD,EAAQjF,KAAR,CAA7B;;QACA,IAAIwG,kBAAkB,CAACI,WAAD,EAAcvF,OAAd,EAAuBzC,OAAvB,EAAgCqG,KAAK,CAAC5B,QAAtC,CAAtB,EAAuE;UACrEiD,gBAAgB,CAACG,IAAjB,CAAsB;YACpBvD,MAAM,EAAE0D,WADY;YAEpB5G,KAFoB;YAGpBH,OAAO,EAAE0B;UAHW,CAAtB;UAMA,MAAMsF,eAAe,GAAG3G,YAAY,CAAC+E,KAAD,EAAQjF,KAAR,EAAepB,OAAO,CAACiB,OAAvB,CAApC;;UACA,IACEgH,eAAe,IACfL,kBAAkB,CAChBK,eADgB,EAEhBxF,OAFgB,EAGhBzC,OAHgB,EAIhBqG,KAAK,CAAC5B,QAJU,CAFpB,EAQE;YACAiD,gBAAgB,CAACG,IAAjB,CAAsB;cACpBvD,MAAM,EAAE2D,eADY;cAEpB7G,KAFoB;cAGpBH,OAAO,EAAEjB,OAAO,CAACiB;YAHG,CAAtB;UAKD;QACF;MACF,CA1BD;IA2BD;;IAKD,IACEyG,gBAAgB,CAAC5B,IAAjB,CACE,CAAC;MACCxB,MAAM,EAAE;QACNhE,OAAO,EAAE;UAAE+D,MAAF;UAAU6D;QAAV;MADH;IADT,CAAD,KAIMxD,YAAY,CAAC1E,OAAD,EAAUqE,MAAV,EAAkB6D,IAAlB,EAAwBzF,OAAxB,CALpB,CADF,EAQE;MACA,OAAO,IAAP;IACD;;IAED,MAAMxC,KAAK,GAAGqD,UAAU,EAAxB;IACA,MAAM6E,MAAM,GAAG3G,YAAY,CAAC6E,KAAD,EAAQrG,OAAR,EAAiBuG,UAAjB,CAA3B;;IAEA,KAAK,MAAM;MAAEjC,MAAF;MAAUlD,KAAV;MAAiBH;IAAjB,CAAX,IAAyCyG,gBAAzC,EAA2D;MACzD,IACE,EAAE,OAAOU,iBAAiB,CACxBnI,KADwB,EAExBqE,MAAM,CAAChE,OAFiB,EAGxBmC,OAHwB,EAIxBzC,OAJwB,EAKxBU,KALwB,EAMxB6F,UANwB,CAA1B,CADF,EASE;QACA,OAAO,IAAP;MACD;;MAED4B,MAAM,CAAC7D,MAAD,EAASlD,KAAT,EAAgBH,OAAhB,CAAN;MACA,OAAOoH,cAAc,CAACpI,KAAD,EAAQqE,MAAR,CAArB;IACD;;IACD,OAAOrE,KAAP;EACD,CA9FD;AA+FD;;AAED,UAAUmI,iBAAV,CACEnI,KADF,EAEEiC,IAFF,EAGEO,OAHF,EAIEzC,OAJF,EAKEU,KALF,EAME6F,UANF,EAOoB;EAClB,IAAIrE,IAAI,CAACoG,OAAL,KAAiB3F,SAArB,EAAgC,OAAO,IAAP;EAEhC,MAAMuD,IAAI,GAAG,OAAO,IAAAnD,iBAAA,EAClBb,IAAI,CAACoG,OADa,EAElB7F,OAFkB,EAGlBzC,OAAO,CAACiB,OAHU,EAIlBjB,OAAO,CAACgD,MAJU,CAApB;;EAOA,IAAItC,KAAK,CAAC6H,GAAN,CAAUrC,IAAV,CAAJ,EAAqB;IACnB,MAAM,IAAIqB,KAAJ,CACH,wCAAuCrB,IAAI,CAACzB,QAAS,KAAtD,GACG,mDADH,GAEEe,KAAK,CAACgD,IAAN,CAAW9H,KAAX,EAAkBwF,IAAI,IAAK,MAAKA,IAAI,CAACzB,QAAS,EAA9C,EAAiDS,IAAjD,CAAsD,IAAtD,CAHE,CAAN;EAKD;;EAEDxE,KAAK,CAAC8D,GAAN,CAAU0B,IAAV;EACA,MAAMlC,SAAS,GAAG,OAAOL,aAAa,CACpCyC,kBAAkB,CAACF,IAAD,CADkB,EAEpClG,OAFoC,EAGpCU,KAHoC,EAIpC6F,UAJoC,CAAtC;EAMA7F,KAAK,CAAC+H,MAAN,CAAavC,IAAb;EAEA,IAAI,CAAClC,SAAL,EAAgB,OAAO,KAAP;EAEhBJ,UAAU,CAAC3D,KAAD,EAAQ+D,SAAR,CAAV;EAEA,OAAO,IAAP;AACD;;AAED,SAASJ,UAAT,CAAoB8E,MAApB,EAAyCC,MAAzC,EAA2E;EACzED,MAAM,CAACpI,OAAP,CAAeuH,IAAf,CAAoB,GAAGc,MAAM,CAACrI,OAA9B;EACAoI,MAAM,CAACvI,OAAP,CAAe0H,IAAf,CAAoB,GAAGc,MAAM,CAACxI,OAA9B;EACAuI,MAAM,CAACrI,OAAP,CAAewH,IAAf,CAAoB,GAAGc,MAAM,CAACtI,OAA9B;;EACA,KAAK,MAAM6F,IAAX,IAAmByC,MAAM,CAACjI,KAA1B,EAAiC;IAC/BgI,MAAM,CAAChI,KAAP,CAAa8D,GAAb,CAAiB0B,IAAjB;EACD;;EAED,OAAOwC,MAAP;AACD;;AAED,UAAUL,cAAV,CACEK,MADF,EAEE;EAAEpI,OAAF;EAAWH,OAAX;EAAoBE;AAApB,CAFF,EAGwB;EACtBqI,MAAM,CAACpI,OAAP,CAAeuH,IAAf,CAAoBvH,OAApB;EACAoI,MAAM,CAACvI,OAAP,CAAe0H,IAAf,CAAoB,IAAI,OAAO1H,OAAO,EAAlB,CAApB;EACAuI,MAAM,CAACrI,OAAP,CAAewH,IAAf,CAAoB,IAAI,OAAOxH,OAAO,EAAlB,CAApB;EAEA,OAAOqI,MAAP;AACD;;AAED,SAASpF,UAAT,GAAmC;EACjC,OAAO;IACLhD,OAAO,EAAE,EADJ;IAELD,OAAO,EAAE,EAFJ;IAGLF,OAAO,EAAE,EAHJ;IAILO,KAAK,EAAE,IAAIC,GAAJ;EAJF,CAAP;AAMD;;AAED,SAASF,gBAAT,CAA0ByB,IAA1B,EAAoE;EAClE,MAAM5B,OAAO,qBACR4B,IADQ,CAAb;EAGA,OAAO5B,OAAO,CAACgI,OAAf;EACA,OAAOhI,OAAO,CAACU,GAAf;EACA,OAAOV,OAAO,CAACa,SAAf;EACA,OAAOb,OAAO,CAACH,OAAf;EACA,OAAOG,OAAO,CAACD,OAAf;EACA,OAAOC,OAAO,CAACsI,aAAf;EACA,OAAOtI,OAAO,CAAC+D,MAAf;EACA,OAAO/D,OAAO,CAAC4H,IAAf;EACA,OAAO5H,OAAO,CAACuI,IAAf;EACA,OAAOvI,OAAO,CAACwI,OAAf;EACA,OAAOxI,OAAO,CAACyI,OAAf;;EAIA,IAAIC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqC7I,OAArC,EAA8C,WAA9C,CAAJ,EAAgE;IAC9DA,OAAO,CAAC8I,UAAR,GAAqB9I,OAAO,CAAC+I,SAA7B;IACA,OAAO/I,OAAO,CAAC+I,SAAf;EACD;;EACD,OAAO/I,OAAP;AACD;;AAED,SAASF,gBAAT,CACEkJ,KADF,EAE6B;EAC3B,MAAM/I,GAGL,GAAG,IAAIgJ,GAAJ,EAHJ;EAKA,MAAMrC,WAAW,GAAG,EAApB;;EAEA,KAAK,MAAMsC,IAAX,IAAmBF,KAAnB,EAA0B;IACxB,IAAI,OAAOE,IAAI,CAACC,KAAZ,KAAsB,UAA1B,EAAsC;MACpC,MAAMC,KAAK,GAAGF,IAAI,CAACC,KAAnB;MACA,IAAIE,OAAO,GAAGpJ,GAAG,CAACqJ,GAAJ,CAAQF,KAAR,CAAd;;MACA,IAAI,CAACC,OAAL,EAAc;QACZA,OAAO,GAAG,IAAIJ,GAAJ,EAAV;QACAhJ,GAAG,CAACsJ,GAAJ,CAAQH,KAAR,EAAeC,OAAf;MACD;;MACD,IAAIG,IAAI,GAAGH,OAAO,CAACC,GAAR,CAAYJ,IAAI,CAAClC,IAAjB,CAAX;;MACA,IAAI,CAACwC,IAAL,EAAW;QACTA,IAAI,GAAG;UAAEL,KAAK,EAAED;QAAT,CAAP;QACAtC,WAAW,CAACW,IAAZ,CAAiBiC,IAAjB;QAIA,IAAI,CAACN,IAAI,CAACO,OAAV,EAAmBJ,OAAO,CAACE,GAAR,CAAYL,IAAI,CAAClC,IAAjB,EAAuBwC,IAAvB;MACpB,CAPD,MAOO;QACLA,IAAI,CAACL,KAAL,GAAaD,IAAb;MACD;IACF,CAlBD,MAkBO;MACLtC,WAAW,CAACW,IAAZ,CAAiB;QAAE4B,KAAK,EAAED;MAAT,CAAjB;IACD;EACF;;EAED,OAAOtC,WAAW,CAAC8C,MAAZ,CAAmB,CAACC,GAAD,EAAMH,IAAN,KAAe;IACvCG,GAAG,CAACpC,IAAJ,CAASiC,IAAI,CAACL,KAAd;IACA,OAAOQ,GAAP;EACD,CAHM,EAGJ,EAHI,CAAP;AAID;;AAED,SAASrC,kBAAT,CACE;EAAEtH;AAAF,CADF,EAEEmC,OAFF,EAGEzC,OAHF,EAIEkK,UAJF,EAKW;EACT,OACE,CAAC5J,OAAO,CAACuI,IAAR,KAAiBlG,SAAjB,IACCwH,uBAAuB,CAACnK,OAAD,EAAUM,OAAO,CAACuI,IAAlB,EAAwBpG,OAAxB,EAAiCyH,UAAjC,CADzB,MAEC5J,OAAO,CAACwI,OAAR,KAAoBnG,SAApB,IACCwH,uBAAuB,CAACnK,OAAD,EAAUM,OAAO,CAACwI,OAAlB,EAA2BrG,OAA3B,EAAoCyH,UAApC,CAHzB,MAIC5J,OAAO,CAACyI,OAAR,KAAoBpG,SAApB,IACC,CAACwH,uBAAuB,CAACnK,OAAD,EAAUM,OAAO,CAACyI,OAAlB,EAA2BtG,OAA3B,EAAoCyH,UAApC,CAL1B,CADF;AAQD;;AAED,SAASC,uBAAT,CACEnK,OADF,EAEE6I,IAFF,EAGEpG,OAHF,EAIEyH,UAJF,EAKW;EACT,MAAME,QAAQ,GAAG5E,KAAK,CAACC,OAAN,CAAcoD,IAAd,IAAsBA,IAAtB,GAA6B,CAACA,IAAD,CAA9C;EAEA,OAAOwB,eAAe,CAACrK,OAAD,EAAUoK,QAAV,EAAoB3H,OAApB,EAA6ByH,UAA7B,CAAtB;AACD;;AAKD,SAASI,kBAAT,CACEC,IADF,EAEEd,KAFF,EAGoC;EAClC,IAAIA,KAAK,YAAYe,MAArB,EAA6B;IAC3B,OAAOC,MAAM,CAAChB,KAAD,CAAb;EACD;;EAED,OAAOA,KAAP;AACD;;AAKD,SAAS/E,YAAT,CACE1E,OADF,EAEEqE,MAFF,EAGE6D,IAHF,EAIEzF,OAJF,EAKW;EACT,IAAI4B,MAAM,IAAIgG,eAAe,CAACrK,OAAD,EAAUqE,MAAV,EAAkB5B,OAAlB,CAA7B,EAAyD;IAAA;;IACvD,MAAMiI,OAAO,GAAI,4BAAD,qBACd1K,OAAO,CAACiE,QADM,gCACM,WACrB,yCAAwC0G,IAAI,CAACC,SAAL,CACvCvG,MADuC,EAEvCiG,kBAFuC,CAGvC,YAAW7H,OAAQ,GALrB;IAMA7C,KAAK,CAAC8K,OAAD,CAAL;;IACA,IAAI1K,OAAO,CAAC6E,UAAZ,EAAwB;MACtBC,OAAO,CAACC,GAAR,CAAY2F,OAAZ;IACD;;IACD,OAAO,IAAP;EACD;;EAED,IAAIxC,IAAI,IAAI,CAACmC,eAAe,CAACrK,OAAD,EAAUkI,IAAV,EAAgBzF,OAAhB,CAA5B,EAAsD;IAAA;;IACpD,MAAMiI,OAAO,GAAI,4BAAD,sBACd1K,OAAO,CAACiE,QADM,iCACM,WACrB,8CAA6C0G,IAAI,CAACC,SAAL,CAC5C1C,IAD4C,EAE5CoC,kBAF4C,CAG5C,YAAW7H,OAAQ,GALrB;IAMA7C,KAAK,CAAC8K,OAAD,CAAL;;IACA,IAAI1K,OAAO,CAAC6E,UAAZ,EAAwB;MACtBC,OAAO,CAACC,GAAR,CAAY2F,OAAZ;IACD;;IACD,OAAO,IAAP;EACD;;EAED,OAAO,KAAP;AACD;;AAMD,SAASL,eAAT,CACErK,OADF,EAEEoK,QAFF,EAGE3H,OAHF,EAIEyH,UAJF,EAKW;EACT,OAAOE,QAAQ,CAACtE,IAAT,CAAc+E,OAAO,IAC1B5E,YAAY,CAAC4E,OAAD,EAAUpI,OAAV,EAAmBzC,OAAO,CAACiE,QAA3B,EAAqCjE,OAArC,EAA8CkK,UAA9C,CADP,CAAP;AAGD;;AAED,SAASjE,YAAT,CACE4E,OADF,EAEEpI,OAFF,EAGEqI,UAHF,EAIE9K,OAJF,EAKEkK,UALF,EAMW;EACT,IAAI,OAAOW,OAAP,KAAmB,UAAvB,EAAmC;IACjC,OAAO,CAAC,CAAC,IAAAE,qCAAA,EAAmBF,OAAnB,EAA4BC,UAA5B,EAAwC;MAC/CrI,OAD+C;MAE/CxB,OAAO,EAAEjB,OAAO,CAACiB,OAF8B;MAG/C+B,MAAM,EAAEhD,OAAO,CAACgD;IAH+B,CAAxC,CAAT;EAKD;;EAED,IAAI,OAAO8H,UAAP,KAAsB,QAA1B,EAAoC;IAClC,MAAM,IAAIE,oBAAJ,CACH,mFADG,EAEJd,UAFI,CAAN;EAID;;EAED,IAAI,OAAOW,OAAP,KAAmB,QAAvB,EAAiC;IAC/BA,OAAO,GAAG,IAAA9E,uBAAA,EAAmB8E,OAAnB,EAA4BpI,OAA5B,CAAV;EACD;;EACD,OAAOoI,OAAO,CAAChC,IAAR,CAAaiC,UAAb,CAAP;AACD"}