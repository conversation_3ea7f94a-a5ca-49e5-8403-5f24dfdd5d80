import { jsxs, jsx } from 'react/jsx-runtime';
import * as React from 'react';

const defaultColor = "#F2CC38";
const SiTabelog = React.forwardRef(function SiTabelog2({ title = "Tabelog", color = "currentColor", size = 24, ...others }, ref) {
  if (color === "default") {
    color = defaultColor;
  }
  return /* @__PURE__ */ jsxs(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      width: size,
      height: size,
      fill: color,
      viewBox: "0 0 24 24",
      ref,
      ...others,
      children: [
        /* @__PURE__ */ jsx("title", { children: title }),
        /* @__PURE__ */ jsx("path", { d: "M24 14.4915a.183.183 0 0 0-.1398-.181l-2.2517-.5431a.1817.1817 0 0 0-.163.0401.183.183 0 0 0-.0615.1505c-.2094-.0678-.4082-.1495-.5707-.2458-.4315-.2573-.9912-.7497-1.207-1.6315-.148-.6047-.5402-1.6616-.7995-2.3615l-.1867-.5094c-.0513-.1451-.1369-.312-.2486-.4861l.905-.4252a.2412.2412 0 0 0 .1127-.3241l-.3468-.7082a.2413.2413 0 0 0-.325-.1098l-1.1905.6057c-.1727-.1466-.356-.2782-.5447-.3832-.2525-.1402-.65-.3415-1.0637-.5456l1.9654-1.1406a.2403.2403 0 0 0 .0846-.3323l-.4063-.6757a.2417.2417 0 0 0-.3338-.0813l-2.5878 1.6122c-.2027-.0962-.3415-.162-.3725-.1765-.0876-.0562-.5863-.3386-1.38-.0305-.759.294-1.4478.5084-1.4546.5108-.058.0184-.562.1838-.7623.4494-.2075.2757-.7672.9064-.9568 1.0114-.2235.1234-.5035.369-.5577.6535a.5921.5921 0 0 0-.0068.0489.451.451 0 0 0 .1045.338c.1267.1534.3086.2255.4658.2603L.1175 15.2635a.246.246 0 0 0-.0823.3367l.0755.1248a.2473.2473 0 0 0 .3357.0856l8.882-5.153c-.0092.1644-.0039.5785.2506.7981.0846.073.2249.1451.4348.1292l-9.265 4.7123a.2459.2459 0 0 0-.1108.3285l.0638.131a.247.247 0 0 0 .327.1142l9.087-4.2693c-.0938.2922-.1649.5665-.1842.775-.0078.0831-.0078.1552.0024.2147.0643.3802.2336.6414.4765.7343.119.046.298.0605.5233-.046.0494.192.1427.4349.3154.5742.1089.0876.239.1258.375.1103.293-.0329.6476-.2931.8827-.6477.1596-.2419.2535-.503.2786-.7672a1.535 1.535 0 0 0-.0005-.2752c.238.0827.582.2554.7672.5993.359.6666 1.6751 2.5008 2.4882 2.88l.4683.2322c.6975.3546 1.6523.8398 2.997 1.0908.0064.0024.0136.0053.0204.0077a.1873.1873 0 0 0-.0232.0706.1836.1836 0 0 0 .1054.1814l2.3393 1.0797a.1805.1805 0 0 0 .1282.0092c.074-.0213 1.8468-.5911 1.9242-4.9334zm-7.1953-6.7933a2.857 2.857 0 0 1 .3662.2448l-2.6324 1.3394a3.6848 3.6848 0 0 0-.3376-.1906l-.3827-.1814c-.2288-.1079-.5248-.2462-.8116-.3875l2.5317-1.4685c.4862.2375.979.4837 1.2664.6438zM9.5166 8.7014c.0237-.1238.1852-.2965.3753-.402.297-.164.9355-.9301 1.072-1.111.0981-.1302.4077-.2656.58-.3207.0057-.0015.7057-.2196 1.4767-.5186.6632-.2568 1.0434-.0082 1.0593.0024a.1997.1997 0 0 0 .0262.015c.0039.0015.0653.0305.1664.0784l-2.3703 1.4768c-.4092-.1529-.8948.0038-1.1657.2844-.1079.1113-.1848.2641-.2278.4363-.1582.3159-.4247.3096-.4572.3072-.0024 0-.0048.001-.0082.0005-.0068-.0005-.0145-.002-.0217-.0015-.0958.0024-.3686-.0208-.4852-.1616-.0203-.0246-.0266-.0498-.0198-.0856zm.3013 2.4776c-.1287-.1108-.132-.4111-.1224-.5287.0039-.0111.0387-.1127.0895-.2578l.9766-.567c.0755.104.1645.2008.2666.2893-.0416.3691-.166.6308-.1674.6337-.0005.0005 0 .0005-.0005.0005v.0005c-.0015.0029-.1557.2897-.774.4537-.1837.0489-.2476-.0058-.2684-.0242zm1.3718-.2723.0005-.001c.016-.0324.1118-.2418.1703-.5495.1615.105.3178.2066.4668.3048l-.685.3487c.0165-.0348.0324-.0701.0474-.103zm-.249 2.9424c-.1597.1243-.3014.1732-.3996.1355-.1137-.044-.2061-.2133-.2467-.4542-.0063-.0358-.0063-.0818-.001-.136.0213-.2268.1345-.6003.283-1.0095l1.1478-.5393c-.1044.2472-.1964.55-.2094.8658-.0208.505-.4992 1.0545-.5742 1.1377zm8.6792 3.8809a.2123.2123 0 0 0-.0304-.0082c-1.3027-.2404-2.2358-.715-2.9164-1.0608l-.4793-.2375c-.6985-.326-1.9707-2.0723-2.3209-2.7219-.3076-.5718-.9162-.7715-1.1745-.8325-.0507-.1717-.1557-.4914-.2824-.6593a.1833.1833 0 0 0-.256-.0358.1833.1833 0 0 0-.0362.2559c.0943.1248.2046.46.2525.6419 0 .001.0005.0015.001.0019.0004.0015.0004.0029.001.0044.1383.4624-.0514.849-.1762 1.0376-.1959.2955-.459.4682-.6186.4861-.0402.0044-.0711-.0048-.105-.032-.117-.0938-.1925-.3453-.223-.5252.1625-.1892.6008-.7503.625-1.3172.0203-.49.2776-.9708.3908-1.1377l.5544-.2607c.3937.2699.6554.4605.727.533l.1214.1152c.2588.2399.7981.7396.863 1.2571.0914.728.951 2.4022 1.9353 2.8709a.182.182 0 0 0 .2433-.0866.1822.1822 0 0 0-.0861-.2433c-.8634-.4112-1.6495-1.9445-1.7303-2.5864-.0812-.6492-.664-1.189-.977-1.4792l-.1103-.105c-.2516-.253-1.8706-1.3075-2.3615-1.6248-.4746-.3072-.5853-.7004-.609-.908-.0281-.2433.0338-.4832.1576-.6109.1858-.192.6003-.3454.8964-.133.338.2419 1.1986.6458 1.7684.9133l.3759.1784c.3502.1732.6491.4136.652.416.0416.0358 1.1218.9607 1.5054 1.2142.5156.341.951.1257 1.1091-.0353a.182.182 0 0 0-.0019-.256.1832.1832 0 0 0-.2573-.0014c-.0261.0256-.2665.2399-.6482-.0121-.1828-.121-.55-.4175-.8721-.6845l2.5119-1.1802c.1069.162.1896.3178.2365.4513l.1892.5151c.2563.6917.6443 1.737.787 2.3209.2452 1.0052.8832 1.5653 1.3747 1.8584.2225.1326.4977.237.7768.3202.018.848-.0875 2.866-1.4245 3.5186a31.0668 31.0668 0 0 0-.3584-.135zm2.4065 1.3205c-.1137-.0527-1.3631-.6293-1.8807-.8682 1.1368-.5549 1.498-1.8933 1.5958-2.9265a7.769 7.769 0 0 0 .0242-1.0744c.3584.0866 1.6393.3952 1.8638.4494-.0087.313-.0247.608-.0504.879-.2578 2.7397-1.2992 3.4154-1.5527 3.5407z" })
      ]
    }
  );
});

export { SiTabelog as default, defaultColor };
