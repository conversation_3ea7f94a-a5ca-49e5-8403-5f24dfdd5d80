import React from 'react';
import { Badge } from '@/components/ui/badge';
import { GitBranch, AlertCircle, CheckCircle, Zap } from 'lucide-react';

interface StatusBarProps {
  currentFile?: string;
  language?: string;
  line?: number;
  column?: number;
  errors?: number;
  warnings?: number;
}

const StatusBar: React.FC<StatusBarProps> = ({
  currentFile,
  language = 'TypeScript',
  line = 1,
  column = 1,
  errors = 0,
  warnings = 0
}) => {
  return (
    <div className="status-bar h-6 bg-surface-primary border-t border-surface-tertiary flex items-center justify-between px-2 text-xs text-gray-400">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <GitBranch size={12} />
          <span>main</span>
        </div>
        
        {errors > 0 && (
          <div className="flex items-center space-x-1 text-red-400">
            <AlertCircle size={12} />
            <span>{errors}</span>
          </div>
        )}
        
        {warnings > 0 && (
          <div className="flex items-center space-x-1 text-yellow-400">
            <AlertCircle size={12} />
            <span>{warnings}</span>
          </div>
        )}
        
        <div className="flex items-center space-x-1 text-green-400">
          <CheckCircle size={12} />
          <span>Ready</span>
        </div>
      </div>
      
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <Zap size={12} className="text-neon-purple" />
          <span>AI Assistant</span>
        </div>
        
        <Badge variant="outline" className="text-xs">
          {language}
        </Badge>
        
        <span>Ln {line}, Col {column}</span>
        
        {currentFile && (
          <span className="truncate max-w-48">{currentFile}</span>
        )}
      </div>
    </div>
  );
};

export default StatusBar;
