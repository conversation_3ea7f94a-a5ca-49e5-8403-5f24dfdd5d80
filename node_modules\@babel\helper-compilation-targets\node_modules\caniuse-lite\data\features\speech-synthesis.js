module.exports={A:{A:{"2":"J D E F A B 4B"},B:{"1":"L G M N O","2":"C K","257":"P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H"},C:{"1":"LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB","2":"0 1 2 5B pB I q J D E F A B C K L G M N O r s t u v w x y z 6B 7B","194":"3 4 5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB"},D:{"1":"5 6 7 8 9 AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB","2":"0 1 2 3 4 I q J D E F A B C K L G M N O r s t u v w x y z","257":"RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B"},E:{"1":"D E F A B C K L G DC EC vB mB nB wB FC GC xB yB zB 0B oB 1B HC","2":"I q J AC uB BC CC"},F:{"1":"0 1 2 3 4 5 6 7 8 9 z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB","2":"F B C G M N O r s t u v w x y IC JC KC LC mB 2B MC nB","257":"c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a"},G:{"1":"E QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B","2":"uB NC 3B OC PC"},H:{"2":"hC"},I:{"2":"pB I H iC jC kC lC 3B mC nC"},J:{"2":"D A"},K:{"2":"A B C c mB 2B nB"},L:{"1":"H"},M:{"1":"b"},N:{"2":"A B"},O:{"2":"oC"},P:{"1":"pC qC rC sC tC vB uC vC wC xC yC oB zC 0C","2":"I"},Q:{"1":"wB"},R:{"2":"1C"},S:{"1":"2C"}},B:7,C:"Speech Synthesis API"};
