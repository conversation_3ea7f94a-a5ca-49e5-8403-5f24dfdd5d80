{"version": 3, "names": ["findConfigUpwards", "rootDir", "findPackageData", "filepath", "directories", "pkg", "isPackage", "findRelativeConfig", "pkgData", "envName", "caller", "config", "ignore", "findRootConfig", "dirname", "loadConfig", "name", "Error", "resolveShowConfigPath", "ROOT_CONFIG_FILENAMES", "resolvePlugin", "resolvePreset", "loadPlugin", "loadPreset"], "sources": ["../../../src/config/files/index-browser.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport type {\n  ConfigFile,\n  IgnoreFile,\n  RelativeConfig,\n  FilePackageData,\n} from \"./types\";\n\nimport type { CallerMetadata } from \"../validation/options\";\n\nexport type { ConfigFile, IgnoreFile, RelativeConfig, FilePackageData };\n\nexport function findConfigUpwards(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  rootDir: string,\n): string | null {\n  return null;\n}\n\n// eslint-disable-next-line require-yield\nexport function* findPackageData(filepath: string): Handler<FilePackageData> {\n  return {\n    filepath,\n    directories: [],\n    pkg: null,\n    isPackage: false,\n  };\n}\n\n// eslint-disable-next-line require-yield\nexport function* findRelativeConfig(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  pkgData: FilePackageData,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<RelativeConfig> {\n  return { config: null, ignore: null };\n}\n\n// eslint-disable-next-line require-yield\nexport function* findRootConfig(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  dirname: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile | null> {\n  return null;\n}\n\n// eslint-disable-next-line require-yield\nexport function* loadConfig(\n  name: string,\n  dirname: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  envName: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  caller: CallerMetadata | undefined,\n): Handler<ConfigFile> {\n  throw new Error(`Cannot load ${name} relative to ${dirname} in a browser`);\n}\n\n// eslint-disable-next-line require-yield\nexport function* resolveShowConfigPath(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  dirname: string,\n): Handler<string | null> {\n  return null;\n}\n\nexport const ROOT_CONFIG_FILENAMES: string[] = [];\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function resolvePlugin(name: string, dirname: string): string | null {\n  return null;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function resolvePreset(name: string, dirname: string): string | null {\n  return null;\n}\n\nexport function loadPlugin(\n  name: string,\n  dirname: string,\n): Handler<{\n  filepath: string;\n  value: unknown;\n}> {\n  throw new Error(\n    `Cannot load plugin ${name} relative to ${dirname} in a browser`,\n  );\n}\n\nexport function loadPreset(\n  name: string,\n  dirname: string,\n): Handler<{\n  filepath: string;\n  value: unknown;\n}> {\n  throw new Error(\n    `Cannot load preset ${name} relative to ${dirname} in a browser`,\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAaO,SAASA,iBAAT,CAELC,OAFK,EAGU;EACf,OAAO,IAAP;AACD;;AAGM,UAAUC,eAAV,CAA0BC,QAA1B,EAAsE;EAC3E,OAAO;IACLA,QADK;IAELC,WAAW,EAAE,EAFR;IAGLC,GAAG,EAAE,IAHA;IAILC,SAAS,EAAE;EAJN,CAAP;AAMD;;AAGM,UAAUC,kBAAV,CAELC,OAFK,EAILC,OAJK,EAMLC,MANK,EAOoB;EACzB,OAAO;IAAEC,MAAM,EAAE,IAAV;IAAgBC,MAAM,EAAE;EAAxB,CAAP;AACD;;AAGM,UAAUC,cAAV,CAELC,OAFK,EAILL,OAJK,EAMLC,MANK,EAOuB;EAC5B,OAAO,IAAP;AACD;;AAGM,UAAUK,UAAV,CACLC,IADK,EAELF,OAFK,EAILL,OAJK,EAMLC,MANK,EAOgB;EACrB,MAAM,IAAIO,KAAJ,CAAW,eAAcD,IAAK,gBAAeF,OAAQ,eAArD,CAAN;AACD;;AAGM,UAAUI,qBAAV,CAELJ,OAFK,EAGmB;EACxB,OAAO,IAAP;AACD;;AAEM,MAAMK,qBAA+B,GAAG,EAAxC;;;AAGA,SAASC,aAAT,CAAuBJ,IAAvB,EAAqCF,OAArC,EAAqE;EAC1E,OAAO,IAAP;AACD;;AAGM,SAASO,aAAT,CAAuBL,IAAvB,EAAqCF,OAArC,EAAqE;EAC1E,OAAO,IAAP;AACD;;AAEM,SAASQ,UAAT,CACLN,IADK,EAELF,OAFK,EAMJ;EACD,MAAM,IAAIG,KAAJ,CACH,sBAAqBD,IAAK,gBAAeF,OAAQ,eAD9C,CAAN;AAGD;;AAEM,SAASS,UAAT,CACLP,IADK,EAELF,OAFK,EAMJ;EACD,MAAM,IAAIG,KAAJ,CACH,sBAAqBD,IAAK,gBAAeF,OAAQ,eAD9C,CAAN;AAGD"}