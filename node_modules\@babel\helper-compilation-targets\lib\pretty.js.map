{"version": 3, "names": ["prettifyVersion", "version", "parts", "semver", "major", "minor", "patch", "push", "join", "prettifyTargets", "targets", "Object", "keys", "reduce", "results", "target", "value", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/pretty.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { unreleasedLabels } from \"./targets\";\nimport type { Targets, Target } from \"./types\";\n\nexport function prettifyVersion(version: string) {\n  if (typeof version !== \"string\") {\n    return version;\n  }\n\n  const parts = [semver.major(version)];\n  const minor = semver.minor(version);\n  const patch = semver.patch(version);\n\n  if (minor || patch) {\n    parts.push(minor);\n  }\n\n  if (patch) {\n    parts.push(patch);\n  }\n\n  return parts.join(\".\");\n}\n\nexport function prettifyTargets(targets: Targets): Targets {\n  return Object.keys(targets).reduce((results, target: Target) => {\n    let value = targets[target];\n\n    const unreleasedLabel =\n      // @ts-expect-error undefined is strictly compared with string later\n      unreleasedLabels[target];\n    if (typeof value === \"string\" && unreleasedLabel !== value) {\n      value = prettifyVersion(value);\n    }\n\n    results[target] = value;\n    return results;\n  }, {} as Targets);\n}\n"], "mappings": ";;;;;;;;AAAA;;AACA;;AAGO,SAASA,eAAT,CAAyBC,OAAzB,EAA0C;EAC/C,IAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;IAC/B,OAAOA,OAAP;EACD;;EAED,MAAMC,KAAK,GAAG,CAACC,OAAM,CAACC,KAAP,CAAaH,OAAb,CAAD,CAAd;;EACA,MAAMI,KAAK,GAAGF,OAAM,CAACE,KAAP,CAAaJ,OAAb,CAAd;;EACA,MAAMK,KAAK,GAAGH,OAAM,CAACG,KAAP,CAAaL,OAAb,CAAd;;EAEA,IAAII,KAAK,IAAIC,KAAb,EAAoB;IAClBJ,KAAK,CAACK,IAAN,CAAWF,KAAX;EACD;;EAED,IAAIC,KAAJ,EAAW;IACTJ,KAAK,CAACK,IAAN,CAAWD,KAAX;EACD;;EAED,OAAOJ,KAAK,CAACM,IAAN,CAAW,GAAX,CAAP;AACD;;AAEM,SAASC,eAAT,CAAyBC,OAAzB,EAAoD;EACzD,OAAOC,MAAM,CAACC,IAAP,CAAYF,OAAZ,EAAqBG,MAArB,CAA4B,CAACC,OAAD,EAAUC,MAAV,KAA6B;IAC9D,IAAIC,KAAK,GAAGN,OAAO,CAACK,MAAD,CAAnB;IAEA,MAAME,eAAe,GAEnBC,yBAAA,CAAiBH,MAAjB,CAFF;;IAGA,IAAI,OAAOC,KAAP,KAAiB,QAAjB,IAA6BC,eAAe,KAAKD,KAArD,EAA4D;MAC1DA,KAAK,GAAGhB,eAAe,CAACgB,KAAD,CAAvB;IACD;;IAEDF,OAAO,CAACC,MAAD,CAAP,GAAkBC,KAAlB;IACA,OAAOF,OAAP;EACD,CAZM,EAYJ,EAZI,CAAP;AAaD"}