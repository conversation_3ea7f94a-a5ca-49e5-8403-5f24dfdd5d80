{"version": 3, "names": ["path", "WeakMap", "scope", "clear", "clear<PERSON>ath", "clearScope"], "sources": ["../src/cache.ts"], "sourcesContent": ["export let path = new WeakMap();\nexport let scope = new WeakMap();\n\nexport function clear() {\n  clearPath();\n  clearScope();\n}\n\nexport function clearPath() {\n  path = new WeakMap();\n}\n\nexport function clearScope() {\n  scope = new WeakMap();\n}\n"], "mappings": ";;;;;;;;;AAAO,IAAIA,IAAI,GAAG,IAAIC,OAAJ,EAAX;;AACA,IAAIC,KAAK,GAAG,IAAID,OAAJ,EAAZ;;;AAEA,SAASE,KAAT,GAAiB;EACtBC,SAAS;EACTC,UAAU;AACX;;AAEM,SAASD,SAAT,GAAqB;EAC1B,eAAAJ,IAAI,GAAG,IAAIC,OAAJ,EAAP;AACD;;AAEM,SAASI,UAAT,GAAsB;EAC3B,gBAAAH,KAAK,GAAG,IAAID,OAAJ,EAAR;AACD"}