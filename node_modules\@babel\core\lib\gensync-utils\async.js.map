{"version": 3, "names": ["runGenerator", "gens<PERSON>", "item", "isAsync", "sync", "errback", "cb", "maybe<PERSON><PERSON>", "fn", "message", "args", "result", "apply", "isThenable", "Error", "async", "Promise", "resolve", "<PERSON><PERSON><PERSON>", "forwardAsync", "action", "g", "kind", "adapted", "onFirstPause", "name", "arity", "firstPause", "completed", "err", "value", "waitFor", "x", "val", "then"], "sources": ["../../src/gensync-utils/async.ts"], "sourcesContent": ["import gensync, { type <PERSON>sync, type Handler, type Callback } from \"gensync\";\n\ntype MaybePromise<T> = T | Promise<T>;\n\nconst runGenerator: {\n  sync<Return>(gen: <PERSON><PERSON><Return>): Return;\n  async<Return>(gen: <PERSON>ler<Return>): Promise<Return>;\n  errback<Return>(gen: <PERSON><PERSON><Return>, cb: Callback<Return>): void;\n} = gensync(function* (item: Handler<any>): Handler<any> {\n  return yield* item;\n});\n\n// This Gensync returns true if the current execution context is\n// asynchronous, otherwise it returns false.\nexport const isAsync = gensync({\n  sync: () => false,\n  errback: cb => cb(null, true),\n});\n\n// This function wraps any functions (which could be either synchronous or\n// asynchronous) with a Gensync. If the wrapped function returns a promise\n// but the current execution context is synchronous, it will throw the\n// provided error.\n// This is used to handle user-provided functions which could be asynchronous.\nexport function maybeAsync<Args extends unknown[], Return>(\n  fn: (...args: Args) => Return,\n  message: string,\n): Gensync<Args, Return> {\n  return gensync({\n    sync(...args) {\n      const result = fn.apply(this, args) as Return;\n      if (isThenable(result)) throw new Error(message);\n      return result;\n    },\n    async(...args) {\n      return Promise.resolve(fn.apply(this, args));\n    },\n  });\n}\n\nconst withKind = gensync({\n  sync: cb => cb(\"sync\"),\n  async: async cb => cb(\"async\"),\n}) as <T>(cb: (kind: \"sync\" | \"async\") => MaybePromise<T>) => Handler<T>;\n\n// This function wraps a generator (or a Gensync) into another function which,\n// when called, will run the provided generator in a sync or async way, depending\n// on the execution context where this forwardAsync function is called.\n// This is useful, for example, when passing a callback to a function which isn't\n// aware of gensync, but it only knows about synchronous and asynchronous functions.\n// An example is cache.using, which being exposed to the user must be as simple as\n// possible:\n//     yield* forwardAsync(gensyncFn, wrappedFn =>\n//       cache.using(x => {\n//         // Here we don't know about gensync. wrappedFn is a\n//         // normal sync or async function\n//         return wrappedFn(x);\n//       })\n//     )\nexport function forwardAsync<Args extends unknown[], Return>(\n  action: (...args: Args) => Handler<Return>,\n  cb: (\n    adapted: (...args: Args) => MaybePromise<Return>,\n  ) => MaybePromise<Return>,\n): Handler<Return> {\n  const g = gensync(action);\n  return withKind(kind => {\n    const adapted = g[kind];\n    return cb(adapted);\n  });\n}\n\n// If the given generator is executed asynchronously, the first time that it\n// is paused (i.e. When it yields a gensync generator which can't be run\n// synchronously), call the \"firstPause\" callback.\nexport const onFirstPause = gensync<\n  [gen: Handler<unknown>, firstPause: () => void],\n  unknown\n>({\n  name: \"onFirstPause\",\n  arity: 2,\n  sync: function (item) {\n    return runGenerator.sync(item);\n  },\n  errback: function (item, firstPause, cb) {\n    let completed = false;\n\n    runGenerator.errback(item, (err, value) => {\n      completed = true;\n      cb(err, value);\n    });\n\n    if (!completed) {\n      firstPause();\n    }\n  },\n}) as <T>(gen: Handler<T>, firstPause: () => void) => Handler<T>;\n\n// Wait for the given promise to be resolved\nexport const waitFor = gensync({\n  sync: x => x,\n  async: async x => x,\n}) as <T>(p: T | Promise<T>) => Handler<T>;\n\nexport function isThenable<T = any>(val: any): val is PromiseLike<T> {\n  return (\n    !!val &&\n    (typeof val === \"object\" || typeof val === \"function\") &&\n    !!val.then &&\n    typeof val.then === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;;;;;AAIA,MAAMA,YAIL,GAAGC,UAAA,CAAQ,WAAWC,IAAX,EAA6C;EACvD,OAAO,OAAOA,IAAd;AACD,CAFG,CAJJ;;AAUO,MAAMC,OAAO,GAAGF,UAAA,CAAQ;EAC7BG,IAAI,EAAE,MAAM,KADiB;EAE7BC,OAAO,EAAEC,EAAE,IAAIA,EAAE,CAAC,IAAD,EAAO,IAAP;AAFY,CAAR,CAAhB;;;;AAUA,SAASC,UAAT,CACLC,EADK,EAELC,OAFK,EAGkB;EACvB,OAAOR,UAAA,CAAQ;IACbG,IAAI,CAAC,GAAGM,IAAJ,EAAU;MACZ,MAAMC,MAAM,GAAGH,EAAE,CAACI,KAAH,CAAS,IAAT,EAAeF,IAAf,CAAf;MACA,IAAIG,UAAU,CAACF,MAAD,CAAd,EAAwB,MAAM,IAAIG,KAAJ,CAAUL,OAAV,CAAN;MACxB,OAAOE,MAAP;IACD,CALY;;IAMbI,KAAK,CAAC,GAAGL,IAAJ,EAAU;MACb,OAAOM,OAAO,CAACC,OAAR,CAAgBT,EAAE,CAACI,KAAH,CAAS,IAAT,EAAeF,IAAf,CAAhB,CAAP;IACD;;EARY,CAAR,CAAP;AAUD;;AAED,MAAMQ,QAAQ,GAAGjB,UAAA,CAAQ;EACvBG,IAAI,EAAEE,EAAE,IAAIA,EAAE,CAAC,MAAD,CADS;EAEvBS,KAAK;IAAA,6BAAE,WAAMT,EAAN;MAAA,OAAYA,EAAE,CAAC,OAAD,CAAd;IAAA,CAAF;;IAAA;MAAA;IAAA;EAAA;AAFkB,CAAR,CAAjB;;AAmBO,SAASa,YAAT,CACLC,MADK,EAELd,EAFK,EAKY;EACjB,MAAMe,CAAC,GAAGpB,UAAA,CAAQmB,MAAR,CAAV;;EACA,OAAOF,QAAQ,CAACI,IAAI,IAAI;IACtB,MAAMC,OAAO,GAAGF,CAAC,CAACC,IAAD,CAAjB;IACA,OAAOhB,EAAE,CAACiB,OAAD,CAAT;EACD,CAHc,CAAf;AAID;;AAKM,MAAMC,YAAY,GAAGvB,UAAA,CAG1B;EACAwB,IAAI,EAAE,cADN;EAEAC,KAAK,EAAE,CAFP;EAGAtB,IAAI,EAAE,UAAUF,IAAV,EAAgB;IACpB,OAAOF,YAAY,CAACI,IAAb,CAAkBF,IAAlB,CAAP;EACD,CALD;EAMAG,OAAO,EAAE,UAAUH,IAAV,EAAgByB,UAAhB,EAA4BrB,EAA5B,EAAgC;IACvC,IAAIsB,SAAS,GAAG,KAAhB;IAEA5B,YAAY,CAACK,OAAb,CAAqBH,IAArB,EAA2B,CAAC2B,GAAD,EAAMC,KAAN,KAAgB;MACzCF,SAAS,GAAG,IAAZ;MACAtB,EAAE,CAACuB,GAAD,EAAMC,KAAN,CAAF;IACD,CAHD;;IAKA,IAAI,CAACF,SAAL,EAAgB;MACdD,UAAU;IACX;EACF;AAjBD,CAH0B,CAArB;;;;AAwBA,MAAMI,OAAO,GAAG9B,UAAA,CAAQ;EAC7BG,IAAI,EAAE4B,CAAC,IAAIA,CADkB;EAE7BjB,KAAK;IAAA,8BAAE,WAAMiB,CAAN;MAAA,OAAWA,CAAX;IAAA,CAAF;;IAAA;MAAA;IAAA;EAAA;AAFwB,CAAR,CAAhB;;;;AAKA,SAASnB,UAAT,CAA6BoB,GAA7B,EAA8D;EACnE,OACE,CAAC,CAACA,GAAF,KACC,OAAOA,GAAP,KAAe,QAAf,IAA2B,OAAOA,GAAP,KAAe,UAD3C,KAEA,CAAC,CAACA,GAAG,CAACC,IAFN,IAGA,OAAOD,GAAG,CAACC,IAAX,KAAoB,UAJtB;AAMD"}