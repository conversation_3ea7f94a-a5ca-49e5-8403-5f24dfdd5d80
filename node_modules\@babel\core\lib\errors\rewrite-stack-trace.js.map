{"version": 3, "names": ["ErrorToString", "Function", "call", "bind", "Error", "prototype", "toString", "SUPPORTED", "captureStackTrace", "START_HIDNG", "STOP_HIDNG", "expectedErrors", "WeakSet", "virtualFrames", "WeakMap", "CallSite", "filename", "Object", "create", "isNative", "isConstructor", "isToplevel", "getFileName", "getLineNumber", "undefined", "getColumnNumber", "getFunctionName", "getMethodName", "getTypeName", "injcectVirtualStackFrame", "error", "frames", "get", "set", "push", "expectedError", "add", "beginHiddenCallStack", "fn", "defineProperty", "args", "setupPrepareStackTrace", "value", "endHiddenCallStack", "prepareStackTrace", "defaultPrepareStackTrace", "MIN_STACK_TRACE_LIMIT", "stackTraceLimit", "Math", "max", "stackTraceRewriter", "err", "trace", "newTrace", "isExpected", "has", "status", "i", "length", "name", "unshift", "join"], "sources": ["../../src/errors/rewrite-stack-trace.ts"], "sourcesContent": ["/**\n * This file uses the iternal V8 Stack Trace API (https://v8.dev/docs/stack-trace-api)\n * to provide utilities to rewrite the stack trace.\n * When this API is not present, all the functions in this file become noops.\n *\n * beginHiddenCallStack(fn) and endHiddenCallStack(fn) wrap their parameter to\n * mark an hidden portion of the stack trace. The function passed to\n * beginHiddenCallStack is the first hidden function, while the function passed\n * to endHiddenCallStack is the first shown function.\n *\n * When an error is thrown _outside_ of the hidden zone, everything between\n * beginHiddenCallStack and endHiddenCallStack will not be shown.\n * If an error is thrown _inside_ the hidden zone, then the whole stack trace\n * will be visible: this is to avoid hiding real bugs.\n * However, if an error inside the hidden zone is expected, it can be marked\n * with the expectedError(error) function to keep the hidden frames hidden.\n *\n * Consider this call stack (the outer function is the bottom one):\n *\n *   1. a()\n *   2. endHiddenCallStack(b)()\n *   3. c()\n *   4. beginHiddenCallStack(d)()\n *   5. e()\n *   6. f()\n *\n * - If a() throws an error, then its shown call stack will be \"a, b, e, f\"\n * - If b() throws an error, then its shown call stack will be \"b, e, f\"\n * - If c() throws an expected error, then its shown call stack will be \"e, f\"\n * - If c() throws an unexpected error, then its shown call stack will be \"c, d, e, f\"\n * - If d() throws an expected error, then its shown call stack will be \"e, f\"\n * - If d() throws an unexpected error, then its shown call stack will be \"d, e, f\"\n * - If e() throws an error, then its shown call stack will be \"e, f\"\n *\n * Additionally, an error can inject additional \"virtual\" stack frames using the\n * injcectVirtualStackFrame(error, filename) function: those are injected as a\n * replacement of the hidden frames.\n * In the example above, if we called injcectVirtualStackFrame(err, \"h\") and\n * injcectVirtualStackFrame(err, \"i\") on the expected error thrown by c(), its\n * shown call stack would have been \"h, i, e, f\".\n * This can be useful, for example, to report config validation errors as if they\n * were directly thrown in the config file.\n */\n\nconst ErrorToString = Function.call.bind(Error.prototype.toString);\n\nconst SUPPORTED = !!Error.captureStackTrace;\n\nconst START_HIDNG = \"startHiding - secret - don't use this - v1\";\nconst STOP_HIDNG = \"stopHiding - secret - don't use this - v1\";\n\ntype CallSite = Parameters<typeof Error.prepareStackTrace>[1][number];\n\nconst expectedErrors = new WeakSet<Error>();\nconst virtualFrames = new WeakMap<Error, CallSite[]>();\n\nfunction CallSite(filename: string): CallSite {\n  // We need to use a prototype otherwise it breaks source-map-support's internals\n  return Object.create({\n    isNative: () => false,\n    isConstructor: () => false,\n    isToplevel: () => true,\n    getFileName: () => filename,\n    getLineNumber: () => undefined,\n    getColumnNumber: () => undefined,\n    getFunctionName: () => undefined,\n    getMethodName: () => undefined,\n    getTypeName: () => undefined,\n    toString: () => filename,\n  } as CallSite);\n}\n\nexport function injcectVirtualStackFrame(error: Error, filename: string) {\n  if (!SUPPORTED) return;\n\n  let frames = virtualFrames.get(error);\n  if (!frames) virtualFrames.set(error, (frames = []));\n  frames.push(CallSite(filename));\n\n  return error;\n}\n\nexport function expectedError(error: Error) {\n  if (!SUPPORTED) return;\n  expectedErrors.add(error);\n  return error;\n}\n\nexport function beginHiddenCallStack<A extends unknown[], R>(\n  fn: (...args: A) => R,\n) {\n  if (!SUPPORTED) return fn;\n\n  return Object.defineProperty(\n    function (...args: A) {\n      setupPrepareStackTrace();\n      return fn(...args);\n    },\n    \"name\",\n    { value: STOP_HIDNG },\n  );\n}\n\nexport function endHiddenCallStack<A extends unknown[], R>(\n  fn: (...args: A) => R,\n) {\n  if (!SUPPORTED) return fn;\n\n  return Object.defineProperty(\n    function (...args: A) {\n      return fn(...args);\n    },\n    \"name\",\n    { value: START_HIDNG },\n  );\n}\n\nfunction setupPrepareStackTrace() {\n  // @ts-expect-error This function is a singleton\n  // eslint-disable-next-line no-func-assign\n  setupPrepareStackTrace = () => {};\n\n  const { prepareStackTrace = defaultPrepareStackTrace } = Error;\n\n  // We add some extra frames to Error.stackTraceLimit, so that we can\n  // always show some useful frames even after deleting ours.\n  // STACK_TRACE_LIMIT_DELTA should be around the maximum expected number\n  // of internal frames, and not too big because capturing the stack trace\n  // is slow (this is why Error.stackTraceLimit does not default to Infinity!).\n  // Increase it if needed.\n  // However, we only do it if the user did not explicitly set it to 0.\n  const MIN_STACK_TRACE_LIMIT = 50;\n  Error.stackTraceLimit &&= Math.max(\n    Error.stackTraceLimit,\n    MIN_STACK_TRACE_LIMIT,\n  );\n\n  Error.prepareStackTrace = function stackTraceRewriter(err, trace) {\n    let newTrace = [];\n\n    const isExpected = expectedErrors.has(err);\n    let status: \"showing\" | \"hiding\" | \"unknown\" = isExpected\n      ? \"hiding\"\n      : \"unknown\";\n    for (let i = 0; i < trace.length; i++) {\n      const name = trace[i].getFunctionName();\n      if (name === START_HIDNG) {\n        status = \"hiding\";\n      } else if (name === STOP_HIDNG) {\n        if (status === \"hiding\") {\n          status = \"showing\";\n          if (virtualFrames.has(err)) {\n            newTrace.unshift(...virtualFrames.get(err));\n          }\n        } else if (status === \"unknown\") {\n          // Unexpected internal error, show the full stack trace\n          newTrace = trace;\n          break;\n        }\n      } else if (status !== \"hiding\") {\n        newTrace.push(trace[i]);\n      }\n    }\n\n    return prepareStackTrace(err, newTrace);\n  };\n}\n\nfunction defaultPrepareStackTrace(err: Error, trace: CallSite[]) {\n  if (trace.length === 0) return ErrorToString(err);\n  return `${ErrorToString(err)}\\n    at ${trace.join(\"\\n    at \")}`;\n}\n"], "mappings": ";;;;;;;;;AA4CA,MAAMA,aAAa,GAAGC,QAAQ,CAACC,IAAT,CAAcC,IAAd,CAAmBC,KAAK,CAACC,SAAN,CAAgBC,QAAnC,CAAtB;AAEA,MAAMC,SAAS,GAAG,CAAC,CAACH,KAAK,CAACI,iBAA1B;AAEA,MAAMC,WAAW,GAAG,4CAApB;AACA,MAAMC,UAAU,GAAG,2CAAnB;AAIA,MAAMC,cAAc,GAAG,IAAIC,OAAJ,EAAvB;AACA,MAAMC,aAAa,GAAG,IAAIC,OAAJ,EAAtB;;AAEA,SAASC,QAAT,CAAkBC,QAAlB,EAA8C;EAE5C,OAAOC,MAAM,CAACC,MAAP,CAAc;IACnBC,QAAQ,EAAE,MAAM,KADG;IAEnBC,aAAa,EAAE,MAAM,KAFF;IAGnBC,UAAU,EAAE,MAAM,IAHC;IAInBC,WAAW,EAAE,MAAMN,QAJA;IAKnBO,aAAa,EAAE,MAAMC,SALF;IAMnBC,eAAe,EAAE,MAAMD,SANJ;IAOnBE,eAAe,EAAE,MAAMF,SAPJ;IAQnBG,aAAa,EAAE,MAAMH,SARF;IASnBI,WAAW,EAAE,MAAMJ,SATA;IAUnBlB,QAAQ,EAAE,MAAMU;EAVG,CAAd,CAAP;AAYD;;AAEM,SAASa,wBAAT,CAAkCC,KAAlC,EAAgDd,QAAhD,EAAkE;EACvE,IAAI,CAACT,SAAL,EAAgB;EAEhB,IAAIwB,MAAM,GAAGlB,aAAa,CAACmB,GAAd,CAAkBF,KAAlB,CAAb;EACA,IAAI,CAACC,MAAL,EAAalB,aAAa,CAACoB,GAAd,CAAkBH,KAAlB,EAA0BC,MAAM,GAAG,EAAnC;EACbA,MAAM,CAACG,IAAP,CAAYnB,QAAQ,CAACC,QAAD,CAApB;EAEA,OAAOc,KAAP;AACD;;AAEM,SAASK,aAAT,CAAuBL,KAAvB,EAAqC;EAC1C,IAAI,CAACvB,SAAL,EAAgB;EAChBI,cAAc,CAACyB,GAAf,CAAmBN,KAAnB;EACA,OAAOA,KAAP;AACD;;AAEM,SAASO,oBAAT,CACLC,EADK,EAEL;EACA,IAAI,CAAC/B,SAAL,EAAgB,OAAO+B,EAAP;EAEhB,OAAOrB,MAAM,CAACsB,cAAP,CACL,UAAU,GAAGC,IAAb,EAAsB;IACpBC,sBAAsB;IACtB,OAAOH,EAAE,CAAC,GAAGE,IAAJ,CAAT;EACD,CAJI,EAKL,MALK,EAML;IAAEE,KAAK,EAAEhC;EAAT,CANK,CAAP;AAQD;;AAEM,SAASiC,kBAAT,CACLL,EADK,EAEL;EACA,IAAI,CAAC/B,SAAL,EAAgB,OAAO+B,EAAP;EAEhB,OAAOrB,MAAM,CAACsB,cAAP,CACL,UAAU,GAAGC,IAAb,EAAsB;IACpB,OAAOF,EAAE,CAAC,GAAGE,IAAJ,CAAT;EACD,CAHI,EAIL,MAJK,EAKL;IAAEE,KAAK,EAAEjC;EAAT,CALK,CAAP;AAOD;;AAED,SAASgC,sBAAT,GAAkC;EAGhCA,sBAAsB,GAAG,MAAM,CAAE,CAAjC;;EAEA,MAAM;IAAEG,iBAAiB,GAAGC;EAAtB,IAAmDzC,KAAzD;EASA,MAAM0C,qBAAqB,GAAG,EAA9B;EACA1C,KAAK,CAAC2C,eAAN,KAAA3C,KAAK,CAAC2C,eAAN,GAA0BC,IAAI,CAACC,GAAL,CACxB7C,KAAK,CAAC2C,eADkB,EAExBD,qBAFwB,CAA1B;;EAKA1C,KAAK,CAACwC,iBAAN,GAA0B,SAASM,kBAAT,CAA4BC,GAA5B,EAAiCC,KAAjC,EAAwC;IAChE,IAAIC,QAAQ,GAAG,EAAf;IAEA,MAAMC,UAAU,GAAG3C,cAAc,CAAC4C,GAAf,CAAmBJ,GAAnB,CAAnB;IACA,IAAIK,MAAwC,GAAGF,UAAU,GACrD,QADqD,GAErD,SAFJ;;IAGA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAAK,CAACM,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;MACrC,MAAME,IAAI,GAAGP,KAAK,CAACK,CAAD,CAAL,CAAS/B,eAAT,EAAb;;MACA,IAAIiC,IAAI,KAAKlD,WAAb,EAA0B;QACxB+C,MAAM,GAAG,QAAT;MACD,CAFD,MAEO,IAAIG,IAAI,KAAKjD,UAAb,EAAyB;QAC9B,IAAI8C,MAAM,KAAK,QAAf,EAAyB;UACvBA,MAAM,GAAG,SAAT;;UACA,IAAI3C,aAAa,CAAC0C,GAAd,CAAkBJ,GAAlB,CAAJ,EAA4B;YAC1BE,QAAQ,CAACO,OAAT,CAAiB,GAAG/C,aAAa,CAACmB,GAAd,CAAkBmB,GAAlB,CAApB;UACD;QACF,CALD,MAKO,IAAIK,MAAM,KAAK,SAAf,EAA0B;UAE/BH,QAAQ,GAAGD,KAAX;UACA;QACD;MACF,CAXM,MAWA,IAAII,MAAM,KAAK,QAAf,EAAyB;QAC9BH,QAAQ,CAACnB,IAAT,CAAckB,KAAK,CAACK,CAAD,CAAnB;MACD;IACF;;IAED,OAAOb,iBAAiB,CAACO,GAAD,EAAME,QAAN,CAAxB;EACD,CA5BD;AA6BD;;AAED,SAASR,wBAAT,CAAkCM,GAAlC,EAA8CC,KAA9C,EAAiE;EAC/D,IAAIA,KAAK,CAACM,MAAN,KAAiB,CAArB,EAAwB,OAAO1D,aAAa,CAACmD,GAAD,CAApB;EACxB,OAAQ,GAAEnD,aAAa,CAACmD,GAAD,CAAM,YAAWC,KAAK,CAACS,IAAN,CAAW,WAAX,CAAwB,EAAhE;AACD"}