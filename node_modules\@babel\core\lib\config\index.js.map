{"version": 3, "names": ["loadOptionsRunner", "gens<PERSON>", "opts", "config", "loadFullConfig", "options", "createConfigItemRunner", "createConfigItemImpl", "maybeErrback", "runner", "arg<PERSON>r<PERSON><PERSON><PERSON>", "maybe<PERSON><PERSON><PERSON>", "arg", "callback", "undefined", "errback", "sync", "loadPartialConfig", "loadPartialConfigRunner", "loadPartialConfigSync", "loadPartialConfigAsync", "async", "loadOptions", "loadOptionsSync", "loadOptionsAsync", "createConfigItemSync", "createConfigItemAsync", "createConfigItem", "target"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["import gensync, { type Handler, type Callback } from \"gensync\";\n\nexport type {\n  ResolvedConfig,\n  InputOptions,\n  PluginPasses,\n  Plugin,\n} from \"./full\";\n\nimport type { PluginTarget } from \"./validation/options\";\n\nimport type {\n  PluginAP<PERSON> as basePluginAPI,\n  PresetAPI as basePresetAPI,\n} from \"./helpers/config-api\";\nexport type { PluginObject } from \"./validation/plugins\";\ntype PluginAPI = basePluginAPI & typeof import(\"..\");\ntype PresetAPI = basePresetAPI & typeof import(\"..\");\nexport type { PluginAPI, PresetAPI };\n// todo: may need to refine PresetObject to be a subset of ValidatedOptions\nexport type {\n  CallerMetadata,\n  ValidatedOptions as PresetObject,\n} from \"./validation/options\";\n\nimport loadFullConfig, { type ResolvedConfig } from \"./full\";\nimport { loadPartialConfig as loadPartialConfigRunner } from \"./partial\";\n\nexport { loadFullConfig as default };\nexport type { PartialConfig } from \"./partial\";\n\nimport { createConfigItem as createConfigItemImpl } from \"./item\";\nimport type { ConfigItem } from \"./item\";\n\nconst loadOptionsRunner = gensync(function* (\n  opts: unknown,\n): Handler<ResolvedConfig | null> {\n  const config = yield* loadFullConfig(opts);\n  // NOTE: We want to return \"null\" explicitly, while ?. alone returns undefined\n  return config?.options ?? null;\n});\n\nconst createConfigItemRunner = gensync(createConfigItemImpl);\n\nconst maybeErrback =\n  <Arg, Return>(runner: gensync.Gensync<[Arg], Return>) =>\n  (argOrCallback: Arg | Callback<Return>, maybeCallback?: Callback<Return>) => {\n    let arg: Arg | undefined;\n    let callback: Callback<Return>;\n    if (maybeCallback === undefined && typeof argOrCallback === \"function\") {\n      callback = argOrCallback as Callback<Return>;\n      arg = undefined;\n    } else {\n      callback = maybeCallback;\n      arg = argOrCallback as Arg;\n    }\n    return callback ? runner.errback(arg, callback) : runner.sync(arg);\n  };\n\nexport const loadPartialConfig = maybeErrback(loadPartialConfigRunner);\nexport const loadPartialConfigSync = loadPartialConfigRunner.sync;\nexport const loadPartialConfigAsync = loadPartialConfigRunner.async;\n\nexport const loadOptions = maybeErrback(loadOptionsRunner);\nexport const loadOptionsSync = loadOptionsRunner.sync;\nexport const loadOptionsAsync = loadOptionsRunner.async;\n\nexport const createConfigItemSync = createConfigItemRunner.sync;\nexport const createConfigItemAsync = createConfigItemRunner.async;\nexport function createConfigItem(\n  target: PluginTarget,\n  options: Parameters<typeof createConfigItemImpl>[1],\n  callback?: (err: Error, val: ConfigItem | null) => void,\n) {\n  if (callback !== undefined) {\n    return createConfigItemRunner.errback(target, options, callback);\n  } else if (typeof options === \"function\") {\n    return createConfigItemRunner.errback(target, undefined, callback);\n  } else {\n    return createConfigItemRunner.sync(target, options);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAyBA;;AACA;;AAKA;;AAGA,MAAMA,iBAAiB,GAAGC,UAAA,CAAQ,WAChCC,IADgC,EAEA;EAAA;;EAChC,MAAMC,MAAM,GAAG,OAAO,IAAAC,aAAA,EAAeF,IAAf,CAAtB;EAEA,0BAAOC,MAAP,oBAAOA,MAAM,CAAEE,OAAf,8BAA0B,IAA1B;AACD,CANyB,CAA1B;;AAQA,MAAMC,sBAAsB,GAAGL,UAAA,CAAQM,sBAAR,CAA/B;;AAEA,MAAMC,YAAY,GACFC,MAAd,IACA,CAACC,aAAD,EAAwCC,aAAxC,KAA6E;EAC3E,IAAIC,GAAJ;EACA,IAAIC,QAAJ;;EACA,IAAIF,aAAa,KAAKG,SAAlB,IAA+B,OAAOJ,aAAP,KAAyB,UAA5D,EAAwE;IACtEG,QAAQ,GAAGH,aAAX;IACAE,GAAG,GAAGE,SAAN;EACD,CAHD,MAGO;IACLD,QAAQ,GAAGF,aAAX;IACAC,GAAG,GAAGF,aAAN;EACD;;EACD,OAAOG,QAAQ,GAAGJ,MAAM,CAACM,OAAP,CAAeH,GAAf,EAAoBC,QAApB,CAAH,GAAmCJ,MAAM,CAACO,IAAP,CAAYJ,GAAZ,CAAlD;AACD,CAbH;;AAeO,MAAMK,iBAAiB,GAAGT,YAAY,CAACU,0BAAD,CAAtC;;AACA,MAAMC,qBAAqB,GAAGD,0BAAA,CAAwBF,IAAtD;;AACA,MAAMI,sBAAsB,GAAGF,0BAAA,CAAwBG,KAAvD;;AAEA,MAAMC,WAAW,GAAGd,YAAY,CAACR,iBAAD,CAAhC;;AACA,MAAMuB,eAAe,GAAGvB,iBAAiB,CAACgB,IAA1C;;AACA,MAAMQ,gBAAgB,GAAGxB,iBAAiB,CAACqB,KAA3C;;AAEA,MAAMI,oBAAoB,GAAGnB,sBAAsB,CAACU,IAApD;;AACA,MAAMU,qBAAqB,GAAGpB,sBAAsB,CAACe,KAArD;;;AACA,SAASM,gBAAT,CACLC,MADK,EAELvB,OAFK,EAGLQ,QAHK,EAIL;EACA,IAAIA,QAAQ,KAAKC,SAAjB,EAA4B;IAC1B,OAAOR,sBAAsB,CAACS,OAAvB,CAA+Ba,MAA/B,EAAuCvB,OAAvC,EAAgDQ,QAAhD,CAAP;EACD,CAFD,MAEO,IAAI,OAAOR,OAAP,KAAmB,UAAvB,EAAmC;IACxC,OAAOC,sBAAsB,CAACS,OAAvB,CAA+Ba,MAA/B,EAAuCd,SAAvC,EAAkDD,QAAlD,CAAP;EACD,CAFM,MAEA;IACL,OAAOP,sBAAsB,CAACU,IAAvB,CAA4BY,MAA5B,EAAoCvB,OAApC,CAAP;EACD;AACF"}