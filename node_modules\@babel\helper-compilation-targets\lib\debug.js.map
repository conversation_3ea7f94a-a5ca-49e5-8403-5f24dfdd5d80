{"version": 3, "names": ["getInclusionReasons", "item", "targetVersions", "list", "minVersions", "Object", "keys", "reduce", "result", "env", "minVersion", "getLowestImplementedVersion", "targetVersion", "prettifyVersion", "minIsUnreleased", "isUnreleasedVersion", "targetIsUnreleased", "semver", "lt", "toString", "semverify"], "sources": ["../src/debug.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { prettifyVersion } from \"./pretty\";\nimport {\n  semverify,\n  isUnreleasedVersion,\n  getLowestImplementedVersion,\n} from \"./utils\";\nimport type { Target, Targets } from \"./types\";\n\nexport function getInclusionReasons(\n  item: string,\n  targetVersions: Targets,\n  list: { [key: string]: Targets },\n) {\n  const minVersions = list[item] || ({} as Targets);\n\n  return (Object.keys(targetVersions) as Target[]).reduce((result, env) => {\n    const minVersion = getLowestImplementedVersion(minVersions, env);\n    const targetVersion = targetVersions[env];\n\n    if (!minVersion) {\n      result[env] = prettifyVersion(targetVersion);\n    } else {\n      const minIsUnreleased = isUnreleasedVersion(minVersion, env);\n      const targetIsUnreleased = isUnreleasedVersion(targetVersion, env);\n\n      if (\n        !targetIsUnreleased &&\n        (minIsUnreleased ||\n          semver.lt(targetVersion.toString(), semverify(minVersion)))\n      ) {\n        result[env] = prettifyVersion(targetVersion);\n      }\n    }\n\n    return result;\n  }, {} as Partial<Record<Target, string>>);\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAOO,SAASA,mBAAT,CACLC,IADK,EAELC,cAFK,EAGLC,IAHK,EAIL;EACA,MAAMC,WAAW,GAAGD,IAAI,CAACF,IAAD,CAAJ,IAAe,EAAnC;EAEA,OAAQI,MAAM,CAACC,IAAP,CAAYJ,cAAZ,CAAD,CAA0CK,MAA1C,CAAiD,CAACC,MAAD,EAASC,GAAT,KAAiB;IACvE,MAAMC,UAAU,GAAG,IAAAC,kCAAA,EAA4BP,WAA5B,EAAyCK,GAAzC,CAAnB;IACA,MAAMG,aAAa,GAAGV,cAAc,CAACO,GAAD,CAApC;;IAEA,IAAI,CAACC,UAAL,EAAiB;MACfF,MAAM,CAACC,GAAD,CAAN,GAAc,IAAAI,uBAAA,EAAgBD,aAAhB,CAAd;IACD,CAFD,MAEO;MACL,MAAME,eAAe,GAAG,IAAAC,0BAAA,EAAoBL,UAApB,EAAgCD,GAAhC,CAAxB;MACA,MAAMO,kBAAkB,GAAG,IAAAD,0BAAA,EAAoBH,aAApB,EAAmCH,GAAnC,CAA3B;;MAEA,IACE,CAACO,kBAAD,KACCF,eAAe,IACdG,OAAM,CAACC,EAAP,CAAUN,aAAa,CAACO,QAAd,EAAV,EAAoC,IAAAC,gBAAA,EAAUV,UAAV,CAApC,CAFF,CADF,EAIE;QACAF,MAAM,CAACC,GAAD,CAAN,GAAc,IAAAI,uBAAA,EAAgBD,aAAhB,CAAd;MACD;IACF;;IAED,OAAOJ,MAAP;EACD,CApBM,EAoBJ,EApBI,CAAP;AAqBD"}