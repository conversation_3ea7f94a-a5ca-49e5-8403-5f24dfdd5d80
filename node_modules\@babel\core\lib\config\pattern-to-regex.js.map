{"version": 3, "names": ["sep", "path", "endSep", "substitution", "starPat", "starPatLast", "starStarPat", "starStarPatLast", "escapeRegExp", "string", "replace", "pathToPattern", "pattern", "dirname", "parts", "resolve", "split", "RegExp", "map", "part", "i", "last", "length", "indexOf", "slice", "join"], "sources": ["../../src/config/pattern-to-regex.ts"], "sourcesContent": ["import path from \"path\";\n\nconst sep = `\\\\${path.sep}`;\nconst endSep = `(?:${sep}|$)`;\n\nconst substitution = `[^${sep}]+`;\n\nconst starPat = `(?:${substitution}${sep})`;\nconst starPatLast = `(?:${substitution}${endSep})`;\n\nconst starStarPat = `${starPat}*?`;\nconst starStarPatLast = `${starPat}*?${starPatLast}?`;\n\nfunction escapeRegExp(string: string) {\n  return string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\");\n}\n\n/**\n * Implement basic pattern matching that will allow users to do the simple\n * tests with * and **. If users want full complex pattern matching, then can\n * always use regex matching, or function validation.\n */\nexport default function pathToPattern(\n  pattern: string,\n  dirname: string,\n): RegExp {\n  const parts = path.resolve(dirname, pattern).split(path.sep);\n\n  return new RegExp(\n    [\n      \"^\",\n      ...parts.map((part, i) => {\n        const last = i === parts.length - 1;\n\n        // ** matches 0 or more path parts.\n        if (part === \"**\") return last ? starStarPatLast : starStarPat;\n\n        // * matches 1 path part.\n        if (part === \"*\") return last ? starPatLast : starPat;\n\n        // *.ext matches a wildcard with an extension.\n        if (part.indexOf(\"*.\") === 0) {\n          return (\n            substitution + escapeRegExp(part.slice(1)) + (last ? endSep : sep)\n          );\n        }\n\n        // Otherwise match the pattern text.\n        return escapeRegExp(part) + (last ? endSep : sep);\n      }),\n    ].join(\"\"),\n  );\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA,MAAMA,GAAG,GAAI,KAAIC,OAAA,CAAKD,GAAI,EAA1B;AACA,MAAME,MAAM,GAAI,MAAKF,GAAI,KAAzB;AAEA,MAAMG,YAAY,GAAI,KAAIH,GAAI,IAA9B;AAEA,MAAMI,OAAO,GAAI,MAAKD,YAAa,GAAEH,GAAI,GAAzC;AACA,MAAMK,WAAW,GAAI,MAAKF,YAAa,GAAED,MAAO,GAAhD;AAEA,MAAMI,WAAW,GAAI,GAAEF,OAAQ,IAA/B;AACA,MAAMG,eAAe,GAAI,GAAEH,OAAQ,KAAIC,WAAY,GAAnD;;AAEA,SAASG,YAAT,CAAsBC,MAAtB,EAAsC;EACpC,OAAOA,MAAM,CAACC,OAAP,CAAe,qBAAf,EAAsC,MAAtC,CAAP;AACD;;AAOc,SAASC,aAAT,CACbC,OADa,EAEbC,OAFa,EAGL;EACR,MAAMC,KAAK,GAAGb,OAAA,CAAKc,OAAL,CAAaF,OAAb,EAAsBD,OAAtB,EAA+BI,KAA/B,CAAqCf,OAAA,CAAKD,GAA1C,CAAd;;EAEA,OAAO,IAAIiB,MAAJ,CACL,CACE,GADF,EAEE,GAAGH,KAAK,CAACI,GAAN,CAAU,CAACC,IAAD,EAAOC,CAAP,KAAa;IACxB,MAAMC,IAAI,GAAGD,CAAC,KAAKN,KAAK,CAACQ,MAAN,GAAe,CAAlC;IAGA,IAAIH,IAAI,KAAK,IAAb,EAAmB,OAAOE,IAAI,GAAGd,eAAH,GAAqBD,WAAhC;IAGnB,IAAIa,IAAI,KAAK,GAAb,EAAkB,OAAOE,IAAI,GAAGhB,WAAH,GAAiBD,OAA5B;;IAGlB,IAAIe,IAAI,CAACI,OAAL,CAAa,IAAb,MAAuB,CAA3B,EAA8B;MAC5B,OACEpB,YAAY,GAAGK,YAAY,CAACW,IAAI,CAACK,KAAL,CAAW,CAAX,CAAD,CAA3B,IAA8CH,IAAI,GAAGnB,MAAH,GAAYF,GAA9D,CADF;IAGD;;IAGD,OAAOQ,YAAY,CAACW,IAAD,CAAZ,IAAsBE,IAAI,GAAGnB,MAAH,GAAYF,GAAtC,CAAP;EACD,CAlBE,CAFL,EAqBEyB,IArBF,CAqBO,EArBP,CADK,CAAP;AAwBD"}