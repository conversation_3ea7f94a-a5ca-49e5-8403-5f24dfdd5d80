{"version": 3, "names": ["pluginNameMap", "asyncDoExpressions", "syntax", "name", "url", "classProperties", "transform", "classPrivateProperties", "classPrivateMethods", "classStaticBlock", "decimal", "decorators", "doExpressions", "dynamicImport", "exportDefaultFrom", "exportNamespaceFrom", "flow", "functionBind", "functionSent", "importMeta", "jsx", "importAssertions", "moduleStringNames", "numericSeparator", "optionalChaining", "pipelineOperator", "privateIn", "recordAndTuple", "regexpUnicodeSets", "throwExpressions", "typescript", "asyncGenerators", "logicalAssignment", "nullishCoalescingOperator", "objectRestSpread", "optionalCatchBinding", "getNameURLCombination", "generateMissingPluginMessage", "missing<PERSON><PERSON><PERSON><PERSON><PERSON>", "loc", "codeFrame", "helpMessage", "line", "column", "pluginInfo", "syntaxPlugin", "transformPlugin", "syntaxPluginInfo", "transformPluginInfo", "sectionType", "startsWith"], "sources": ["../../../src/parser/util/missing-plugin-helper.ts"], "sourcesContent": ["const pluginNameMap: Record<\n  string,\n  Partial<Record<\"syntax\" | \"transform\", Record<\"name\" | \"url\", string>>>\n> = {\n  asyncDoExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-async-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-do-expressions\",\n    },\n  },\n  classProperties: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-class-properties\",\n    },\n  },\n  classPrivateProperties: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-class-properties\",\n    },\n  },\n  classPrivateMethods: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-properties\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-private-methods\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-private-methods\",\n    },\n  },\n  classStaticBlock: {\n    syntax: {\n      name: \"@babel/plugin-syntax-class-static-block\",\n      url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-syntax-class-static-block\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-class-static-block\",\n      url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-proposal-class-static-block\",\n    },\n  },\n  decimal: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decimal\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decimal\",\n    },\n  },\n  decorators: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decorators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-decorators\",\n    },\n  },\n  doExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-do-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-do-expressions\",\n    },\n  },\n  dynamicImport: {\n    syntax: {\n      name: \"@babel/plugin-syntax-dynamic-import\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-dynamic-import\",\n    },\n  },\n  exportDefaultFrom: {\n    syntax: {\n      name: \"@babel/plugin-syntax-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-default-from\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-default-from\",\n    },\n  },\n  exportNamespaceFrom: {\n    syntax: {\n      name: \"@babel/plugin-syntax-export-namespace-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-namespace-from\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-export-namespace-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-namespace-from\",\n    },\n  },\n  flow: {\n    syntax: {\n      name: \"@babel/plugin-syntax-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-flow\",\n    },\n    transform: {\n      name: \"@babel/preset-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-flow\",\n    },\n  },\n  functionBind: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-bind\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-bind\",\n    },\n  },\n  functionSent: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-sent\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-sent\",\n    },\n  },\n  importMeta: {\n    syntax: {\n      name: \"@babel/plugin-syntax-import-meta\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-meta\",\n    },\n  },\n  jsx: {\n    syntax: {\n      name: \"@babel/plugin-syntax-jsx\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-jsx\",\n    },\n    transform: {\n      name: \"@babel/preset-react\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-react\",\n    },\n  },\n  importAssertions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-import-assertions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-assertions\",\n    },\n  },\n  moduleStringNames: {\n    syntax: {\n      name: \"@babel/plugin-syntax-module-string-names\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-module-string-names\",\n    },\n  },\n  numericSeparator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-numeric-separator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-numeric-separator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-numeric-separator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-numeric-separator\",\n    },\n  },\n  optionalChaining: {\n    syntax: {\n      name: \"@babel/plugin-syntax-optional-chaining\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-chaining\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-optional-chaining\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-optional-chaining\",\n    },\n  },\n  pipelineOperator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-pipeline-operator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-pipeline-operator\",\n    },\n  },\n  privateIn: {\n    syntax: {\n      name: \"@babel/plugin-syntax-private-property-in-object\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-private-property-in-object\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-private-property-in-object\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-private-property-in-object\",\n    },\n  },\n  recordAndTuple: {\n    syntax: {\n      name: \"@babel/plugin-syntax-record-and-tuple\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-record-and-tuple\",\n    },\n  },\n  regexpUnicodeSets: {\n    syntax: {\n      name: \"@babel/plugin-syntax-unicode-sets-regex\",\n      url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-syntax-unicode-sets-regex/README.md\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-unicode-sets-regex\",\n      url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-proposalunicode-sets-regex/README.md\",\n    },\n  },\n  throwExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-throw-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-throw-expressions\",\n    },\n  },\n  typescript: {\n    syntax: {\n      name: \"@babel/plugin-syntax-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-typescript\",\n    },\n    transform: {\n      name: \"@babel/preset-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-typescript\",\n    },\n  },\n\n  // TODO: This plugins are now supported by default by @babel/parser: they can\n  // be removed from this list. Although removing them isn't a breaking change,\n  // it's better to keep a nice error message for users using older versions of\n  // the parser. They can be removed in Babel 8.\n  asyncGenerators: {\n    syntax: {\n      name: \"@babel/plugin-syntax-async-generators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-generators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-async-generator-functions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-async-generator-functions\",\n    },\n  },\n  logicalAssignment: {\n    syntax: {\n      name: \"@babel/plugin-syntax-logical-assignment-operators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-logical-assignment-operators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-logical-assignment-operators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-logical-assignment-operators\",\n    },\n  },\n  nullishCoalescingOperator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-nullish-coalescing-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-nullish-coalescing-operator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-nullish-coalescing-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-nullish-coalescing-opearator\",\n    },\n  },\n  objectRestSpread: {\n    syntax: {\n      name: \"@babel/plugin-syntax-object-rest-spread\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-object-rest-spread\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-object-rest-spread\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-object-rest-spread\",\n    },\n  },\n  optionalCatchBinding: {\n    syntax: {\n      name: \"@babel/plugin-syntax-optional-catch-binding\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-catch-binding\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-optional-catch-binding\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-optional-catch-binding\",\n    },\n  },\n};\n\n//todo: we don't have plugin-syntax-private-property-in-object\npluginNameMap.privateIn.syntax = pluginNameMap.privateIn.transform;\n\nconst getNameURLCombination = ({ name, url }: { name: string; url: string }) =>\n  `${name} (${url})`;\n\n/*\nReturns a string of the format:\nSupport for the experimental syntax [@babel/parser plugin name] isn't currently enabled ([loc]):\n\n[code frame]\n\nAdd [npm package name] ([url]) to the 'plugins' section of your Babel config\nto enable [parsing|transformation].\n*/\nexport default function generateMissingPluginMessage(\n  missingPluginName: string,\n  loc: {\n    line: number;\n    column: number;\n  },\n  codeFrame: string,\n): string {\n  let helpMessage =\n    `Support for the experimental syntax '${missingPluginName}' isn't currently enabled ` +\n    `(${loc.line}:${loc.column + 1}):\\n\\n` +\n    codeFrame;\n  const pluginInfo = pluginNameMap[missingPluginName];\n  if (pluginInfo) {\n    const { syntax: syntaxPlugin, transform: transformPlugin } = pluginInfo;\n    if (syntaxPlugin) {\n      const syntaxPluginInfo = getNameURLCombination(syntaxPlugin);\n      if (transformPlugin) {\n        const transformPluginInfo = getNameURLCombination(transformPlugin);\n        const sectionType = transformPlugin.name.startsWith(\"@babel/plugin\")\n          ? \"plugins\"\n          : \"presets\";\n        helpMessage += `\\n\\nAdd ${transformPluginInfo} to the '${sectionType}' section of your Babel config to enable transformation.\nIf you want to leave it as-is, add ${syntaxPluginInfo} to the 'plugins' section to enable parsing.`;\n      } else {\n        helpMessage +=\n          `\\n\\nAdd ${syntaxPluginInfo} to the 'plugins' section of your Babel config ` +\n          `to enable parsing.`;\n      }\n    }\n  }\n  return helpMessage;\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,aAGL,GAAG;EACFC,kBAAkB,EAAE;IAClBC,MAAM,EAAE;MACNC,IAAI,EAAE,2CADA;MAENC,GAAG,EAAE;IAFC;EADU,CADlB;EAOFC,eAAe,EAAE;IACfH,MAAM,EAAE;MACNC,IAAI,EAAE,uCADA;MAENC,GAAG,EAAE;IAFC,CADO;IAKfE,SAAS,EAAE;MACTH,IAAI,EAAE,yCADG;MAETC,GAAG,EAAE;IAFI;EALI,CAPf;EAiBFG,sBAAsB,EAAE;IACtBL,MAAM,EAAE;MACNC,IAAI,EAAE,uCADA;MAENC,GAAG,EAAE;IAFC,CADc;IAKtBE,SAAS,EAAE;MACTH,IAAI,EAAE,yCADG;MAETC,GAAG,EAAE;IAFI;EALW,CAjBtB;EA2BFI,mBAAmB,EAAE;IACnBN,MAAM,EAAE;MACNC,IAAI,EAAE,uCADA;MAENC,GAAG,EAAE;IAFC,CADW;IAKnBE,SAAS,EAAE;MACTH,IAAI,EAAE,wCADG;MAETC,GAAG,EAAE;IAFI;EALQ,CA3BnB;EAqCFK,gBAAgB,EAAE;IAChBP,MAAM,EAAE;MACNC,IAAI,EAAE,yCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,2CADG;MAETC,GAAG,EAAE;IAFI;EALK,CArChB;EA+CFM,OAAO,EAAE;IACPR,MAAM,EAAE;MACNC,IAAI,EAAE,8BADA;MAENC,GAAG,EAAE;IAFC;EADD,CA/CP;EAqDFO,UAAU,EAAE;IACVT,MAAM,EAAE;MACNC,IAAI,EAAE,iCADA;MAENC,GAAG,EAAE;IAFC,CADE;IAKVE,SAAS,EAAE;MACTH,IAAI,EAAE,mCADG;MAETC,GAAG,EAAE;IAFI;EALD,CArDV;EA+DFQ,aAAa,EAAE;IACbV,MAAM,EAAE;MACNC,IAAI,EAAE,qCADA;MAENC,GAAG,EAAE;IAFC,CADK;IAKbE,SAAS,EAAE;MACTH,IAAI,EAAE,uCADG;MAETC,GAAG,EAAE;IAFI;EALE,CA/Db;EAyEFS,aAAa,EAAE;IACbX,MAAM,EAAE;MACNC,IAAI,EAAE,qCADA;MAENC,GAAG,EAAE;IAFC;EADK,CAzEb;EA+EFU,iBAAiB,EAAE;IACjBZ,MAAM,EAAE;MACNC,IAAI,EAAE,0CADA;MAENC,GAAG,EAAE;IAFC,CADS;IAKjBE,SAAS,EAAE;MACTH,IAAI,EAAE,4CADG;MAETC,GAAG,EAAE;IAFI;EALM,CA/EjB;EAyFFW,mBAAmB,EAAE;IACnBb,MAAM,EAAE;MACNC,IAAI,EAAE,4CADA;MAENC,GAAG,EAAE;IAFC,CADW;IAKnBE,SAAS,EAAE;MACTH,IAAI,EAAE,8CADG;MAETC,GAAG,EAAE;IAFI;EALQ,CAzFnB;EAmGFY,IAAI,EAAE;IACJd,MAAM,EAAE;MACNC,IAAI,EAAE,2BADA;MAENC,GAAG,EAAE;IAFC,CADJ;IAKJE,SAAS,EAAE;MACTH,IAAI,EAAE,oBADG;MAETC,GAAG,EAAE;IAFI;EALP,CAnGJ;EA6GFa,YAAY,EAAE;IACZf,MAAM,EAAE;MACNC,IAAI,EAAE,oCADA;MAENC,GAAG,EAAE;IAFC,CADI;IAKZE,SAAS,EAAE;MACTH,IAAI,EAAE,sCADG;MAETC,GAAG,EAAE;IAFI;EALC,CA7GZ;EAuHFc,YAAY,EAAE;IACZhB,MAAM,EAAE;MACNC,IAAI,EAAE,oCADA;MAENC,GAAG,EAAE;IAFC,CADI;IAKZE,SAAS,EAAE;MACTH,IAAI,EAAE,sCADG;MAETC,GAAG,EAAE;IAFI;EALC,CAvHZ;EAiIFe,UAAU,EAAE;IACVjB,MAAM,EAAE;MACNC,IAAI,EAAE,kCADA;MAENC,GAAG,EAAE;IAFC;EADE,CAjIV;EAuIFgB,GAAG,EAAE;IACHlB,MAAM,EAAE;MACNC,IAAI,EAAE,0BADA;MAENC,GAAG,EAAE;IAFC,CADL;IAKHE,SAAS,EAAE;MACTH,IAAI,EAAE,qBADG;MAETC,GAAG,EAAE;IAFI;EALR,CAvIH;EAiJFiB,gBAAgB,EAAE;IAChBnB,MAAM,EAAE;MACNC,IAAI,EAAE,wCADA;MAENC,GAAG,EAAE;IAFC;EADQ,CAjJhB;EAuJFkB,iBAAiB,EAAE;IACjBpB,MAAM,EAAE;MACNC,IAAI,EAAE,0CADA;MAENC,GAAG,EAAE;IAFC;EADS,CAvJjB;EA6JFmB,gBAAgB,EAAE;IAChBrB,MAAM,EAAE;MACNC,IAAI,EAAE,wCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,0CADG;MAETC,GAAG,EAAE;IAFI;EALK,CA7JhB;EAuKFoB,gBAAgB,EAAE;IAChBtB,MAAM,EAAE;MACNC,IAAI,EAAE,wCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,0CADG;MAETC,GAAG,EAAE;IAFI;EALK,CAvKhB;EAiLFqB,gBAAgB,EAAE;IAChBvB,MAAM,EAAE;MACNC,IAAI,EAAE,wCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,0CADG;MAETC,GAAG,EAAE;IAFI;EALK,CAjLhB;EA2LFsB,SAAS,EAAE;IACTxB,MAAM,EAAE;MACNC,IAAI,EAAE,iDADA;MAENC,GAAG,EAAE;IAFC,CADC;IAKTE,SAAS,EAAE;MACTH,IAAI,EAAE,mDADG;MAETC,GAAG,EAAE;IAFI;EALF,CA3LT;EAqMFuB,cAAc,EAAE;IACdzB,MAAM,EAAE;MACNC,IAAI,EAAE,uCADA;MAENC,GAAG,EAAE;IAFC;EADM,CArMd;EA2MFwB,iBAAiB,EAAE;IACjB1B,MAAM,EAAE;MACNC,IAAI,EAAE,yCADA;MAENC,GAAG,EAAE;IAFC,CADS;IAKjBE,SAAS,EAAE;MACTH,IAAI,EAAE,2CADG;MAETC,GAAG,EAAE;IAFI;EALM,CA3MjB;EAqNFyB,gBAAgB,EAAE;IAChB3B,MAAM,EAAE;MACNC,IAAI,EAAE,wCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,0CADG;MAETC,GAAG,EAAE;IAFI;EALK,CArNhB;EA+NF0B,UAAU,EAAE;IACV5B,MAAM,EAAE;MACNC,IAAI,EAAE,iCADA;MAENC,GAAG,EAAE;IAFC,CADE;IAKVE,SAAS,EAAE;MACTH,IAAI,EAAE,0BADG;MAETC,GAAG,EAAE;IAFI;EALD,CA/NV;EA8OF2B,eAAe,EAAE;IACf7B,MAAM,EAAE;MACNC,IAAI,EAAE,uCADA;MAENC,GAAG,EAAE;IAFC,CADO;IAKfE,SAAS,EAAE;MACTH,IAAI,EAAE,kDADG;MAETC,GAAG,EAAE;IAFI;EALI,CA9Of;EAwPF4B,iBAAiB,EAAE;IACjB9B,MAAM,EAAE;MACNC,IAAI,EAAE,mDADA;MAENC,GAAG,EAAE;IAFC,CADS;IAKjBE,SAAS,EAAE;MACTH,IAAI,EAAE,qDADG;MAETC,GAAG,EAAE;IAFI;EALM,CAxPjB;EAkQF6B,yBAAyB,EAAE;IACzB/B,MAAM,EAAE;MACNC,IAAI,EAAE,kDADA;MAENC,GAAG,EAAE;IAFC,CADiB;IAKzBE,SAAS,EAAE;MACTH,IAAI,EAAE,oDADG;MAETC,GAAG,EAAE;IAFI;EALc,CAlQzB;EA4QF8B,gBAAgB,EAAE;IAChBhC,MAAM,EAAE;MACNC,IAAI,EAAE,yCADA;MAENC,GAAG,EAAE;IAFC,CADQ;IAKhBE,SAAS,EAAE;MACTH,IAAI,EAAE,2CADG;MAETC,GAAG,EAAE;IAFI;EALK,CA5QhB;EAsRF+B,oBAAoB,EAAE;IACpBjC,MAAM,EAAE;MACNC,IAAI,EAAE,6CADA;MAENC,GAAG,EAAE;IAFC,CADY;IAKpBE,SAAS,EAAE;MACTH,IAAI,EAAE,+CADG;MAETC,GAAG,EAAE;IAFI;EALS;AAtRpB,CAHJ;AAsSAJ,aAAa,CAAC0B,SAAd,CAAwBxB,MAAxB,GAAiCF,aAAa,CAAC0B,SAAd,CAAwBpB,SAAzD;;AAEA,MAAM8B,qBAAqB,GAAG,CAAC;EAAEjC,IAAF;EAAQC;AAAR,CAAD,KAC3B,GAAED,IAAK,KAAIC,GAAI,GADlB;;AAYe,SAASiC,4BAAT,CACbC,iBADa,EAEbC,GAFa,EAMbC,SANa,EAOL;EACR,IAAIC,WAAW,GACZ,wCAAuCH,iBAAkB,4BAA1D,GACC,IAAGC,GAAG,CAACG,IAAK,IAAGH,GAAG,CAACI,MAAJ,GAAa,CAAE,QAD/B,GAEAH,SAHF;EAIA,MAAMI,UAAU,GAAG5C,aAAa,CAACsC,iBAAD,CAAhC;;EACA,IAAIM,UAAJ,EAAgB;IACd,MAAM;MAAE1C,MAAM,EAAE2C,YAAV;MAAwBvC,SAAS,EAAEwC;IAAnC,IAAuDF,UAA7D;;IACA,IAAIC,YAAJ,EAAkB;MAChB,MAAME,gBAAgB,GAAGX,qBAAqB,CAACS,YAAD,CAA9C;;MACA,IAAIC,eAAJ,EAAqB;QACnB,MAAME,mBAAmB,GAAGZ,qBAAqB,CAACU,eAAD,CAAjD;QACA,MAAMG,WAAW,GAAGH,eAAe,CAAC3C,IAAhB,CAAqB+C,UAArB,CAAgC,eAAhC,IAChB,SADgB,GAEhB,SAFJ;QAGAT,WAAW,IAAK,WAAUO,mBAAoB,YAAWC,WAAY;AAC7E,qCAAqCF,gBAAiB,8CAD9C;MAED,CAPD,MAOO;QACLN,WAAW,IACR,WAAUM,gBAAiB,iDAA5B,GACC,oBAFH;MAGD;IACF;EACF;;EACD,OAAON,WAAP;AACD"}