{"version": 3, "sources": ["../../../src/optimizer/transforms/unnest-useless-classes.ts"], "sourcesContent": ["import type {CharacterClassNode} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\nimport {hasOnlyChild} from '../../parser/node-utils.js';\n\n/**\nUnnest character classes when possible.\nSee also `unwrapNegationWrappers`.\n*/\nconst unnestUselessClasses: Visitor = {\n  CharacterClass({node, parent, replaceWith, replaceWithMultiple}) {\n    const {body, kind, negate} = node;\n    if (\n      // Don't use this to unwrap outermost classes; see `unwrapUselessClasses` for that\n      parent.type !== 'CharacterClass' ||\n      kind !== 'union' ||\n      !body.length\n    ) {\n      return;\n    }\n    const firstEl = body[0];\n    // Special case to unnest classes that are an only-kid of their parent, since it might flip\n    // `negate` on the parent; ex:\n    // `[[a]]` -> `[a]`; `[[^a]]` -> `[^a]`; `[^[a]]` -> `[^a]`; `[^[^a]]` -> `[a]`\n    if (hasOnlyChild(parent, {\n      type: 'CharacterClass',\n      kind: 'union',\n    })) {\n      parent.negate = parent.negate !== negate;\n      replaceWithMultiple(body, {traverse: true});\n      return;\n    }\n    // Remainder of options apply only if the class is non-negated\n    if (negate) {\n      return;\n    }\n    // Unnest all kids into a union class\n    if (parent.kind === 'union') {\n      replaceWithMultiple(body, {traverse: true});\n    // Can unnest any one kid into an intersection class\n    // TODO: After supporting `format` for classes (see <github.com/slevithan/oniguruma-parser/issues/1>),\n    // can visually unnest any number of kids into intersection by flipping this class's `format`\n    // from `'explicit'` to `'implicit'`, rather than replacing it\n    } else if (hasOnlyChild(node)) {\n      replaceWith(firstEl, {traverse: true});\n    }\n  },\n};\n\nexport {\n  unnestUselessClasses,\n};\n"], "mappings": "aAEA,OAAQ,gBAAAA,MAAmB,6BAM3B,MAAMC,EAAgC,CACpC,eAAe,CAAC,KAAAC,EAAM,OAAAC,EAAQ,YAAAC,EAAa,oBAAAC,CAAmB,EAAG,CAC/D,KAAM,CAAC,KAAAC,EAAM,KAAAC,EAAM,OAAAC,CAAM,EAAIN,EAC7B,GAEEC,EAAO,OAAS,kBAChBI,IAAS,SACT,CAACD,EAAK,OAEN,OAEF,MAAMG,EAAUH,EAAK,CAAC,EAItB,GAAIN,EAAaG,EAAQ,CACvB,KAAM,iBACN,KAAM,OACR,CAAC,EAAG,CACFA,EAAO,OAASA,EAAO,SAAWK,EAClCH,EAAoBC,EAAM,CAAC,SAAU,EAAI,CAAC,EAC1C,MACF,CAEIE,IAIAL,EAAO,OAAS,QAClBE,EAAoBC,EAAM,CAAC,SAAU,EAAI,CAAC,EAKjCN,EAAaE,CAAI,GAC1BE,EAAYK,EAAS,CAAC,SAAU,EAAI,CAAC,EAEzC,CACF,EAEA,OACER,KAAA", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unnestUselessClasses", "node", "parent", "replaceWith", "replaceWithMultiple", "body", "kind", "negate", "firstEl"]}