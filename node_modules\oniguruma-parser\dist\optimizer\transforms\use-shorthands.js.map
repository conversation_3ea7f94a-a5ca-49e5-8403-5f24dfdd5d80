{"version": 3, "sources": ["../../../src/optimizer/transforms/use-shorthands.ts"], "sourcesContent": ["import type {CharacterSetNode, Node} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\nimport {createCharacterSet} from '../../parser/parse.js';\nimport {cpOf} from '../../utils.js';\n\n/**\nUse shorthands (`\\d`, `\\h`, `\\s`, etc.) when possible.\n- `\\d` from `\\p{Decimal_Number}`, `\\p{Nd}`, `\\p{digit}`, `[[:digit:]]`\n- `\\h` from `\\p{ASCII_Hex_Digit}`, `\\p{AHex}`, `\\p{xdigit}`, `[[:xdigit:]]`, `[0-9A-Fa-f]`\n- `\\s` from `\\p{White_Space}`, `\\p{WSpace}`, `\\p{space}`, `[[:space:]]`\n- `\\w` from `[\\p{L}\\p{M}\\p{N}\\p{Pc}]` - Not the same as POSIX `\\p{word}`, `[[:word:]]`!\n- `\\O` from `\\p{Any}` if not in class\nSee also `useUnicodeProps`.\n*/\nconst useShorthands: Visitor = {\n  CharacterSet({node, parent, root, replaceWith}) {\n    const {kind, negate, value} = node;\n    let newNode: CharacterSetNode | null = null;\n    if (\n      ( kind === 'property' &&\n        (value === 'Decimal_Number' || value === 'Nd') &&\n        // TODO: Also check local context when the parser supports these flags on pattern modifiers\n        !root.flags.digitIsAscii &&\n        !root.flags.posixIsAscii\n      ) ||\n      ( kind === 'posix' &&\n        value === 'digit'\n      )\n    ) {\n      newNode = createCharacterSet('digit', {negate});\n    } else if (\n      ( kind === 'property' &&\n        (value === 'ASCII_Hex_Digit' || value === 'AHex')\n      ) ||\n      ( kind === 'posix' &&\n        value === 'xdigit'\n      )\n    ) {\n      newNode = createCharacterSet('hex', {negate});\n    } else if (\n      ( kind === 'property' &&\n        (value === 'White_Space' || value === 'WSpace') &&\n        // TODO: Also check local context when the parser supports these flags on pattern modifiers\n        !root.flags.spaceIsAscii &&\n        !root.flags.posixIsAscii\n      ) ||\n      ( kind === 'posix' &&\n        value === 'space'\n      )\n    ) {\n      newNode = createCharacterSet('space', {negate});\n    } else if (\n      parent.type !== 'CharacterClass' &&\n      kind === 'property' &&\n      !negate &&\n      value === 'Any'\n    ) {\n      newNode = createCharacterSet('any');\n    }\n    if (newNode) {\n      replaceWith(newNode);\n    }\n  },\n\n  CharacterClass({node, root}) {\n    if (node.kind !== 'union') {\n      return;\n    }\n    const has = {\n      rangeDigit0To9: false,\n      rangeAToFLower: false,\n      rangeAToFUpper: false,\n      unicodeL: false,\n      unicodeM: false,\n      unicodeN: false,\n      unicodePc: false,\n    };\n    for (const kid of node.body) {\n      if (kid.type === 'CharacterClassRange') {\n        has.rangeDigit0To9 ||= isRange(kid, cp.n0, cp.n9);\n        has.rangeAToFLower ||= isRange(kid, cp.a, cp.f);\n        has.rangeAToFUpper ||= isRange(kid, cp.A, cp.F);\n      } else if (kid.type === 'CharacterSet') {\n        has.unicodeL ||= isUnicode(kid, 'L');\n        has.unicodeM ||= isUnicode(kid, 'M');\n        has.unicodeN ||= isUnicode(kid, 'N');\n        has.unicodePc ||= isUnicode(kid, 'Pc', {includeSupercategories: true});\n      }\n    }\n    // TODO: Only need one of A-F or a-f if flag i is active in local context; relies on\n    // <github.com/slevithan/oniguruma-parser/issues/14>\n    if (has.rangeDigit0To9 && has.rangeAToFUpper && has.rangeAToFLower) {\n      node.body = node.body.filter(kid => !(\n        isRange(kid, cp.n0, cp.n9) || isRange(kid, cp.a, cp.f) || isRange(kid, cp.A, cp.F)\n      ));\n      node.body.push(createCharacterSet('hex'));\n    }\n    if (\n      (has.unicodeL && has.unicodeM && has.unicodeN && has.unicodePc) &&\n      // TODO: Also check local context when the parser supports these flags on pattern modifiers\n      !root.flags.wordIsAscii &&\n      !root.flags.posixIsAscii\n    ) {\n      node.body = node.body.filter(kid => !isUnicode(kid, ['L', 'M', 'N', 'Pc'], {\n        includeSubcategories: true,\n      }));\n      node.body.push(createCharacterSet('word'));\n    }\n  },\n};\n\nconst cp = {\n  n0: cpOf('0'),\n  n9: cpOf('9'),\n  A: cpOf('A'),\n  F: cpOf('F'),\n  a: cpOf('a'),\n  f: cpOf('f'),\n};\n\nfunction isRange(node: Node, min: number, max: number): boolean {\n  return (\n    node.type === 'CharacterClassRange' &&\n    node.min.value === min &&\n    node.max.value === max\n  );\n}\n\nfunction isUnicode(\n  node: Node,\n  value: string | Array<string>,\n  options: {includeSupercategories?: boolean; includeSubcategories?: boolean} = {}\n): boolean {\n  if (\n    node.type !== 'CharacterSet' ||\n    node.kind !== 'property' ||\n    node.negate\n  ) {\n    return false;\n  }\n  const names = Array.isArray(value) ? value : [value];\n  const expanded: Array<string> = [];\n  for (const v of names) {\n    expanded.push(v);\n    const supercategoryFullName = categories[v as SupercategoryShortName]?.full;\n    const supercategoryShortName = supercategories[v as SubcategoryShortName];\n    const subcategoryShortNames = categories[v as SupercategoryShortName]?.sub;\n    if (supercategoryFullName) {\n      expanded.push(supercategoryFullName);\n    }\n    if (options.includeSupercategories && supercategoryShortName) {\n      expanded.push(supercategoryShortName);\n      expanded.push(categories[supercategoryShortName].full);\n    }\n    if (options.includeSubcategories && subcategoryShortNames) {\n      expanded.push(...subcategoryShortNames);\n    }\n  }\n  return expanded.includes(node.value);\n}\n\ntype SupercategoryShortName = 'L' | 'M' | 'N' | 'P';\ntype SubcategoryShortName = typeof subL[number] | typeof subM[number] | typeof subN[number] | typeof subP[number];\nconst subL = ['Ll', 'Lm', 'Lo', 'Lt', 'Lu'] as const;\nconst subM = ['Mc', 'Me', 'Mn'] as const;\nconst subN = ['Nd', 'Nl', 'No'] as const;\nconst subP = ['Pc', 'Pd', 'Pe', 'Pf', 'Pi', 'Po', 'Ps'] as const;\nconst categories: {[key in SupercategoryShortName]: {full: string; sub: ReadonlyArray<SubcategoryShortName>}} = {\n  L: {full: 'Letter', sub: subL},\n  M: {full: 'Mark', sub: subM},\n  N: {full: 'Number', sub: subN},\n  P: {full: 'Punctuation', sub: subP},\n};\nconst supercategories = {} as {[key in SubcategoryShortName]: SupercategoryShortName};\nfor (const key of Object.keys(categories) as Array<SupercategoryShortName>) {\n  for (const sub of categories[key].sub) {\n    supercategories[sub] = key;\n  }\n}\n\nexport {\n  isRange,\n  useShorthands,\n};\n"], "mappings": "aAEA,OAAQ,sBAAAA,MAAyB,wBACjC,OAAQ,QAAAC,MAAW,iBAWnB,MAAMC,EAAyB,CAC7B,aAAa,CAAC,KAAAC,EAAM,OAAAC,EAAQ,KAAAC,EAAM,YAAAC,CAAW,EAAG,CAC9C,KAAM,CAAC,KAAAC,EAAM,OAAAC,EAAQ,MAAAC,CAAK,EAAIN,EAC9B,IAAIO,EAAmC,KAEnCH,IAAS,aACRE,IAAU,kBAAoBA,IAAU,OAEzC,CAACJ,EAAK,MAAM,cACZ,CAACA,EAAK,MAAM,cAEZE,IAAS,SACTE,IAAU,QAGZC,EAAUV,EAAmB,QAAS,CAAC,OAAAQ,CAAM,CAAC,EAE5CD,IAAS,aACRE,IAAU,mBAAqBA,IAAU,SAE1CF,IAAS,SACTE,IAAU,SAGZC,EAAUV,EAAmB,MAAO,CAAC,OAAAQ,CAAM,CAAC,EAE1CD,IAAS,aACRE,IAAU,eAAiBA,IAAU,WAEtC,CAACJ,EAAK,MAAM,cACZ,CAACA,EAAK,MAAM,cAEZE,IAAS,SACTE,IAAU,QAGZC,EAAUV,EAAmB,QAAS,CAAC,OAAAQ,CAAM,CAAC,EAE9CJ,EAAO,OAAS,kBAChBG,IAAS,YACT,CAACC,GACDC,IAAU,QAEVC,EAAUV,EAAmB,KAAK,GAEhCU,GACFJ,EAAYI,CAAO,CAEvB,EAEA,eAAe,CAAC,KAAAP,EAAM,KAAAE,CAAI,EAAG,CAC3B,GAAIF,EAAK,OAAS,QAChB,OAEF,MAAMQ,EAAM,CACV,eAAgB,GAChB,eAAgB,GAChB,eAAgB,GAChB,SAAU,GACV,SAAU,GACV,SAAU,GACV,UAAW,EACb,EACA,UAAWC,KAAOT,EAAK,KACjBS,EAAI,OAAS,uBACfD,EAAI,iBAAmBE,EAAQD,EAAKE,EAAG,GAAIA,EAAG,EAAE,EAChDH,EAAI,iBAAmBE,EAAQD,EAAKE,EAAG,EAAGA,EAAG,CAAC,EAC9CH,EAAI,iBAAmBE,EAAQD,EAAKE,EAAG,EAAGA,EAAG,CAAC,GACrCF,EAAI,OAAS,iBACtBD,EAAI,WAAaI,EAAUH,EAAK,GAAG,EACnCD,EAAI,WAAaI,EAAUH,EAAK,GAAG,EACnCD,EAAI,WAAaI,EAAUH,EAAK,GAAG,EACnCD,EAAI,YAAcI,EAAUH,EAAK,KAAM,CAAC,uBAAwB,EAAI,CAAC,GAKrED,EAAI,gBAAkBA,EAAI,gBAAkBA,EAAI,iBAClDR,EAAK,KAAOA,EAAK,KAAK,OAAOS,GAAO,EAClCC,EAAQD,EAAKE,EAAG,GAAIA,EAAG,EAAE,GAAKD,EAAQD,EAAKE,EAAG,EAAGA,EAAG,CAAC,GAAKD,EAAQD,EAAKE,EAAG,EAAGA,EAAG,CAAC,EAClF,EACDX,EAAK,KAAK,KAAKH,EAAmB,KAAK,CAAC,GAGvCW,EAAI,UAAYA,EAAI,UAAYA,EAAI,UAAYA,EAAI,WAErD,CAACN,EAAK,MAAM,aACZ,CAACA,EAAK,MAAM,eAEZF,EAAK,KAAOA,EAAK,KAAK,OAAOS,GAAO,CAACG,EAAUH,EAAK,CAAC,IAAK,IAAK,IAAK,IAAI,EAAG,CACzE,qBAAsB,EACxB,CAAC,CAAC,EACFT,EAAK,KAAK,KAAKH,EAAmB,MAAM,CAAC,EAE7C,CACF,EAEMc,EAAK,CACT,GAAIb,EAAK,GAAG,EACZ,GAAIA,EAAK,GAAG,EACZ,EAAGA,EAAK,GAAG,EACX,EAAGA,EAAK,GAAG,EACX,EAAGA,EAAK,GAAG,EACX,EAAGA,EAAK,GAAG,CACb,EAEA,SAASY,EAAQV,EAAYa,EAAaC,EAAsB,CAC9D,OACEd,EAAK,OAAS,uBACdA,EAAK,IAAI,QAAUa,GACnBb,EAAK,IAAI,QAAUc,CAEvB,CAEA,SAASF,EACPZ,EACAM,EACAS,EAA8E,CAAC,EACtE,CACT,GACEf,EAAK,OAAS,gBACdA,EAAK,OAAS,YACdA,EAAK,OAEL,MAAO,GAET,MAAMgB,EAAQ,MAAM,QAAQV,CAAK,EAAIA,EAAQ,CAACA,CAAK,EAC7CW,EAA0B,CAAC,EACjC,UAAWC,KAAKF,EAAO,CACrBC,EAAS,KAAKC,CAAC,EACf,MAAMC,EAAwBC,EAAWF,CAA2B,GAAG,KACjEG,EAAyBC,EAAgBJ,CAAyB,EAClEK,EAAwBH,EAAWF,CAA2B,GAAG,IACnEC,GACFF,EAAS,KAAKE,CAAqB,EAEjCJ,EAAQ,wBAA0BM,IACpCJ,EAAS,KAAKI,CAAsB,EACpCJ,EAAS,KAAKG,EAAWC,CAAsB,EAAE,IAAI,GAEnDN,EAAQ,sBAAwBQ,GAClCN,EAAS,KAAK,GAAGM,CAAqB,CAE1C,CACA,OAAON,EAAS,SAASjB,EAAK,KAAK,CACrC,CAIA,MAAMwB,EAAO,CAAC,KAAM,KAAM,KAAM,KAAM,IAAI,EACpCC,EAAO,CAAC,KAAM,KAAM,IAAI,EACxBC,EAAO,CAAC,KAAM,KAAM,IAAI,EACxBC,EAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAChDP,EAA0G,CAC9G,EAAG,CAAC,KAAM,SAAU,IAAKI,CAAI,EAC7B,EAAG,CAAC,KAAM,OAAQ,IAAKC,CAAI,EAC3B,EAAG,CAAC,KAAM,SAAU,IAAKC,CAAI,EAC7B,EAAG,CAAC,KAAM,cAAe,IAAKC,CAAI,CACpC,EACML,EAAkB,CAAC,EACzB,UAAWM,KAAO,OAAO,KAAKR,CAAU,EACtC,UAAWS,KAAOT,EAAWQ,CAAG,EAAE,IAChCN,EAAgBO,CAAG,EAAID,EAI3B,OACElB,KAAA,QACAX,KAAA", "names": ["createCharacterSet", "cpOf", "useShorthands", "node", "parent", "root", "replaceWith", "kind", "negate", "value", "newNode", "has", "kid", "isRange", "cp", "isUnicode", "min", "max", "options", "names", "expanded", "v", "supercategoryFullName", "categories", "supercategoryShortName", "supercategories", "subcategoryShortNames", "subL", "subM", "subN", "subP", "key", "sub"]}