{"version": 3, "names": ["createItemFromDescriptor", "desc", "ConfigItem", "createConfigItem", "value", "dirname", "type", "descriptor", "createDescriptor", "path", "resolve", "alias", "getItemDescriptor", "item", "CONFIG_ITEM_BRAND", "_descriptor", "undefined", "Symbol", "for", "constructor", "options", "name", "file", "Object", "defineProperty", "enumerable", "request", "resolved", "freeze", "prototype"], "sources": ["../../src/config/item.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\nimport type { PluginTarget, PluginOptions } from \"./validation/options\";\n\nimport path from \"path\";\nimport { createDescriptor } from \"./config-descriptors\";\n\nimport type { UnloadedDescriptor } from \"./config-descriptors\";\n\nexport function createItemFromDescriptor(desc: UnloadedDescriptor): ConfigItem {\n  return new ConfigItem(desc);\n}\n\n/**\n * Create a config item using the same value format used in Babel's config\n * files. Items returned from this function should be cached by the caller\n * ideally, as recreating the config item will mean re-resolving the item\n * and re-evaluating the plugin/preset function.\n */\nexport function* createConfigItem(\n  value:\n    | PluginTarget\n    | [PluginTarget, PluginOptions]\n    | [PluginTarget, PluginOptions, string | void],\n  {\n    dirname = \".\",\n    type,\n  }: {\n    dirname?: string;\n    type?: \"preset\" | \"plugin\";\n  } = {},\n): Handler<ConfigItem> {\n  const descriptor = yield* createDescriptor(value, path.resolve(dirname), {\n    type,\n    alias: \"programmatic item\",\n  });\n\n  return createItemFromDescriptor(descriptor);\n}\n\nexport function getItemDescriptor(item: unknown): UnloadedDescriptor | void {\n  if ((item as any)?.[CONFIG_ITEM_BRAND]) {\n    return (item as ConfigItem)._descriptor;\n  }\n\n  return undefined;\n}\n\nexport type { ConfigItem };\n\nconst CONFIG_ITEM_BRAND = Symbol.for(\"@babel/core@7 - ConfigItem\");\n\n/**\n * A public representation of a plugin/preset that will _eventually_ be load.\n * Users can use this to interact with the results of a loaded Babel\n * configuration.\n *\n * Any changes to public properties of this class should be considered a\n * breaking change to Babel's API.\n */\nclass ConfigItem {\n  /**\n   * The private underlying descriptor that Babel actually cares about.\n   * If you access this, you are a bad person.\n   */\n  _descriptor: UnloadedDescriptor;\n\n  // TODO(Babel 8): Check if this symbol needs to be updated\n  /**\n   * Used to detect ConfigItem instances from other Babel instances.\n   */\n  [CONFIG_ITEM_BRAND] = true;\n\n  /**\n   * The resolved value of the item itself.\n   */\n  value: {} | Function;\n\n  /**\n   * The options, if any, that were passed to the item.\n   * Mutating this will lead to undefined behavior.\n   *\n   * \"false\" means that this item has been disabled.\n   */\n  options: {} | void | false;\n\n  /**\n   * The directory that the options for this item are relative to.\n   */\n  dirname: string;\n\n  /**\n   * Get the name of the plugin, if the user gave it one.\n   */\n  name: string | void;\n\n  /**\n   * Data about the file that the item was loaded from, if Babel knows it.\n   */\n  file: {\n    // The requested path, e.g. \"@babel/env\".\n    request: string;\n    // The resolved absolute path of the file.\n    resolved: string;\n  } | void;\n\n  constructor(descriptor: UnloadedDescriptor) {\n    // Make people less likely to stumble onto this if they are exploring\n    // programmatically, and also make sure that if people happen to\n    // pass the item through JSON.stringify, it doesn't show up.\n    this._descriptor = descriptor;\n    Object.defineProperty(this, \"_descriptor\", { enumerable: false });\n\n    Object.defineProperty(this, CONFIG_ITEM_BRAND, { enumerable: false });\n\n    this.value = this._descriptor.value;\n    this.options = this._descriptor.options;\n    this.dirname = this._descriptor.dirname;\n    this.name = this._descriptor.name;\n    this.file = this._descriptor.file\n      ? {\n          request: this._descriptor.file.request,\n          resolved: this._descriptor.file.resolved,\n        }\n      : undefined;\n\n    // Freeze the object to make it clear that people shouldn't expect mutating\n    // this object to do anything. A new item should be created if they want\n    // to change something.\n    Object.freeze(this);\n  }\n}\n\nObject.freeze(ConfigItem.prototype);\n"], "mappings": ";;;;;;;;;AAGA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAIO,SAASA,wBAAT,CAAkCC,IAAlC,EAAwE;EAC7E,OAAO,IAAIC,UAAJ,CAAeD,IAAf,CAAP;AACD;;AAQM,UAAUE,gBAAV,CACLC,KADK,EAKL;EACEC,OAAO,GAAG,GADZ;EAEEC;AAFF,IAMI,EAXC,EAYgB;EACrB,MAAMC,UAAU,GAAG,OAAO,IAAAC,mCAAA,EAAiBJ,KAAjB,EAAwBK,OAAA,CAAKC,OAAL,CAAaL,OAAb,CAAxB,EAA+C;IACvEC,IADuE;IAEvEK,KAAK,EAAE;EAFgE,CAA/C,CAA1B;EAKA,OAAOX,wBAAwB,CAACO,UAAD,CAA/B;AACD;;AAEM,SAASK,iBAAT,CAA2BC,IAA3B,EAAqE;EAC1E,IAAKA,IAAL,YAAKA,IAAD,CAAgBC,iBAAhB,CAAJ,EAAwC;IACtC,OAAQD,IAAD,CAAqBE,WAA5B;EACD;;EAED,OAAOC,SAAP;AACD;;AAID,MAAMF,iBAAiB,GAAGG,MAAM,CAACC,GAAP,CAAW,4BAAX,CAA1B;;AAUA,MAAMhB,UAAN,CAAiB;EA8CfiB,WAAW,CAACZ,UAAD,EAAiC;IAAA,KAzC5CQ,WAyC4C;IAAA,KAnC3CD,iBAmC2C,IAnCtB,IAmCsB;IAAA,KA9B5CV,KA8B4C;IAAA,KAtB5CgB,OAsB4C;IAAA,KAjB5Cf,OAiB4C;IAAA,KAZ5CgB,IAY4C;IAAA,KAP5CC,IAO4C;IAI1C,KAAKP,WAAL,GAAmBR,UAAnB;IACAgB,MAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4B,aAA5B,EAA2C;MAAEC,UAAU,EAAE;IAAd,CAA3C;IAEAF,MAAM,CAACC,cAAP,CAAsB,IAAtB,EAA4BV,iBAA5B,EAA+C;MAAEW,UAAU,EAAE;IAAd,CAA/C;IAEA,KAAKrB,KAAL,GAAa,KAAKW,WAAL,CAAiBX,KAA9B;IACA,KAAKgB,OAAL,GAAe,KAAKL,WAAL,CAAiBK,OAAhC;IACA,KAAKf,OAAL,GAAe,KAAKU,WAAL,CAAiBV,OAAhC;IACA,KAAKgB,IAAL,GAAY,KAAKN,WAAL,CAAiBM,IAA7B;IACA,KAAKC,IAAL,GAAY,KAAKP,WAAL,CAAiBO,IAAjB,GACR;MACEI,OAAO,EAAE,KAAKX,WAAL,CAAiBO,IAAjB,CAAsBI,OADjC;MAEEC,QAAQ,EAAE,KAAKZ,WAAL,CAAiBO,IAAjB,CAAsBK;IAFlC,CADQ,GAKRX,SALJ;IAUAO,MAAM,CAACK,MAAP,CAAc,IAAd;EACD;;AAtEc;;AAyEjBL,MAAM,CAACK,MAAP,CAAc1B,UAAU,CAAC2B,SAAzB"}