{"version": 3, "names": ["import_", "require", "supportsESM", "semver", "satisfies", "process", "versions", "node", "loadCjsOrMjsDefault", "filepath", "asyncError", "fallbackToTranspiledModule", "guessJSModuleType", "loadCjsDefault", "e", "code", "isAsync", "waitFor", "loadMjsDefault", "ConfigError", "filename", "path", "extname", "module", "endHiddenCallStack", "__esModule", "default", "undefined", "pathToFileURL"], "sources": ["../../../src/config/files/module-types.ts"], "sourcesContent": ["import { isAsync, waitFor } from \"../../gensync-utils/async\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport path from \"path\";\nimport { pathToFileURL } from \"url\";\nimport { createRequire } from \"module\";\nimport semver from \"semver\";\n\nimport { endHiddenCallStack } from \"../../errors/rewrite-stack-trace\";\nimport ConfigError from \"../../errors/config-error\";\n\nconst require = createRequire(import.meta.url);\n\nlet import_: ((specifier: string | URL) => any) | undefined;\ntry {\n  // Old Node.js versions don't support import() syntax.\n  import_ = require(\"./import.cjs\");\n} catch {}\n\nexport const supportsESM = semver.satisfies(\n  process.versions.node,\n  // older versions, starting from 10, support the dynamic\n  // import syntax but always return a rejected promise.\n  \"^12.17 || >=13.2\",\n);\n\nexport default function* loadCjsOrMjsDefault(\n  filepath: string,\n  asyncError: string,\n  // TODO(Babel 8): Remove this\n  fallbackToTranspiledModule: boolean = false,\n): Handler<unknown> {\n  switch (guessJSModuleType(filepath)) {\n    case \"cjs\":\n      return loadCjsDefault(filepath, fallbackToTranspiledModule);\n    case \"unknown\":\n      try {\n        return loadCjsDefault(filepath, fallbackToTranspiledModule);\n      } catch (e) {\n        if (e.code !== \"ERR_REQUIRE_ESM\") throw e;\n      }\n    // fall through\n    case \"mjs\":\n      if (yield* isAsync()) {\n        return yield* waitFor(loadMjsDefault(filepath));\n      }\n      throw new ConfigError(asyncError, filepath);\n  }\n}\n\nfunction guessJSModuleType(filename: string): \"cjs\" | \"mjs\" | \"unknown\" {\n  switch (path.extname(filename)) {\n    case \".cjs\":\n      return \"cjs\";\n    case \".mjs\":\n      return \"mjs\";\n    default:\n      return \"unknown\";\n  }\n}\n\nfunction loadCjsDefault(filepath: string, fallbackToTranspiledModule: boolean) {\n  const module = endHiddenCallStack(require)(filepath) as any;\n  return module?.__esModule\n    ? // TODO (Babel 8): Remove \"module\" and \"undefined\" fallback\n      module.default || (fallbackToTranspiledModule ? module : undefined)\n    : module;\n}\n\nasync function loadMjsDefault(filepath: string) {\n  if (!import_) {\n    throw new ConfigError(\n      \"Internal error: Native ECMAScript modules aren't supported\" +\n        \" by this platform.\\n\",\n      filepath,\n    );\n  }\n\n  // import() expects URLs, not file paths.\n  // https://github.com/nodejs/node/issues/31710\n  const module = await endHiddenCallStack(import_)(pathToFileURL(filepath));\n  return module.default;\n}\n"], "mappings": ";;;;;;;;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AACA;;;;;;AAIA,IAAIA,OAAJ;;AACA,IAAI;EAEFA,OAAO,GAAGC,OAAO,CAAC,cAAD,CAAjB;AACD,CAHD,CAGE,gBAAM,CAAE;;AAEH,MAAMC,WAAW,GAAGC,SAAA,CAAOC,SAAP,CACzBC,OAAO,CAACC,QAAR,CAAiBC,IADQ,EAIzB,kBAJyB,CAApB;;;;AAOQ,UAAUC,mBAAV,CACbC,QADa,EAEbC,UAFa,EAIbC,0BAAmC,GAAG,KAJzB,EAKK;EAClB,QAAQC,iBAAiB,CAACH,QAAD,CAAzB;IACE,KAAK,KAAL;MACE,OAAOI,cAAc,CAACJ,QAAD,EAAWE,0BAAX,CAArB;;IACF,KAAK,SAAL;MACE,IAAI;QACF,OAAOE,cAAc,CAACJ,QAAD,EAAWE,0BAAX,CAArB;MACD,CAFD,CAEE,OAAOG,CAAP,EAAU;QACV,IAAIA,CAAC,CAACC,IAAF,KAAW,iBAAf,EAAkC,MAAMD,CAAN;MACnC;;IAEH,KAAK,KAAL;MACE,IAAI,OAAO,IAAAE,cAAA,GAAX,EAAsB;QACpB,OAAO,OAAO,IAAAC,cAAA,EAAQC,cAAc,CAACT,QAAD,CAAtB,CAAd;MACD;;MACD,MAAM,IAAIU,oBAAJ,CAAgBT,UAAhB,EAA4BD,QAA5B,CAAN;EAdJ;AAgBD;;AAED,SAASG,iBAAT,CAA2BQ,QAA3B,EAAwE;EACtE,QAAQC,OAAA,CAAKC,OAAL,CAAaF,QAAb,CAAR;IACE,KAAK,MAAL;MACE,OAAO,KAAP;;IACF,KAAK,MAAL;MACE,OAAO,KAAP;;IACF;MACE,OAAO,SAAP;EANJ;AAQD;;AAED,SAASP,cAAT,CAAwBJ,QAAxB,EAA0CE,0BAA1C,EAA+E;EAC7E,MAAMY,MAAM,GAAG,IAAAC,qCAAA,EAAmBvB,OAAnB,EAA4BQ,QAA5B,CAAf;EACA,OAAOc,MAAM,QAAN,IAAAA,MAAM,CAAEE,UAAR,GAEHF,MAAM,CAACG,OAAP,KAAmBf,0BAA0B,GAAGY,MAAH,GAAYI,SAAzD,CAFG,GAGHJ,MAHJ;AAID;;SAEcL,c;;;;;sCAAf,WAA8BT,QAA9B,EAAgD;IAC9C,IAAI,CAACT,OAAL,EAAc;MACZ,MAAM,IAAImB,oBAAJ,CACJ,+DACE,sBAFE,EAGJV,QAHI,CAAN;IAKD;;IAID,MAAMc,MAAM,SAAS,IAAAC,qCAAA,EAAmBxB,OAAnB,EAA4B,IAAA4B,oBAAA,EAAcnB,QAAd,CAA5B,CAArB;IACA,OAAOc,MAAM,CAACG,OAAd;EACD,C"}