{"version": 3, "sources": ["../../../src/optimizer/transforms/remove-empty-groups.ts"], "sourcesContent": ["import type {AlternativeContainerNode, Node} from '../../parser/parse.js';\nimport type {Visitor} from '../../traverser/traverse.js';\n\n/**\nRemove empty noncapturing, atomic, and flag groups, even if quantified.\n*/\nconst removeEmptyGroups: Visitor = {\n  AbsenceFunction({node, remove}) {\n    if (isQualifiedAndEmpty(node)) {\n      remove();\n    }\n  },\n\n  Group({node, remove}) {\n    if (isQualifiedAndEmpty(node)) {\n      remove();\n    }\n  },\n\n  LookaroundAssertion({node, remove}) {\n    if (isQualifiedAndEmpty(node)) {\n      remove();\n    }\n  },\n\n  Quantifier({node, remove}) {\n    let kid = node.body;\n    while (kid.type === 'Quantifier') {\n      kid = kid.body;\n    }\n    if (isQualifiedAndEmpty(kid)) {\n      remove();\n    }\n  },\n};\n\nfunction hasOnlyEmptyAlts(node: AlternativeContainerNode): boolean {\n  return node.body.every(alt => !alt.body.length);\n}\n\nfunction isQualifiedAndEmpty(node: Node): boolean {\n  switch (node.type) {\n    case 'AbsenceFunction':\n      return node.kind === 'repeater' && hasOnlyEmptyAlts(node);\n    case 'Group':\n      return hasOnlyEmptyAlts(node);\n    case 'LookaroundAssertion':\n      return !node.negate && hasOnlyEmptyAlts(node);\n    default:\n      return false;\n  }\n}\n\nexport {\n  removeEmptyGroups,\n};\n"], "mappings": "aAMA,MAAMA,EAA6B,CACjC,gBAAgB,CAAC,KAAAC,EAAM,OAAAC,CAAM,EAAG,CAC1BC,EAAoBF,CAAI,GAC1BC,EAAO,CAEX,EAEA,MAAM,CAAC,KAAAD,EAAM,OAAAC,CAAM,EAAG,CAChBC,EAAoBF,CAAI,GAC1BC,EAAO,CAEX,EAEA,oBAAoB,CAAC,KAAAD,EAAM,OAAAC,CAAM,EAAG,CAC9BC,EAAoBF,CAAI,GAC1BC,EAAO,CAEX,EAEA,WAAW,CAAC,KAAAD,EAAM,OAAAC,CAAM,EAAG,CACzB,IAAIE,EAAMH,EAAK,KACf,KAAOG,EAAI,OAAS,cAClBA,EAAMA,EAAI,KAERD,EAAoBC,CAAG,GACzBF,EAAO,CAEX,CACF,EAEA,SAASG,EAAiBJ,EAAyC,CACjE,OAAOA,EAAK,KAAK,MAAMK,GAAO,CAACA,EAAI,KAAK,MAAM,CAChD,CAEA,SAASH,EAAoBF,EAAqB,CAChD,OAAQA,EAAK,KAAM,CACjB,IAAK,kBACH,OAAOA,EAAK,OAAS,YAAcI,EAAiBJ,CAAI,EAC1D,IAAK,QACH,OAAOI,EAAiBJ,CAAI,EAC9B,IAAK,sBACH,MAAO,CAACA,EAAK,QAAUI,EAAiBJ,CAAI,EAC9C,QACE,MAAO,EACX,CACF,CAEA,OACED,KAAA", "names": ["removeEmptyGroups", "node", "remove", "isQualifiedAndEmpty", "kid", "hasOnlyEmptyAlts", "alt"]}