module.exports={A:{A:{"2":"J D E F A B 4B"},B:{"1":"K L G M N O P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H","132":"C"},C:{"2":"0 1 2 3 4 5 6 7 8 9 5B pB I q J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB 6B 7B","1090":"PB QB RB SB","2052":"TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d","4100":"e f g h i j k l m n o p b H tB"},D:{"1":"0 1 2 3 4 5 6 7 8 9 x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B","2":"I q J D E F A B C K L G M N O r","2052":"s t u v w"},E:{"2":"I q J D E F A B C K L AC uB BC CC DC EC vB mB nB wB","4100":"G FC GC xB yB zB 0B oB 1B HC"},F:{"1":"0 1 2 3 4 5 6 7 8 9 F B C G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a IC JC KC LC mB 2B MC nB"},G:{"2":"uB NC 3B","260":"E OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B"},H:{"2":"hC"},I:{"1":"H mC nC","2":"pB iC jC kC","514":"I lC 3B"},J:{"1":"A","2":"D"},K:{"1":"A B C c mB 2B nB"},L:{"1":"H"},M:{"4100":"b"},N:{"2":"A B"},O:{"1":"oC"},P:{"1":"I pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"1":"wB"},R:{"1":"1C"},S:{"2052":"2C"}},B:1,C:"Date and time input types"};
