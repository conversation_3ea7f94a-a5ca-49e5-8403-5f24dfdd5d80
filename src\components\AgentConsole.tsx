import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Bot,
  User,
  Code,
  Send,
  Sparkles,
  Trash2,
  Lightbulb
} from 'lucide-react';

type Message = {
  id: string;
  text: string;
  sender: 'user' | 'ai' | 'system';
  timestamp: Date;
  code?: string;
  language?: string;
  suggestions?: string[];
};

function AgentConsole() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'AI Assistant is ready to help you with coding, debugging, and explanations.',
      sender: 'system',
      timestamp: new Date(),
    }
  ]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: input,
      sender: 'user',
      timestamp: new Date()
    };
    setMessages([...messages, userMessage]);
    setInput('');
    setIsLoading(true);

    // Mock AI response with enhanced features
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: `I understand you're asking about: "${input}". Here's my analysis and suggestions.`,
        sender: 'ai',
        timestamp: new Date(),
        suggestions: [
          'Explain this code',
          'Find potential bugs',
          'Optimize performance',
          'Add documentation'
        ],
        code: input.toLowerCase().includes('code') ? `// Example code based on your request\nfunction example() {\n  console.log("${input}");\n}` : undefined,
        language: 'typescript'
      };
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const clearMessages = () => {
    setMessages([{
      id: '1',
      text: 'AI Assistant is ready to help you with coding, debugging, and explanations.',
      sender: 'system',
      timestamp: new Date(),
    }]);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
  };

  return (
    <div className="h-full flex flex-col bg-surface-primary">
      <div className="ai-header p-4 border-b border-surface-tertiary">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bot className="text-neon-purple" size={20} />
            <h2 className="text-lg font-semibold text-neon-purple">AI Assistant</h2>
            <Badge variant="outline" className="text-xs border-neon-purple/50 text-neon-purple">
              <Sparkles size={12} className="mr-1" />
              Active
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearMessages}
            className="h-8 w-8 p-0 hover:bg-red-500/20"
          >
            <Trash2 size={14} className="text-gray-400 hover:text-red-400" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4" ref={scrollRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="mb-4">
              <div className="flex items-start space-x-2">
                {message.sender === 'user' ? (
                  <User size={16} className="text-neon-blue mt-1" />
                ) : message.sender === 'ai' ? (
                  <Bot size={16} className="text-neon-purple mt-1" />
                ) : (
                  <Sparkles size={16} className="text-gray-400 mt-1" />
                )}
                <div className="flex-1">
                  <div className={`p-3 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-neon-blue/20 text-neon-blue'
                      : message.sender === 'ai'
                      ? 'bg-neon-purple/20 text-gray-200'
                      : 'bg-surface-secondary text-gray-400'
                  }`}>
                    {message.text}
                  </div>

                  {message.code && (
                    <div className="mt-2 bg-surface-secondary rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Code size={16} className="text-neon-purple" />
                        <span className="text-sm font-medium">Code Suggestion</span>
                      </div>
                      <pre className="bg-surface-tertiary p-3 rounded-md text-sm code-font overflow-x-auto">
                        <code>{message.code}</code>
                      </pre>
                      <div className="flex space-x-2 mt-2">
                        <Button size="sm" variant="outline">
                          Insert Code
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => navigator.clipboard.writeText(message.code!)}
                        >
                          Copy
                        </Button>
                      </div>
                    </div>
                  )}

                  {message.suggestions && (
                    <div className="mt-2 space-y-1">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          className="justify-start h-auto p-2 text-left hover:bg-neon-purple/10 w-full"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          <Lightbulb size={12} className="mr-2 text-neon-purple" />
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="mb-4">
              <div className="flex items-center space-x-2">
                <Bot size={16} className="text-neon-purple animate-pulse" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-neon-purple rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="ai-input-area p-4 border-t border-surface-tertiary">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask AI about your code, request explanations, or get suggestions..."
            disabled={isLoading}
            className="flex-1 bg-surface-secondary border-surface-tertiary text-gray-200 placeholder:text-gray-500 focus:border-neon-purple focus:ring-neon-purple/20"
          />
          <Button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="bg-neon-purple/20 hover:bg-neon-purple/30 border border-neon-purple/50 text-neon-purple"
            variant="outline"
          >
            <Send size={16} />
          </Button>
        </form>
      </div>
    </div>
  );
}

export default AgentConsole;