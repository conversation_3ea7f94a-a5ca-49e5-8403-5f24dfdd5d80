/* Import Fonts - must come before Tailwind directives */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500&family=Inter:wght@300;400;500&display=swap');

/* Tailwind CSS v4 import */
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

html {
  @apply bg-[rgba(15,17,23,0.85)] w-screen h-screen;
}

.bg-darken {
  background-color: #222426;
}

.soure-item .source-codes {
  @apply pl-4 relative;
}

.source-item-active {
  @apply bg-blue-800 text-gray-300;
}

.soure-item .source-codes:before {
  content: "";
  @apply absolute top-0 bottom-0 border-l border-dotted border-stone-500;
}

.inp {
  @apply block w-full rounded-md outline-none shadow-sm sm:text-sm bg-[rgba(15,17,23,0.85)];
  @apply text-gray-200;
  @apply px-2 py-0.5;
}

#titlebar {
  @apply flex items-center justify-between text-gray-300 pl-2;
  background: #1c1c1c;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.titlebar-actions {
  @apply flex items-center;
}

.titlebar-icon {
  @apply py-0.5 cursor-pointer;
  width: 30px;
  text-align: center;
  cursor: pointer;
}

.titlebar-icon:hover {
  background-color: #383838;
}

#ttb-close:hover {
  @apply bg-red-500 text-gray-100;
}

#editor {
  padding-top: 27px;
}

.project-explorer {
  @apply w-full text-left uppercase text-gray-400 text-xs;
}

.code-structure {
  @apply px-2 overflow-y-auto;
  height: calc(100vh - 70px);
}

/* Styles for scrollbar */

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: #222426;
}

::-webkit-scrollbar-thumb {
  background: #465056;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

:root {
  font-family: 'Inter', sans-serif;
  --background: oklch(1 0 0);
  --surface-primary: rgba(15, 17, 23, 0.85);
  --surface-secondary: rgba(20, 22, 28, 0.75);
  --surface-tertiary: rgba(25, 27, 33, 0.65);
  --neon-purple: #b026ff;
  --neon-blue: #0099ff;
  --neon-pink: #ff2d95;
  --neon-glow: rgba(176, 38, 255, 0.15);
  --neon-hover: rgba(176, 38, 255, 0.25);
  --neon-active: rgba(176, 38, 255, 0.35);
  --radius: 0.625rem;
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

body {
  @apply bg-[var(--background)] text-gray-200;
  font-family: 'Inter', sans-serif;
}

.code-font {
  font-family: 'JetBrains Mono', monospace;
}

.glass-panel {
  @apply bg-[var(--surface-primary)] backdrop-blur-md border border-[rgba(255,255,255,0.1)] rounded-xl shadow-[0_4px_32px_rgba(0,0,0,0.15)];
}

.neon-button {
  @apply bg-[rgba(176,38,255,0.15)] border border-[rgba(176,38,255,0.3)] rounded-2xl px-4 py-2 transition-all duration-300;
}

.neon-button:hover {
  @apply bg-[var(--neon-hover)] scale-105 shadow-[0_0_15px_rgba(176,38,255,0.3)];
}

.neon-input {
  @apply bg-[var(--surface-secondary)] border border-[rgba(255,255,255,0.1)] rounded-lg px-3 py-2 focus:outline-none focus:border-[var(--neon-purple)] focus:shadow-[0_0_8px_rgba(176,38,255,0.3)] transition-all duration-200;
}

@keyframes neon-glow {
  0% { box-shadow: 0 0 5px rgba(176,38,255,0.3); }
  50% { box-shadow: 0 0 20px rgba(176,38,255,0.5); }
  100% { box-shadow: 0 0 5px rgba(176,38,255,0.3); }
}

.neon-glow {
  animation: neon-glow 2s infinite alternate;
}

/* VS Code Layout Styles */
.vscode-layout {
  font-family: 'Inter', sans-serif;
}

.activity-bar {
  background: var(--surface-primary);
  border-right: 1px solid var(--surface-tertiary);
}

.enhanced-sidebar {
  background: var(--surface-primary);
  border-right: 1px solid var(--surface-tertiary);
}

.enhanced-panel {
  background: var(--surface-primary);
  border-top: 1px solid var(--surface-tertiary);
}

.status-bar {
  background: var(--surface-primary);
  border-top: 1px solid var(--surface-tertiary);
}

/* Panel resize handles */
.panel-resize-handle {
  background: var(--surface-tertiary);
  transition: background-color 0.2s ease;
}

.panel-resize-handle:hover {
  background: rgba(176, 38, 255, 0.5);
}

/* AI Agent specific styles */
.ai-agent {
  background: var(--surface-primary);
}

.ai-header {
  border-bottom: 1px solid var(--surface-tertiary);
}

.ai-input-area {
  border-top: 1px solid var(--surface-tertiary);
}

/* Enhanced animations for AI components */
@keyframes ai-thinking {
  0%, 20% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

.ai-thinking {
  animation: ai-thinking 1.5s ease-in-out infinite;
}

/* Code insertion highlight */
@keyframes code-insert-highlight {
  0% { background-color: rgba(176, 38, 255, 0.3); }
  100% { background-color: transparent; }
}

.code-insert-highlight {
  animation: code-insert-highlight 1s ease-out;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
