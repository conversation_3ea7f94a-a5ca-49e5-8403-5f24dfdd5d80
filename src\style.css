/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  @apply bg-primary w-screen h-screen;
}

.bg-darken {
  background-color: #222426;
}

.soure-item .source-codes {
  @apply pl-4 relative;
}

.source-item-active {
  @apply bg-blue-800 text-gray-300;
}

.soure-item .source-codes:before {
  content: "";
  @apply absolute top-0 bottom-0 border-l border-dotted border-stone-500;
}

.inp {
  @apply block w-full rounded-md outline-none shadow-sm sm:text-sm bg-primary;
  @apply text-gray-200;
  @apply px-2 py-0.5;
}

#titlebar {
  @apply flex items-center justify-between text-gray-300 pl-2;
  background: #1c1c1c;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

.titlebar-actions {
  @apply flex items-center;
}

.titlebar-icon {
  @apply py-0.5 cursor-pointer;
  width: 30px;
  text-align: center;
  cursor: pointer;
}

.titlebar-icon:hover {
  background-color: #383838;
}

#ttb-close:hover {
  @apply bg-red-500 text-gray-100;
}

#editor {
  padding-top: 27px;
}

.project-explorer {
  @apply w-full text-left uppercase text-gray-400 text-xs;
}

.code-structure {
  @apply px-2 overflow-y-auto;
  height: calc(100vh - 70px);
}

/* Styles for scrollbar */

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: #222426;
}

::-webkit-scrollbar-thumb {
  background: #465056;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Import Fonts */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500&family=Inter:wght@300;400;500&display=swap');

:root {
  font-family: 'Inter', sans-serif;
  --background: #0f1117;
  --surface-primary: rgba(15, 17, 23, 0.85);
  --surface-secondary: rgba(20, 22, 28, 0.75);
  --surface-tertiary: rgba(25, 27, 33, 0.65);
  --neon-purple: #b026ff;
  --neon-blue: #0099ff;
  --neon-pink: #ff2d95;
  --neon-glow: rgba(176, 38, 255, 0.15);
  --neon-hover: rgba(176, 38, 255, 0.25);
  --neon-active: rgba(176, 38, 255, 0.35);
}

body {
  @apply bg-[var(--background)] text-gray-200;
  font-family: 'Inter', sans-serif;
}

.code-font {
  font-family: 'JetBrains Mono', monospace;
}

.glass-panel {
  @apply bg-[var(--surface-primary)] backdrop-blur-md border border-[rgba(255,255,255,0.1)] rounded-xl shadow-[0_4px_32px_rgba(0,0,0,0.15)];
}

.neon-button {
  @apply bg-[rgba(176,38,255,0.15)] border border-[rgba(176,38,255,0.3)] rounded-2xl px-4 py-2 transition-all duration-300;
}

.neon-button:hover {
  @apply bg-[var(--neon-hover)] scale-105 shadow-[0_0_15px_rgba(176,38,255,0.3)];
}

.neon-input {
  @apply bg-[var(--surface-secondary)] border border-[rgba(255,255,255,0.1)] rounded-lg px-3 py-2 focus:outline-none focus:border-[var(--neon-purple)] focus:shadow-[0_0_8px_rgba(176,38,255,0.3)] transition-all duration-200;
}

@keyframes neon-glow {
  0% { box-shadow: 0 0 5px rgba(176,38,255,0.3); }
  50% { box-shadow: 0 0 20px rgba(176,38,255,0.5); }
  100% { box-shadow: 0 0 5px rgba(176,38,255,0.3); }
}

.neon-glow {
  animation: neon-glow 2s infinite alternate;
}
