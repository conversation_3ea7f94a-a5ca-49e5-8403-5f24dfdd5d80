{"version": 3, "names": ["resolveBrowserslistConfigFile", "browserslistConfigFile", "config<PERSON><PERSON><PERSON><PERSON>", "undefined", "resolveTargets", "options", "root", "optTargets", "targets", "Array", "isArray", "browsers", "<PERSON><PERSON><PERSON><PERSON>", "getTargets", "ignoreBrowserslistConfig", "browserslistEnv"], "sources": ["../../src/config/resolve-targets-browser.ts"], "sourcesContent": ["import type { ValidatedOptions } from \"./validation/options\";\nimport getTargets, {\n  type InputTargets,\n} from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nexport function resolveBrowserslistConfigFile(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  browserslistConfigFile: string,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  configFilePath: string,\n): string | void {\n  return undefined;\n}\n\nexport function resolveTargets(\n  options: ValidatedOptions,\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  root: string,\n): Targets {\n  const optTargets = options.targets;\n  let targets: InputTargets;\n\n  if (typeof optTargets === \"string\" || Array.isArray(optTargets)) {\n    targets = { browsers: optTargets };\n  } else if (optTargets) {\n    if (\"esmodules\" in optTargets) {\n      targets = { ...optTargets, esmodules: \"intersect\" };\n    } else {\n      // https://github.com/microsoft/TypeScript/issues/17002\n      targets = optTargets as InputTargets;\n    }\n  }\n\n  return getTargets(targets, {\n    ignoreBrowserslistConfig: true,\n    browserslistEnv: options.browserslistEnv,\n  });\n}\n"], "mappings": ";;;;;;;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAMO,SAASA,6BAAT,CAELC,sBAFK,EAILC,cAJK,EAKU;EACf,OAAOC,SAAP;AACD;;AAEM,SAASC,cAAT,CACLC,OADK,EAGLC,IAHK,EAII;EACT,MAAMC,UAAU,GAAGF,OAAO,CAACG,OAA3B;EACA,IAAIA,OAAJ;;EAEA,IAAI,OAAOD,UAAP,KAAsB,QAAtB,IAAkCE,KAAK,CAACC,OAAN,CAAcH,UAAd,CAAtC,EAAiE;IAC/DC,OAAO,GAAG;MAAEG,QAAQ,EAAEJ;IAAZ,CAAV;EACD,CAFD,MAEO,IAAIA,UAAJ,EAAgB;IACrB,IAAI,eAAeA,UAAnB,EAA+B;MAC7BC,OAAO,qBAAQD,UAAR;QAAoBK,SAAS,EAAE;MAA/B,EAAP;IACD,CAFD,MAEO;MAELJ,OAAO,GAAGD,UAAV;IACD;EACF;;EAED,OAAO,IAAAM,mCAAA,EAAWL,OAAX,EAAoB;IACzBM,wBAAwB,EAAE,IADD;IAEzBC,eAAe,EAAEV,OAAO,CAACU;EAFA,CAApB,CAAP;AAID"}