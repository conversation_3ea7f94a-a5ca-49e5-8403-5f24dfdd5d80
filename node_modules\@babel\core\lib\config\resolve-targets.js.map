{"version": 3, "names": ["resolveBrowserslistConfigFile", "browserslistConfigFile", "configFileDir", "path", "resolve", "resolveTargets", "options", "root", "optTargets", "targets", "Array", "isArray", "browsers", "<PERSON><PERSON><PERSON><PERSON>", "configFile", "ignoreBrowserslistConfig", "getTargets", "config<PERSON><PERSON>", "browserslistEnv"], "sources": ["../../src/config/resolve-targets.ts"], "sourcesContent": ["type browserType = typeof import(\"./resolve-targets-browser\");\ntype nodeType = typeof import(\"./resolve-targets\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of index-browser, since this file may be replaced at bundle time with index-browser.\n({} as any as browserType as nodeType);\n\nimport type { ValidatedOptions } from \"./validation/options\";\nimport path from \"path\";\nimport getTargets, {\n  type InputTargets,\n} from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nexport function resolveBrowserslistConfigFile(\n  browserslistConfigFile: string,\n  configFileDir: string,\n): string | undefined {\n  return path.resolve(configFileDir, browserslistConfigFile);\n}\n\nexport function resolveTargets(\n  options: ValidatedOptions,\n  root: string,\n): Targets {\n  const optTargets = options.targets;\n  let targets: InputTargets;\n\n  if (typeof optTargets === \"string\" || Array.isArray(optTargets)) {\n    targets = { browsers: optTargets };\n  } else if (optTargets) {\n    if (\"esmodules\" in optTargets) {\n      targets = { ...optTargets, esmodules: \"intersect\" };\n    } else {\n      // https://github.com/microsoft/TypeScript/issues/17002\n      targets = optTargets as InputTargets;\n    }\n  }\n\n  const { browserslistConfigFile } = options;\n  let configFile;\n  let ignoreBrowserslistConfig = false;\n  if (typeof browserslistConfigFile === \"string\") {\n    configFile = browserslistConfigFile;\n  } else {\n    ignoreBrowserslistConfig = browserslistConfigFile === false;\n  }\n\n  return getTargets(targets, {\n    ignoreBrowserslistConfig,\n    configFile,\n    configPath: root,\n    browserslistEnv: options.browserslistEnv,\n  });\n}\n"], "mappings": ";;;;;;;;AAQA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAJA,CAAC,EAAD;;AAUO,SAASA,6BAAT,CACLC,sBADK,EAELC,aAFK,EAGe;EACpB,OAAOC,OAAA,CAAKC,OAAL,CAAaF,aAAb,EAA4BD,sBAA5B,CAAP;AACD;;AAEM,SAASI,cAAT,CACLC,OADK,EAELC,IAFK,EAGI;EACT,MAAMC,UAAU,GAAGF,OAAO,CAACG,OAA3B;EACA,IAAIA,OAAJ;;EAEA,IAAI,OAAOD,UAAP,KAAsB,QAAtB,IAAkCE,KAAK,CAACC,OAAN,CAAcH,UAAd,CAAtC,EAAiE;IAC/DC,OAAO,GAAG;MAAEG,QAAQ,EAAEJ;IAAZ,CAAV;EACD,CAFD,MAEO,IAAIA,UAAJ,EAAgB;IACrB,IAAI,eAAeA,UAAnB,EAA+B;MAC7BC,OAAO,qBAAQD,UAAR;QAAoBK,SAAS,EAAE;MAA/B,EAAP;IACD,CAFD,MAEO;MAELJ,OAAO,GAAGD,UAAV;IACD;EACF;;EAED,MAAM;IAAEP;EAAF,IAA6BK,OAAnC;EACA,IAAIQ,UAAJ;EACA,IAAIC,wBAAwB,GAAG,KAA/B;;EACA,IAAI,OAAOd,sBAAP,KAAkC,QAAtC,EAAgD;IAC9Ca,UAAU,GAAGb,sBAAb;EACD,CAFD,MAEO;IACLc,wBAAwB,GAAGd,sBAAsB,KAAK,KAAtD;EACD;;EAED,OAAO,IAAAe,mCAAA,EAAWP,OAAX,EAAoB;IACzBM,wBADyB;IAEzBD,UAFyB;IAGzBG,UAAU,EAAEV,IAHa;IAIzBW,eAAe,EAAEZ,OAAO,CAACY;EAJA,CAApB,CAAP;AAMD"}