{"version": 3, "names": ["makeConfigAPI", "cache", "env", "value", "using", "data", "envName", "assertSimpleType", "Array", "isArray", "some", "entry", "Error", "caller", "cb", "version", "coreVersion", "simple", "async", "assertVersion", "makePresetAPI", "externalDependencies", "targets", "JSON", "parse", "stringify", "addExternalDependency", "ref", "push", "makePluginAPI", "assumption", "name", "assumptions", "range", "Number", "isInteger", "semver", "satisfies", "limit", "stackTraceLimit", "err", "Object", "assign", "code"], "sources": ["../../../src/config/helpers/config-api.ts"], "sourcesContent": ["import semver from \"semver\";\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nimport { version as coreVersion } from \"../../\";\nimport { assertSimpleType } from \"../caching\";\nimport type {\n  CacheConfigurator,\n  SimpleCacheConfigurator,\n  SimpleType,\n} from \"../caching\";\n\nimport type { AssumptionName, CallerMetadata } from \"../validation/options\";\n\nimport type * as Context from \"../cache-contexts\";\n\ntype EnvFunction = {\n  (): string;\n  <T>(extractor: (babelEnv: string) => T): T;\n  (envVar: string): boolean;\n  (envVars: Array<string>): boolean;\n};\n\ntype CallerFactory = (\n  extractor: (callerMetadata: CallerMetadata | undefined) => unknown,\n) => SimpleType;\ntype TargetsFunction = () => Targets;\ntype AssumptionFunction = (name: AssumptionName) => boolean | undefined;\n\nexport type ConfigAPI = {\n  version: string;\n  cache: SimpleCacheConfigurator;\n  env: EnvFunction;\n  async: () => boolean;\n  assertVersion: typeof assertVersion;\n  caller?: CallerFactory;\n};\n\nexport type PresetAPI = {\n  targets: TargetsFunction;\n  addExternalDependency: (ref: string) => void;\n} & ConfigAPI;\n\nexport type PluginAPI = {\n  assumption: AssumptionFunction;\n} & PresetAPI;\n\nexport function makeConfigAPI<SideChannel extends Context.SimpleConfig>(\n  cache: CacheConfigurator<SideChannel>,\n): ConfigAPI {\n  // TODO(@nicolo-ribaudo): If we remove the explicit type from `value`\n  // and the `as any` type cast, TypeScript crashes in an infinite\n  // recursion. After upgrading to TS4.7 and finishing the noImplicitAny\n  // PR, we should check if it still crashes and report it to the TS team.\n  const env: EnvFunction = ((\n    value: string | string[] | (<T>(babelEnv: string) => T),\n  ) =>\n    cache.using(data => {\n      if (typeof value === \"undefined\") return data.envName;\n      if (typeof value === \"function\") {\n        return assertSimpleType(value(data.envName));\n      }\n      return (Array.isArray(value) ? value : [value]).some(entry => {\n        if (typeof entry !== \"string\") {\n          throw new Error(\"Unexpected non-string value\");\n        }\n        return entry === data.envName;\n      });\n    })) as any;\n\n  const caller = (cb: {\n    (CallerMetadata: CallerMetadata | undefined): SimpleType;\n  }) => cache.using(data => assertSimpleType(cb(data.caller)));\n\n  return {\n    version: coreVersion,\n    cache: cache.simple(),\n    // Expose \".env()\" so people can easily get the same env that we expose using the \"env\" key.\n    env,\n    async: () => false,\n    caller,\n    assertVersion,\n  };\n}\n\nexport function makePresetAPI<SideChannel extends Context.SimplePreset>(\n  cache: CacheConfigurator<SideChannel>,\n  externalDependencies: Array<string>,\n): PresetAPI {\n  const targets = () =>\n    // We are using JSON.parse/JSON.stringify because it's only possible to cache\n    // primitive values. We can safely stringify the targets object because it\n    // only contains strings as its properties.\n    // Please make the Record and Tuple proposal happen!\n    JSON.parse(cache.using(data => JSON.stringify(data.targets)));\n\n  const addExternalDependency = (ref: string) => {\n    externalDependencies.push(ref);\n  };\n\n  return { ...makeConfigAPI(cache), targets, addExternalDependency };\n}\n\nexport function makePluginAPI<SideChannel extends Context.SimplePlugin>(\n  cache: CacheConfigurator<SideChannel>,\n  externalDependencies: Array<string>,\n): PluginAPI {\n  const assumption = (name: string) =>\n    cache.using(data => data.assumptions[name]);\n\n  return { ...makePresetAPI(cache, externalDependencies), assumption };\n}\n\nfunction assertVersion(range: string | number): void {\n  if (typeof range === \"number\") {\n    if (!Number.isInteger(range)) {\n      throw new Error(\"Expected string or integer value.\");\n    }\n    range = `^${range}.0.0-0`;\n  }\n  if (typeof range !== \"string\") {\n    throw new Error(\"Expected string or integer value.\");\n  }\n\n  if (semver.satisfies(coreVersion, range)) return;\n\n  const limit = Error.stackTraceLimit;\n\n  if (typeof limit === \"number\" && limit < 25) {\n    // Bump up the limit if needed so that users are more likely\n    // to be able to see what is calling Babel.\n    Error.stackTraceLimit = 25;\n  }\n\n  const err = new Error(\n    `Requires Babel \"${range}\", but was loaded with \"${coreVersion}\". ` +\n      `If you are sure you have a compatible version of @babel/core, ` +\n      `it is likely that something in your build process is loading the ` +\n      `wrong version. Inspect the stack trace of this error to look for ` +\n      `the first entry that doesn't mention \"@babel/core\" or \"babel-core\" ` +\n      `to see what is calling Babel.`,\n  );\n\n  if (typeof limit === \"number\") {\n    Error.stackTraceLimit = limit;\n  }\n\n  throw Object.assign(err, {\n    code: \"BABEL_VERSION_UNSUPPORTED\",\n    version: coreVersion,\n    range,\n  });\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAGA;;AACA;;AA0CO,SAASA,aAAT,CACLC,KADK,EAEM;EAKX,MAAMC,GAAgB,GACpBC,KADwB,IAGxBF,KAAK,CAACG,KAAN,CAAYC,IAAI,IAAI;IAClB,IAAI,OAAOF,KAAP,KAAiB,WAArB,EAAkC,OAAOE,IAAI,CAACC,OAAZ;;IAClC,IAAI,OAAOH,KAAP,KAAiB,UAArB,EAAiC;MAC/B,OAAO,IAAAI,yBAAA,EAAiBJ,KAAK,CAACE,IAAI,CAACC,OAAN,CAAtB,CAAP;IACD;;IACD,OAAO,CAACE,KAAK,CAACC,OAAN,CAAcN,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,CAAhC,EAAyCO,IAAzC,CAA8CC,KAAK,IAAI;MAC5D,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;QAC7B,MAAM,IAAIC,KAAJ,CAAU,6BAAV,CAAN;MACD;;MACD,OAAOD,KAAK,KAAKN,IAAI,CAACC,OAAtB;IACD,CALM,CAAP;EAMD,CAXD,CAHF;;EAgBA,MAAMO,MAAM,GAAIC,EAAD,IAETb,KAAK,CAACG,KAAN,CAAYC,IAAI,IAAI,IAAAE,yBAAA,EAAiBO,EAAE,CAACT,IAAI,CAACQ,MAAN,CAAnB,CAApB,CAFN;;EAIA,OAAO;IACLE,OAAO,EAAEC,SADJ;IAELf,KAAK,EAAEA,KAAK,CAACgB,MAAN,EAFF;IAILf,GAJK;IAKLgB,KAAK,EAAE,MAAM,KALR;IAMLL,MANK;IAOLM;EAPK,CAAP;AASD;;AAEM,SAASC,aAAT,CACLnB,KADK,EAELoB,oBAFK,EAGM;EACX,MAAMC,OAAO,GAAG,MAKdC,IAAI,CAACC,KAAL,CAAWvB,KAAK,CAACG,KAAN,CAAYC,IAAI,IAAIkB,IAAI,CAACE,SAAL,CAAepB,IAAI,CAACiB,OAApB,CAApB,CAAX,CALF;;EAOA,MAAMI,qBAAqB,GAAIC,GAAD,IAAiB;IAC7CN,oBAAoB,CAACO,IAArB,CAA0BD,GAA1B;EACD,CAFD;;EAIA,yBAAY3B,aAAa,CAACC,KAAD,CAAzB;IAAkCqB,OAAlC;IAA2CI;EAA3C;AACD;;AAEM,SAASG,aAAT,CACL5B,KADK,EAELoB,oBAFK,EAGM;EACX,MAAMS,UAAU,GAAIC,IAAD,IACjB9B,KAAK,CAACG,KAAN,CAAYC,IAAI,IAAIA,IAAI,CAAC2B,WAAL,CAAiBD,IAAjB,CAApB,CADF;;EAGA,yBAAYX,aAAa,CAACnB,KAAD,EAAQoB,oBAAR,CAAzB;IAAwDS;EAAxD;AACD;;AAED,SAASX,aAAT,CAAuBc,KAAvB,EAAqD;EACnD,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC7B,IAAI,CAACC,MAAM,CAACC,SAAP,CAAiBF,KAAjB,CAAL,EAA8B;MAC5B,MAAM,IAAIrB,KAAJ,CAAU,mCAAV,CAAN;IACD;;IACDqB,KAAK,GAAI,IAAGA,KAAM,QAAlB;EACD;;EACD,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;IAC7B,MAAM,IAAIrB,KAAJ,CAAU,mCAAV,CAAN;EACD;;EAED,IAAIwB,SAAA,CAAOC,SAAP,CAAiBrB,SAAjB,EAA8BiB,KAA9B,CAAJ,EAA0C;EAE1C,MAAMK,KAAK,GAAG1B,KAAK,CAAC2B,eAApB;;EAEA,IAAI,OAAOD,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,GAAG,EAAzC,EAA6C;IAG3C1B,KAAK,CAAC2B,eAAN,GAAwB,EAAxB;EACD;;EAED,MAAMC,GAAG,GAAG,IAAI5B,KAAJ,CACT,mBAAkBqB,KAAM,2BAA0BjB,SAAY,KAA/D,GACG,gEADH,GAEG,mEAFH,GAGG,mEAHH,GAIG,qEAJH,GAKG,+BANO,CAAZ;;EASA,IAAI,OAAOsB,KAAP,KAAiB,QAArB,EAA+B;IAC7B1B,KAAK,CAAC2B,eAAN,GAAwBD,KAAxB;EACD;;EAED,MAAMG,MAAM,CAACC,MAAP,CAAcF,GAAd,EAAmB;IACvBG,IAAI,EAAE,2BADiB;IAEvB5B,OAAO,EAAEC,SAFc;IAGvBiB;EAHuB,CAAnB,CAAN;AAKD"}