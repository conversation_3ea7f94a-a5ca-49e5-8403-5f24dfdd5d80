{"version": 3, "names": ["once", "fn", "result", "resultP", "isAsync", "waitFor", "resolve", "reject", "Promise", "res", "rej", "error"], "sources": ["../../src/gensync-utils/functional.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport { isAsync, waitFor } from \"./async\";\n\nexport function once<R>(fn: () => Handler<R>): () => Handler<R> {\n  let result: R;\n  let resultP: Promise<R>;\n  return function* () {\n    if (result) return result;\n    if (!(yield* isAsync())) return (result = yield* fn());\n    if (resultP) return yield* waitFor(resultP);\n\n    let resolve: (result: R) => void, reject: (error: unknown) => void;\n    resultP = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n\n    try {\n      result = yield* fn();\n      // Avoid keeping the promise around\n      // now that we have the result.\n      resultP = null;\n      resolve(result);\n      return result;\n    } catch (error) {\n      reject(error);\n      throw error;\n    }\n  };\n}\n"], "mappings": ";;;;;;;AAEA;;AAEO,SAASA,IAAT,CAAiBC,EAAjB,EAAyD;EAC9D,IAAIC,MAAJ;EACA,IAAIC,OAAJ;EACA,OAAO,aAAa;IAClB,IAAID,MAAJ,EAAY,OAAOA,MAAP;IACZ,IAAI,EAAE,OAAO,IAAAE,cAAA,GAAT,CAAJ,EAAyB,OAAQF,MAAM,GAAG,OAAOD,EAAE,EAA1B;IACzB,IAAIE,OAAJ,EAAa,OAAO,OAAO,IAAAE,cAAA,EAAQF,OAAR,CAAd;IAEb,IAAIG,OAAJ,EAAkCC,MAAlC;IACAJ,OAAO,GAAG,IAAIK,OAAJ,CAAY,CAACC,GAAD,EAAMC,GAAN,KAAc;MAClCJ,OAAO,GAAGG,GAAV;MACAF,MAAM,GAAGG,GAAT;IACD,CAHS,CAAV;;IAKA,IAAI;MACFR,MAAM,GAAG,OAAOD,EAAE,EAAlB;MAGAE,OAAO,GAAG,IAAV;MACAG,OAAO,CAACJ,MAAD,CAAP;MACA,OAAOA,MAAP;IACD,CAPD,CAOE,OAAOS,KAAP,EAAc;MACdJ,MAAM,CAACI,KAAD,CAAN;MACA,MAAMA,KAAN;IACD;EACF,CAtBD;AAuBD"}