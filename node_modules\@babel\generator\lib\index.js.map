{"version": 3, "names": ["Generator", "Printer", "constructor", "ast", "opts", "code", "format", "normalizeOptions", "map", "sourceMaps", "SourceMap", "generate", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "shouldPrintComment", "retainLines", "retainFunctionParens", "comments", "compact", "minified", "concise", "indent", "adjustMultilineComment", "style", "jsescOption", "quotes", "wrap", "minimal", "recordAndTupleSyntaxType", "topicToken", "decoratorsBeforeExport", "jsonCompatibleStrings", "value", "includes", "length", "console", "error", "filename", "CodeGenerator", "_generator", "gen"], "sources": ["../src/index.ts"], "sourcesContent": ["import SourceMap from \"./source-map\";\nimport Printer from \"./printer\";\nimport type * as t from \"@babel/types\";\nimport type { Opts as jsescOptions } from \"jsesc\";\nimport type { Format } from \"./printer\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { DecodedSourceMap, Mapping } from \"@jridgewell/gen-mapping\";\n\n/**\n * <PERSON><PERSON>'s code generator, turns an ast into code, maintaining sourcemaps,\n * user preferences, and valid output.\n */\n\nclass Generator extends Printer {\n  constructor(\n    ast: t.Node,\n    opts: GeneratorOptions = {},\n    code: string | { [filename: string]: string },\n  ) {\n    const format = normalizeOptions(code, opts);\n    const map = opts.sourceMaps ? new SourceMap(opts, code) : null;\n    super(format, map);\n\n    this.ast = ast;\n  }\n\n  ast: t.Node;\n\n  /**\n   * Generate code and sourcemap from ast.\n   *\n   * Appends comments that weren't attached to any node to the end of the generated output.\n   */\n\n  generate() {\n    return super.generate(this.ast);\n  }\n}\n\n/**\n * Normalize generator options, setting defaults.\n *\n * - Detects code indentation.\n * - If `opts.compact = \"auto\"` and the code is over 500KB, `compact` will be set to `true`.\n */\n\nfunction normalizeOptions(\n  code: string | { [filename: string]: string },\n  opts: GeneratorOptions,\n): Format {\n  const format: Format = {\n    auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n    auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n    shouldPrintComment: opts.shouldPrintComment,\n    retainLines: opts.retainLines,\n    retainFunctionParens: opts.retainFunctionParens,\n    comments: opts.comments == null || opts.comments,\n    compact: opts.compact,\n    minified: opts.minified,\n    concise: opts.concise,\n    indent: {\n      adjustMultilineComment: true,\n      style: \"  \",\n    },\n    jsescOption: {\n      quotes: \"double\",\n      wrap: true,\n      minimal: process.env.BABEL_8_BREAKING ? true : false,\n      ...opts.jsescOption,\n    },\n    recordAndTupleSyntaxType: opts.recordAndTupleSyntaxType,\n    topicToken: opts.topicToken,\n  };\n\n  if (!process.env.BABEL_8_BREAKING) {\n    format.decoratorsBeforeExport = !!opts.decoratorsBeforeExport;\n    format.jsonCompatibleStrings = opts.jsonCompatibleStrings;\n  }\n\n  if (format.minified) {\n    format.compact = true;\n\n    format.shouldPrintComment =\n      format.shouldPrintComment || (() => format.comments);\n  } else {\n    format.shouldPrintComment =\n      format.shouldPrintComment ||\n      (value =>\n        format.comments ||\n        value.includes(\"@license\") ||\n        value.includes(\"@preserve\"));\n  }\n\n  if (format.compact === \"auto\") {\n    format.compact = code.length > 500_000; // 500KB\n\n    if (format.compact) {\n      console.error(\n        \"[BABEL] Note: The code generator has deoptimised the styling of \" +\n          `${opts.filename} as it exceeds the max of ${\"500KB\"}.`,\n      );\n    }\n  }\n\n  if (format.compact) {\n    format.indent.adjustMultilineComment = false;\n  }\n\n  return format;\n}\n\nexport interface GeneratorOptions {\n  /**\n   * Optional string to add as a block comment at the start of the output file.\n   */\n  auxiliaryCommentBefore?: string;\n\n  /**\n   * Optional string to add as a block comment at the end of the output file.\n   */\n  auxiliaryCommentAfter?: string;\n\n  /**\n   * Function that takes a comment (as a string) and returns true if the comment should be included in the output.\n   * By default, comments are included if `opts.comments` is `true` or if `opts.minifed` is `false` and the comment\n   * contains `@preserve` or `@license`.\n   */\n  shouldPrintComment?(comment: string): boolean;\n\n  /**\n   * Attempt to use the same line numbers in the output code as in the source code (helps preserve stack traces).\n   * Defaults to `false`.\n   */\n  retainLines?: boolean;\n\n  /**\n   * Retain parens around function expressions (could be used to change engine parsing behavior)\n   * Defaults to `false`.\n   */\n  retainFunctionParens?: boolean;\n\n  /**\n   * Should comments be included in output? Defaults to `true`.\n   */\n  comments?: boolean;\n\n  /**\n   * Set to true to avoid adding whitespace for formatting. Defaults to the value of `opts.minified`.\n   */\n  compact?: boolean | \"auto\";\n\n  /**\n   * Should the output be minified. Defaults to `false`.\n   */\n  minified?: boolean;\n\n  /**\n   * Set to true to reduce whitespace (but not as much as opts.compact). Defaults to `false`.\n   */\n  concise?: boolean;\n\n  /**\n   * Used in warning messages\n   */\n  filename?: string;\n\n  /**\n   * Enable generating source maps. Defaults to `false`.\n   */\n  sourceMaps?: boolean;\n\n  /**\n   * A root for all relative URLs in the source map.\n   */\n  sourceRoot?: string;\n\n  /**\n   * The filename for the source code (i.e. the code in the `code` argument).\n   * This will only be used if `code` is a string.\n   */\n  sourceFileName?: string;\n\n  /**\n   * Set to true to run jsesc with \"json\": true to print \"\\u00A9\" vs. \"©\";\n   * @deprecated use `jsescOptions: { json: true }` instead\n   */\n  jsonCompatibleStrings?: boolean;\n\n  /**\n   * Set to true to enable support for experimental decorators syntax before module exports.\n   * Defaults to `false`.\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n\n  /**\n   * Options for outputting jsesc representation.\n   */\n  jsescOption?: jsescOptions;\n\n  /**\n   * For use with the recordAndTuple token.\n   */\n  recordAndTupleSyntaxType?: RecordAndTuplePluginOptions[\"syntaxType\"];\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n}\n\nexport interface GeneratorResult {\n  code: string;\n  map: {\n    version: number;\n    sources: readonly string[];\n    names: readonly string[];\n    sourceRoot?: string;\n    sourcesContent?: readonly string[];\n    mappings: string;\n    file?: string;\n  } | null;\n  decodedMap: DecodedSourceMap | undefined;\n  rawMappings: Mapping[] | undefined;\n}\n\n/**\n * We originally exported the Generator class above, but to make it extra clear that it is a private API,\n * we have moved that to an internal class instance and simplified the interface to the two public methods\n * that we wish to support.\n */\n\nexport class CodeGenerator {\n  private _generator: Generator;\n  constructor(ast: t.Node, opts?: GeneratorOptions, code?: string) {\n    this._generator = new Generator(ast, opts, code);\n  }\n  generate(): GeneratorResult {\n    return this._generator.generate();\n  }\n}\n\n/**\n * Turns an AST into code, maintaining sourcemaps, user preferences, and valid output.\n * @param ast - the abstract syntax tree from which to generate output code.\n * @param opts - used for specifying options for code generation.\n * @param code - the original source code, used for source maps.\n * @returns - an object containing the output code and source map.\n */\nexport default function generate(\n  ast: t.Node,\n  opts?: GeneratorOptions,\n  code?: string | { [filename: string]: string },\n) {\n  const gen = new Generator(ast, opts, code);\n  return gen.generate();\n}\n"], "mappings": ";;;;;;;;AAAA;;AACA;;AAeA,MAAMA,SAAN,SAAwBC,gBAAxB,CAAgC;EAC9BC,WAAW,CACTC,GADS,EAETC,IAAsB,GAAG,EAFhB,EAGTC,IAHS,EAIT;IACA,MAAMC,MAAM,GAAGC,gBAAgB,CAACF,IAAD,EAAOD,IAAP,CAA/B;IACA,MAAMI,GAAG,GAAGJ,IAAI,CAACK,UAAL,GAAkB,IAAIC,kBAAJ,CAAcN,IAAd,EAAoBC,IAApB,CAAlB,GAA8C,IAA1D;IACA,MAAMC,MAAN,EAAcE,GAAd;IAHA,KAQFL,GARE;IAKA,KAAKA,GAAL,GAAWA,GAAX;EACD;;EAUDQ,QAAQ,GAAG;IACT,OAAO,MAAMA,QAAN,CAAe,KAAKR,GAApB,CAAP;EACD;;AAvB6B;;AAiChC,SAASI,gBAAT,CACEF,IADF,EAEED,IAFF,EAGU;EACR,MAAME,MAAc,GAAG;IACrBM,sBAAsB,EAAER,IAAI,CAACQ,sBADR;IAErBC,qBAAqB,EAAET,IAAI,CAACS,qBAFP;IAGrBC,kBAAkB,EAAEV,IAAI,CAACU,kBAHJ;IAIrBC,WAAW,EAAEX,IAAI,CAACW,WAJG;IAKrBC,oBAAoB,EAAEZ,IAAI,CAACY,oBALN;IAMrBC,QAAQ,EAAEb,IAAI,CAACa,QAAL,IAAiB,IAAjB,IAAyBb,IAAI,CAACa,QANnB;IAOrBC,OAAO,EAAEd,IAAI,CAACc,OAPO;IAQrBC,QAAQ,EAAEf,IAAI,CAACe,QARM;IASrBC,OAAO,EAAEhB,IAAI,CAACgB,OATO;IAUrBC,MAAM,EAAE;MACNC,sBAAsB,EAAE,IADlB;MAENC,KAAK,EAAE;IAFD,CAVa;IAcrBC,WAAW;MACTC,MAAM,EAAE,QADC;MAETC,IAAI,EAAE,IAFG;MAGTC,OAAO,EAAwC;IAHtC,GAINvB,IAAI,CAACoB,WAJC,CAdU;IAoBrBI,wBAAwB,EAAExB,IAAI,CAACwB,wBApBV;IAqBrBC,UAAU,EAAEzB,IAAI,CAACyB;EArBI,CAAvB;EAwBmC;IACjCvB,MAAM,CAACwB,sBAAP,GAAgC,CAAC,CAAC1B,IAAI,CAAC0B,sBAAvC;IACAxB,MAAM,CAACyB,qBAAP,GAA+B3B,IAAI,CAAC2B,qBAApC;EACD;;EAED,IAAIzB,MAAM,CAACa,QAAX,EAAqB;IACnBb,MAAM,CAACY,OAAP,GAAiB,IAAjB;;IAEAZ,MAAM,CAACQ,kBAAP,GACER,MAAM,CAACQ,kBAAP,KAA8B,MAAMR,MAAM,CAACW,QAA3C,CADF;EAED,CALD,MAKO;IACLX,MAAM,CAACQ,kBAAP,GACER,MAAM,CAACQ,kBAAP,KACCkB,KAAK,IACJ1B,MAAM,CAACW,QAAP,IACAe,KAAK,CAACC,QAAN,CAAe,UAAf,CADA,IAEAD,KAAK,CAACC,QAAN,CAAe,WAAf,CAJF,CADF;EAMD;;EAED,IAAI3B,MAAM,CAACY,OAAP,KAAmB,MAAvB,EAA+B;IAC7BZ,MAAM,CAACY,OAAP,GAAiBb,IAAI,CAAC6B,MAAL,GAAc,MAA/B;;IAEA,IAAI5B,MAAM,CAACY,OAAX,EAAoB;MAClBiB,OAAO,CAACC,KAAR,CACE,qEACG,GAAEhC,IAAI,CAACiC,QAAS,6BAA4B,OAAQ,GAFzD;IAID;EACF;;EAED,IAAI/B,MAAM,CAACY,OAAX,EAAoB;IAClBZ,MAAM,CAACe,MAAP,CAAcC,sBAAd,GAAuC,KAAvC;EACD;;EAED,OAAOhB,MAAP;AACD;;AA2HM,MAAMgC,aAAN,CAAoB;EAEzBpC,WAAW,CAACC,GAAD,EAAcC,IAAd,EAAuCC,IAAvC,EAAsD;IAAA,KADzDkC,UACyD;IAC/D,KAAKA,UAAL,GAAkB,IAAIvC,SAAJ,CAAcG,GAAd,EAAmBC,IAAnB,EAAyBC,IAAzB,CAAlB;EACD;;EACDM,QAAQ,GAAoB;IAC1B,OAAO,KAAK4B,UAAL,CAAgB5B,QAAhB,EAAP;EACD;;AAPwB;;;;AAiBZ,SAASA,QAAT,CACbR,GADa,EAEbC,IAFa,EAGbC,IAHa,EAIb;EACA,MAAMmC,GAAG,GAAG,IAAIxC,SAAJ,CAAcG,GAAd,EAAmBC,IAAnB,EAAyBC,IAAzB,CAAZ;EACA,OAAOmC,GAAG,CAAC7B,QAAJ,EAAP;AACD"}