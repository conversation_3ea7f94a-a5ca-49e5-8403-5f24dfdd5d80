{"version": 3, "names": ["<PERSON><PERSON>", "getCode", "getScope", "addHelper", "Error", "buildError", "node", "msg", "TypeError"], "sources": ["../src/hub.ts"], "sourcesContent": ["import type Scope from \"./scope\";\nimport type { Node } from \"@babel/types\";\n\nexport interface HubInterface {\n  getCode(): string | void;\n  getScope(): Scope | void;\n  addHelper(name: string): any;\n  buildError(node: Node, msg: string, Error: new () => Error): Error;\n}\n\nexport default class Hub implements HubInterface {\n  getCode() {}\n\n  getScope() {}\n\n  addHelper() {\n    throw new Error(\"Helpers are not supported by the default hub.\");\n  }\n\n  buildError(node: Node, msg: string, Error = TypeError): Error {\n    return new Error(msg);\n  }\n}\n"], "mappings": ";;;;;;;AAUe,MAAMA,GAAN,CAAkC;EAC/CC,OAAO,GAAG,CAAE;;EAEZC,QAAQ,GAAG,CAAE;;EAEbC,SAAS,GAAG;IACV,MAAM,IAAIC,KAAJ,CAAU,+CAAV,CAAN;EACD;;EAEDC,UAAU,CAACC,IAAD,EAAaC,GAAb,EAA0BH,KAAK,GAAGI,SAAlC,EAAoD;IAC5D,OAAO,IAAIJ,KAAJ,CAAUG,GAAV,CAAP;EACD;;AAX8C"}