import {
  o as o2
} from "./chunk-B2ZHVKHC.js";
import {
  a,
  o
} from "./chunk-IFV3GWMF.js";
import "./chunk-AHEYWOTW.js";

// node_modules/@tauri-apps/api/dialog-5c2a33bf.js
function e(e2) {
  return void 0 === e2 && (e2 = {}), o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return "object" == typeof e2 && Object.freeze(e2), [2, o2({ __tauriModule: "Dialog", message: { cmd: "openDialog", options: e2 } })];
    });
  });
}
function n(e2) {
  return void 0 === e2 && (e2 = {}), o(this, void 0, void 0, function() {
    return a(this, function(t) {
      return "object" == typeof e2 && Object.freeze(e2), [2, o2({ __tauriModule: "Dialog", message: { cmd: "saveDialog", options: e2 } })];
    });
  });
}
function r(e2, n2) {
  var r2;
  return o(this, void 0, void 0, function() {
    var t;
    return a(this, function(i) {
      return t = "string" == typeof n2 ? { title: n2 } : n2, [2, o2({ __tauriModule: "Dialog", message: { cmd: "messageDialog", message: e2.toString(), title: null === (r2 = null == t ? void 0 : t.title) || void 0 === r2 ? void 0 : r2.toString(), type: null == t ? void 0 : t.type } })];
    });
  });
}
function s(e2, n2) {
  var r2;
  return o(this, void 0, void 0, function() {
    var t;
    return a(this, function(i) {
      return t = "string" == typeof n2 ? { title: n2 } : n2, [2, o2({ __tauriModule: "Dialog", message: { cmd: "askDialog", message: e2.toString(), title: null === (r2 = null == t ? void 0 : t.title) || void 0 === r2 ? void 0 : r2.toString(), type: null == t ? void 0 : t.type } })];
    });
  });
}
function u(e2, n2) {
  var r2;
  return o(this, void 0, void 0, function() {
    var t;
    return a(this, function(i) {
      return t = "string" == typeof n2 ? { title: n2 } : n2, [2, o2({ __tauriModule: "Dialog", message: { cmd: "confirmDialog", message: e2.toString(), title: null === (r2 = null == t ? void 0 : t.title) || void 0 === r2 ? void 0 : r2.toString(), type: null == t ? void 0 : t.type } })];
    });
  });
}
var a2 = Object.freeze({ __proto__: null, open: e, save: n, message: r, ask: s, confirm: u });
export {
  s as ask,
  u as confirm,
  r as message,
  e as open,
  n as save
};
//# sourceMappingURL=@tauri-apps_api_dialog.js.map
