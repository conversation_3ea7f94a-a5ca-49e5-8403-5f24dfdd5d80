import {
  conf,
  language
} from "./chunk-BITZY6MC.js";
import "./chunk-PVIGDAPC.js";
import "./chunk-AHEYWOTW.js";

// node_modules/monaco-editor/esm/vs/basic-languages/javascript/javascript.js
var conf2 = conf;
var language2 = {
  defaultToken: "invalid",
  tokenPostfix: ".js",
  keywords: [
    "break",
    "case",
    "catch",
    "class",
    "continue",
    "const",
    "constructor",
    "debugger",
    "default",
    "delete",
    "do",
    "else",
    "export",
    "extends",
    "false",
    "finally",
    "for",
    "from",
    "function",
    "get",
    "if",
    "import",
    "in",
    "instanceof",
    "let",
    "new",
    "null",
    "return",
    "set",
    "static",
    "super",
    "switch",
    "symbol",
    "this",
    "throw",
    "true",
    "try",
    "typeof",
    "undefined",
    "var",
    "void",
    "while",
    "with",
    "yield",
    "async",
    "await",
    "of"
  ],
  typeKeywords: [],
  operators: language.operators,
  symbols: language.symbols,
  escapes: language.escapes,
  digits: language.digits,
  octaldigits: language.octaldigits,
  binarydigits: language.binarydigits,
  hexdigits: language.hexdigits,
  regexpctl: language.regexpctl,
  regexpesc: language.regexpesc,
  tokenizer: language.tokenizer
};
export {
  conf2 as conf,
  language2 as language
};
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.44.0(3e047efd345ff102c8c61b5398fb30845aaac166)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/
//# sourceMappingURL=javascript-6RZARDWY.js.map
