{"version": 3, "names": ["gens<PERSON>", "loadFullConfig", "inputOpts", "result", "loadPrivatePartialConfig", "options", "context", "fileHandling", "optionDefaults", "plugins", "presets", "Error", "presetContext", "targets", "toDescriptor", "item", "desc", "getItemDescriptor", "presetsDescriptors", "map", "initialPluginsDescriptors", "pluginDescriptorsByPass", "passes", "externalDependencies", "ignored", "enhanceError", "recursePresetDescriptors", "rawPresets", "pluginDescriptorsPass", "i", "length", "descriptor", "preset", "loadPresetDescriptor", "e", "code", "checkNoUnwrappedItemOptionPairs", "push", "ownPass", "chain", "pass", "unshift", "splice", "o", "filter", "p", "for<PERSON>ach", "opts", "mergeOptions", "pluginContext", "assumptions", "loadPluginDescriptors", "descs", "plugin", "loadPluginDescriptor", "slice", "passPerPreset", "freezeDeepArray", "fn", "arg1", "arg2", "test", "message", "filename", "makeDescriptorLoader", "apiFactory", "makeWeakCache", "value", "dirname", "alias", "cache", "factory", "maybe<PERSON><PERSON>", "api", "JSON", "stringify", "isThenable", "configured", "mode", "error", "pluginDescriptorLoader", "makePluginAPI", "presetDes<PERSON>or<PERSON><PERSON><PERSON>", "makePresetAPI", "Plugin", "instantiatePlugin", "pluginObj", "validatePluginObject", "visitor", "traverse", "explode", "inherits", "inheritsDescriptor", "name", "undefined", "forwardAsync", "run", "invalidate", "data", "pre", "post", "manipulateOptions", "visitors", "merge", "needsFilename", "val", "validateIfOptionNeedsFilename", "include", "exclude", "formattedPresetName", "ConfigError", "join", "validatePreset", "overrides", "overrideOptions", "instantiatePreset", "buildPresetChain", "makeWeakCacheSync", "validate", "a", "b", "fns", "Boolean", "args", "apply"], "sources": ["../../src/config/full.ts"], "sourcesContent": ["import gensync, { type Handler } from \"gensync\";\nimport { forwardAsync, maybeAsync, isThenable } from \"../gensync-utils/async\";\n\nimport { mergeOptions } from \"./util\";\nimport * as context from \"../index\";\nimport Plugin from \"./plugin\";\nimport { getItemDescriptor } from \"./item\";\nimport { buildPresetChain } from \"./config-chain\";\nimport { finalize as freezeDeepArray } from \"./helpers/deep-array\";\nimport type { DeepArray, ReadonlyDeepArray } from \"./helpers/deep-array\";\nimport type {\n  ConfigContext,\n  ConfigChain,\n  PresetInstance,\n} from \"./config-chain\";\nimport type { UnloadedDescriptor } from \"./config-descriptors\";\nimport traverse from \"@babel/traverse\";\nimport { makeWeakCache, makeWeakCacheSync } from \"./caching\";\nimport type { CacheConfigurator } from \"./caching\";\nimport {\n  validate,\n  checkNoUnwrappedItemOptionPairs,\n} from \"./validation/options\";\nimport type { PluginItem } from \"./validation/options\";\nimport { validatePluginObject } from \"./validation/plugins\";\nimport { makePluginAPI, makePresetAPI } from \"./helpers/config-api\";\nimport type { PluginAPI, PresetAPI } from \"./helpers/config-api\";\n\nimport loadPrivatePartialConfig from \"./partial\";\nimport type { ValidatedOptions } from \"./validation/options\";\n\nimport type * as Context from \"./cache-contexts\";\nimport ConfigError from \"../errors/config-error\";\n\ntype LoadedDescriptor = {\n  value: {};\n  options: {};\n  dirname: string;\n  alias: string;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type { InputOptions } from \"./validation/options\";\n\nexport type ResolvedConfig = {\n  options: any;\n  passes: PluginPasses;\n  externalDependencies: ReadonlyDeepArray<string>;\n};\n\nexport type { Plugin };\nexport type PluginPassList = Array<Plugin>;\nexport type PluginPasses = Array<PluginPassList>;\n\nexport default gensync(function* loadFullConfig(\n  inputOpts: unknown,\n): Handler<ResolvedConfig | null> {\n  const result = yield* loadPrivatePartialConfig(inputOpts);\n  if (!result) {\n    return null;\n  }\n  const { options, context, fileHandling } = result;\n\n  if (fileHandling === \"ignored\") {\n    return null;\n  }\n\n  const optionDefaults = {};\n\n  const { plugins, presets } = options;\n\n  if (!plugins || !presets) {\n    throw new Error(\"Assertion failure - plugins and presets exist\");\n  }\n\n  const presetContext: Context.FullPreset = {\n    ...context,\n    targets: options.targets,\n  };\n\n  const toDescriptor = (item: PluginItem) => {\n    const desc = getItemDescriptor(item);\n    if (!desc) {\n      throw new Error(\"Assertion failure - must be config item\");\n    }\n\n    return desc;\n  };\n\n  const presetsDescriptors = presets.map(toDescriptor);\n  const initialPluginsDescriptors = plugins.map(toDescriptor);\n  const pluginDescriptorsByPass: Array<Array<UnloadedDescriptor>> = [[]];\n  const passes: Array<Array<Plugin>> = [];\n\n  const externalDependencies: DeepArray<string> = [];\n\n  const ignored = yield* enhanceError(\n    context,\n    function* recursePresetDescriptors(\n      rawPresets: Array<UnloadedDescriptor>,\n      pluginDescriptorsPass: Array<UnloadedDescriptor>,\n    ): Handler<true | void> {\n      const presets: Array<{\n        preset: ConfigChain | null;\n        pass: Array<UnloadedDescriptor>;\n      }> = [];\n\n      for (let i = 0; i < rawPresets.length; i++) {\n        const descriptor = rawPresets[i];\n        if (descriptor.options !== false) {\n          try {\n            // eslint-disable-next-line no-var\n            var preset = yield* loadPresetDescriptor(descriptor, presetContext);\n          } catch (e) {\n            if (e.code === \"BABEL_UNKNOWN_OPTION\") {\n              checkNoUnwrappedItemOptionPairs(rawPresets, i, \"preset\", e);\n            }\n            throw e;\n          }\n\n          externalDependencies.push(preset.externalDependencies);\n\n          // Presets normally run in reverse order, but if they\n          // have their own pass they run after the presets\n          // in the previous pass.\n          if (descriptor.ownPass) {\n            presets.push({ preset: preset.chain, pass: [] });\n          } else {\n            presets.unshift({\n              preset: preset.chain,\n              pass: pluginDescriptorsPass,\n            });\n          }\n        }\n      }\n\n      // resolve presets\n      if (presets.length > 0) {\n        // The passes are created in the same order as the preset list, but are inserted before any\n        // existing additional passes.\n        pluginDescriptorsByPass.splice(\n          1,\n          0,\n          ...presets.map(o => o.pass).filter(p => p !== pluginDescriptorsPass),\n        );\n\n        for (const { preset, pass } of presets) {\n          if (!preset) return true;\n\n          pass.push(...preset.plugins);\n\n          const ignored = yield* recursePresetDescriptors(preset.presets, pass);\n          if (ignored) return true;\n\n          preset.options.forEach(opts => {\n            mergeOptions(optionDefaults, opts);\n          });\n        }\n      }\n    },\n  )(presetsDescriptors, pluginDescriptorsByPass[0]);\n\n  if (ignored) return null;\n\n  const opts: any = optionDefaults;\n  mergeOptions(opts, options);\n\n  const pluginContext: Context.FullPlugin = {\n    ...presetContext,\n    assumptions: opts.assumptions ?? {},\n  };\n\n  yield* enhanceError(context, function* loadPluginDescriptors() {\n    pluginDescriptorsByPass[0].unshift(...initialPluginsDescriptors);\n\n    for (const descs of pluginDescriptorsByPass) {\n      const pass: Plugin[] = [];\n      passes.push(pass);\n\n      for (let i = 0; i < descs.length; i++) {\n        const descriptor: UnloadedDescriptor = descs[i];\n        if (descriptor.options !== false) {\n          try {\n            // eslint-disable-next-line no-var\n            var plugin = yield* loadPluginDescriptor(descriptor, pluginContext);\n          } catch (e) {\n            if (e.code === \"BABEL_UNKNOWN_PLUGIN_PROPERTY\") {\n              // print special message for `plugins: [\"@babel/foo\", { foo: \"option\" }]`\n              checkNoUnwrappedItemOptionPairs(descs, i, \"plugin\", e);\n            }\n            throw e;\n          }\n          pass.push(plugin);\n\n          externalDependencies.push(plugin.externalDependencies);\n        }\n      }\n    }\n  })();\n\n  opts.plugins = passes[0];\n  opts.presets = passes\n    .slice(1)\n    .filter(plugins => plugins.length > 0)\n    .map(plugins => ({ plugins }));\n  opts.passPerPreset = opts.presets.length > 0;\n\n  return {\n    options: opts,\n    passes: passes,\n    externalDependencies: freezeDeepArray(externalDependencies),\n  };\n});\n\nfunction enhanceError<T extends Function>(context: ConfigContext, fn: T): T {\n  return function* (arg1: unknown, arg2: unknown) {\n    try {\n      return yield* fn(arg1, arg2);\n    } catch (e) {\n      // There are a few case where thrown errors will try to annotate themselves multiple times, so\n      // to keep things simple we just bail out if re-wrapping the message.\n      if (!/^\\[BABEL\\]/.test(e.message)) {\n        e.message = `[BABEL] ${context.filename ?? \"unknown file\"}: ${\n          e.message\n        }`;\n      }\n\n      throw e;\n    }\n  } as any;\n}\n\n/**\n * Load a generic plugin/preset from the given descriptor loaded from the config object.\n */\nconst makeDescriptorLoader = <Context, API>(\n  apiFactory: (\n    cache: CacheConfigurator<Context>,\n    externalDependencies: Array<string>,\n  ) => API,\n) =>\n  makeWeakCache(function* (\n    { value, options, dirname, alias }: UnloadedDescriptor,\n    cache: CacheConfigurator<Context>,\n  ): Handler<LoadedDescriptor> {\n    // Disabled presets should already have been filtered out\n    if (options === false) throw new Error(\"Assertion failure\");\n\n    options = options || {};\n\n    const externalDependencies: Array<string> = [];\n\n    let item = value;\n    if (typeof value === \"function\") {\n      const factory = maybeAsync(\n        value,\n        `You appear to be using an async plugin/preset, but Babel has been called synchronously`,\n      );\n\n      const api = {\n        ...context,\n        ...apiFactory(cache, externalDependencies),\n      };\n      try {\n        item = yield* factory(api, options, dirname);\n      } catch (e) {\n        if (alias) {\n          e.message += ` (While processing: ${JSON.stringify(alias)})`;\n        }\n        throw e;\n      }\n    }\n\n    if (!item || typeof item !== \"object\") {\n      throw new Error(\"Plugin/Preset did not return an object.\");\n    }\n\n    if (isThenable(item)) {\n      // @ts-expect-error - if we want to support async plugins\n      yield* [];\n\n      throw new Error(\n        `You appear to be using a promise as a plugin, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, ` +\n          `you may need to upgrade your @babel/core version. ` +\n          `As an alternative, you can prefix the promise with \"await\". ` +\n          `(While processing: ${JSON.stringify(alias)})`,\n      );\n    }\n\n    if (\n      externalDependencies.length > 0 &&\n      (!cache.configured() || cache.mode() === \"forever\")\n    ) {\n      let error =\n        `A plugin/preset has external untracked dependencies ` +\n        `(${externalDependencies[0]}), but the cache `;\n      if (!cache.configured()) {\n        error += `has not been configured to be invalidated when the external dependencies change. `;\n      } else {\n        error += ` has been configured to never be invalidated. `;\n      }\n      error +=\n        `Plugins/presets should configure their cache to be invalidated when the external ` +\n        `dependencies change, for example using \\`api.cache.invalidate(() => ` +\n        `statSync(filepath).mtimeMs)\\` or \\`api.cache.never()\\`\\n` +\n        `(While processing: ${JSON.stringify(alias)})`;\n\n      throw new Error(error);\n    }\n\n    return {\n      value: item,\n      options,\n      dirname,\n      alias,\n      externalDependencies: freezeDeepArray(externalDependencies),\n    };\n  });\n\nconst pluginDescriptorLoader = makeDescriptorLoader<\n  Context.SimplePlugin,\n  PluginAPI\n>(makePluginAPI);\nconst presetDescriptorLoader = makeDescriptorLoader<\n  Context.SimplePreset,\n  PresetAPI\n>(makePresetAPI);\n\n/**\n * Instantiate a plugin for the given descriptor, returning the plugin/options pair.\n */\nfunction* loadPluginDescriptor(\n  descriptor: UnloadedDescriptor,\n  context: Context.SimplePlugin,\n): Handler<Plugin> {\n  if (descriptor.value instanceof Plugin) {\n    if (descriptor.options) {\n      throw new Error(\n        \"Passed options to an existing Plugin instance will not work.\",\n      );\n    }\n\n    return descriptor.value;\n  }\n\n  return yield* instantiatePlugin(\n    yield* pluginDescriptorLoader(descriptor, context),\n    context,\n  );\n}\n\nconst instantiatePlugin = makeWeakCache(function* (\n  { value, options, dirname, alias, externalDependencies }: LoadedDescriptor,\n  cache: CacheConfigurator<Context.SimplePlugin>,\n): Handler<Plugin> {\n  const pluginObj = validatePluginObject(value);\n\n  const plugin = {\n    ...pluginObj,\n  };\n  if (plugin.visitor) {\n    plugin.visitor = traverse.explode({\n      ...plugin.visitor,\n    });\n  }\n\n  if (plugin.inherits) {\n    const inheritsDescriptor: UnloadedDescriptor = {\n      name: undefined,\n      alias: `${alias}$inherits`,\n      value: plugin.inherits,\n      options,\n      dirname,\n    };\n\n    const inherits = yield* forwardAsync(loadPluginDescriptor, run => {\n      // If the inherited plugin changes, reinstantiate this plugin.\n      return cache.invalidate(data => run(inheritsDescriptor, data));\n    });\n\n    plugin.pre = chain(inherits.pre, plugin.pre);\n    plugin.post = chain(inherits.post, plugin.post);\n    plugin.manipulateOptions = chain(\n      inherits.manipulateOptions,\n      plugin.manipulateOptions,\n    );\n    plugin.visitor = traverse.visitors.merge([\n      inherits.visitor || {},\n      plugin.visitor || {},\n    ]);\n\n    if (inherits.externalDependencies.length > 0) {\n      if (externalDependencies.length === 0) {\n        externalDependencies = inherits.externalDependencies;\n      } else {\n        externalDependencies = freezeDeepArray([\n          externalDependencies,\n          inherits.externalDependencies,\n        ]);\n      }\n    }\n  }\n\n  return new Plugin(plugin, options, alias, externalDependencies);\n});\n\nconst needsFilename = (val: unknown) => val && typeof val !== \"function\";\n\nconst validateIfOptionNeedsFilename = (\n  options: ValidatedOptions,\n  descriptor: UnloadedDescriptor,\n): void => {\n  if (\n    needsFilename(options.test) ||\n    needsFilename(options.include) ||\n    needsFilename(options.exclude)\n  ) {\n    const formattedPresetName = descriptor.name\n      ? `\"${descriptor.name}\"`\n      : \"/* your preset */\";\n    throw new ConfigError(\n      [\n        `Preset ${formattedPresetName} requires a filename to be set when babel is called directly,`,\n        `\\`\\`\\``,\n        `babel.transformSync(code, { filename: 'file.ts', presets: [${formattedPresetName}] });`,\n        `\\`\\`\\``,\n        `See https://babeljs.io/docs/en/options#filename for more information.`,\n      ].join(\"\\n\"),\n    );\n  }\n};\n\nconst validatePreset = (\n  preset: PresetInstance,\n  context: ConfigContext,\n  descriptor: UnloadedDescriptor,\n): void => {\n  if (!context.filename) {\n    const { options } = preset;\n    validateIfOptionNeedsFilename(options, descriptor);\n    if (options.overrides) {\n      options.overrides.forEach(overrideOptions =>\n        validateIfOptionNeedsFilename(overrideOptions, descriptor),\n      );\n    }\n  }\n};\n\n/**\n * Generate a config object that will act as the root of a new nested config.\n */\nfunction* loadPresetDescriptor(\n  descriptor: UnloadedDescriptor,\n  context: Context.FullPreset,\n): Handler<{\n  chain: ConfigChain | null;\n  externalDependencies: ReadonlyDeepArray<string>;\n}> {\n  const preset = instantiatePreset(\n    yield* presetDescriptorLoader(descriptor, context),\n  );\n  validatePreset(preset, context, descriptor);\n  return {\n    chain: yield* buildPresetChain(preset, context),\n    externalDependencies: preset.externalDependencies,\n  };\n}\n\nconst instantiatePreset = makeWeakCacheSync(\n  ({\n    value,\n    dirname,\n    alias,\n    externalDependencies,\n  }: LoadedDescriptor): PresetInstance => {\n    return {\n      options: validate(\"preset\", value),\n      alias,\n      dirname,\n      externalDependencies,\n    };\n  },\n);\n\nfunction chain<Args extends any[]>(\n  a: undefined | ((...args: Args) => void),\n  b: undefined | ((...args: Args) => void),\n) {\n  const fns = [a, b].filter(Boolean);\n  if (fns.length <= 1) return fns[0];\n\n  return function (this: unknown, ...args: unknown[]) {\n    for (const fn of fns) {\n      fn.apply(this, args);\n    }\n  };\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAQA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;;AAEA;;AAKA;;AACA;;AAGA;;AAIA;;eAsBeA,UAAA,CAAQ,UAAUC,cAAV,CACrBC,SADqB,EAEW;EAAA;;EAChC,MAAMC,MAAM,GAAG,OAAO,IAAAC,gBAAA,EAAyBF,SAAzB,CAAtB;;EACA,IAAI,CAACC,MAAL,EAAa;IACX,OAAO,IAAP;EACD;;EACD,MAAM;IAAEE,OAAF;IAAWC,OAAX;IAAoBC;EAApB,IAAqCJ,MAA3C;;EAEA,IAAII,YAAY,KAAK,SAArB,EAAgC;IAC9B,OAAO,IAAP;EACD;;EAED,MAAMC,cAAc,GAAG,EAAvB;EAEA,MAAM;IAAEC,OAAF;IAAWC;EAAX,IAAuBL,OAA7B;;EAEA,IAAI,CAACI,OAAD,IAAY,CAACC,OAAjB,EAA0B;IACxB,MAAM,IAAIC,KAAJ,CAAU,+CAAV,CAAN;EACD;;EAED,MAAMC,aAAiC,qBAClCN,OADkC;IAErCO,OAAO,EAAER,OAAO,CAACQ;EAFoB,EAAvC;;EAKA,MAAMC,YAAY,GAAIC,IAAD,IAAsB;IACzC,MAAMC,IAAI,GAAG,IAAAC,uBAAA,EAAkBF,IAAlB,CAAb;;IACA,IAAI,CAACC,IAAL,EAAW;MACT,MAAM,IAAIL,KAAJ,CAAU,yCAAV,CAAN;IACD;;IAED,OAAOK,IAAP;EACD,CAPD;;EASA,MAAME,kBAAkB,GAAGR,OAAO,CAACS,GAAR,CAAYL,YAAZ,CAA3B;EACA,MAAMM,yBAAyB,GAAGX,OAAO,CAACU,GAAR,CAAYL,YAAZ,CAAlC;EACA,MAAMO,uBAAyD,GAAG,CAAC,EAAD,CAAlE;EACA,MAAMC,MAA4B,GAAG,EAArC;EAEA,MAAMC,oBAAuC,GAAG,EAAhD;EAEA,MAAMC,OAAO,GAAG,OAAOC,YAAY,CACjCnB,OADiC,EAEjC,UAAUoB,wBAAV,CACEC,UADF,EAEEC,qBAFF,EAGwB;IACtB,MAAMlB,OAGJ,GAAG,EAHL;;IAKA,KAAK,IAAImB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAAU,CAACG,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;MAC1C,MAAME,UAAU,GAAGJ,UAAU,CAACE,CAAD,CAA7B;;MACA,IAAIE,UAAU,CAAC1B,OAAX,KAAuB,KAA3B,EAAkC;QAChC,IAAI;UAEF,IAAI2B,MAAM,GAAG,OAAOC,oBAAoB,CAACF,UAAD,EAAanB,aAAb,CAAxC;QACD,CAHD,CAGE,OAAOsB,CAAP,EAAU;UACV,IAAIA,CAAC,CAACC,IAAF,KAAW,sBAAf,EAAuC;YACrC,IAAAC,wCAAA,EAAgCT,UAAhC,EAA4CE,CAA5C,EAA+C,QAA/C,EAAyDK,CAAzD;UACD;;UACD,MAAMA,CAAN;QACD;;QAEDX,oBAAoB,CAACc,IAArB,CAA0BL,MAAM,CAACT,oBAAjC;;QAKA,IAAIQ,UAAU,CAACO,OAAf,EAAwB;UACtB5B,OAAO,CAAC2B,IAAR,CAAa;YAAEL,MAAM,EAAEA,MAAM,CAACO,KAAjB;YAAwBC,IAAI,EAAE;UAA9B,CAAb;QACD,CAFD,MAEO;UACL9B,OAAO,CAAC+B,OAAR,CAAgB;YACdT,MAAM,EAAEA,MAAM,CAACO,KADD;YAEdC,IAAI,EAAEZ;UAFQ,CAAhB;QAID;MACF;IACF;;IAGD,IAAIlB,OAAO,CAACoB,MAAR,GAAiB,CAArB,EAAwB;MAGtBT,uBAAuB,CAACqB,MAAxB,CACE,CADF,EAEE,CAFF,EAGE,GAAGhC,OAAO,CAACS,GAAR,CAAYwB,CAAC,IAAIA,CAAC,CAACH,IAAnB,EAAyBI,MAAzB,CAAgCC,CAAC,IAAIA,CAAC,KAAKjB,qBAA3C,CAHL;;MAMA,KAAK,MAAM;QAAEI,MAAF;QAAUQ;MAAV,CAAX,IAA+B9B,OAA/B,EAAwC;QACtC,IAAI,CAACsB,MAAL,EAAa,OAAO,IAAP;QAEbQ,IAAI,CAACH,IAAL,CAAU,GAAGL,MAAM,CAACvB,OAApB;QAEA,MAAMe,OAAO,GAAG,OAAOE,wBAAwB,CAACM,MAAM,CAACtB,OAAR,EAAiB8B,IAAjB,CAA/C;QACA,IAAIhB,OAAJ,EAAa,OAAO,IAAP;QAEbQ,MAAM,CAAC3B,OAAP,CAAeyC,OAAf,CAAuBC,IAAI,IAAI;UAC7B,IAAAC,kBAAA,EAAaxC,cAAb,EAA6BuC,IAA7B;QACD,CAFD;MAGD;IACF;EACF,CA/DgC,CAAZ,CAgErB7B,kBAhEqB,EAgEDG,uBAAuB,CAAC,CAAD,CAhEtB,CAAvB;EAkEA,IAAIG,OAAJ,EAAa,OAAO,IAAP;EAEb,MAAMuB,IAAS,GAAGvC,cAAlB;EACA,IAAAwC,kBAAA,EAAaD,IAAb,EAAmB1C,OAAnB;EAEA,MAAM4C,aAAiC,qBAClCrC,aADkC;IAErCsC,WAAW,uBAAEH,IAAI,CAACG,WAAP,gCAAsB;EAFI,EAAvC;EAKA,OAAOzB,YAAY,CAACnB,OAAD,EAAU,UAAU6C,qBAAV,GAAkC;IAC7D9B,uBAAuB,CAAC,CAAD,CAAvB,CAA2BoB,OAA3B,CAAmC,GAAGrB,yBAAtC;;IAEA,KAAK,MAAMgC,KAAX,IAAoB/B,uBAApB,EAA6C;MAC3C,MAAMmB,IAAc,GAAG,EAAvB;MACAlB,MAAM,CAACe,IAAP,CAAYG,IAAZ;;MAEA,KAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuB,KAAK,CAACtB,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;QACrC,MAAME,UAA8B,GAAGqB,KAAK,CAACvB,CAAD,CAA5C;;QACA,IAAIE,UAAU,CAAC1B,OAAX,KAAuB,KAA3B,EAAkC;UAChC,IAAI;YAEF,IAAIgD,MAAM,GAAG,OAAOC,oBAAoB,CAACvB,UAAD,EAAakB,aAAb,CAAxC;UACD,CAHD,CAGE,OAAOf,CAAP,EAAU;YACV,IAAIA,CAAC,CAACC,IAAF,KAAW,+BAAf,EAAgD;cAE9C,IAAAC,wCAAA,EAAgCgB,KAAhC,EAAuCvB,CAAvC,EAA0C,QAA1C,EAAoDK,CAApD;YACD;;YACD,MAAMA,CAAN;UACD;;UACDM,IAAI,CAACH,IAAL,CAAUgB,MAAV;UAEA9B,oBAAoB,CAACc,IAArB,CAA0BgB,MAAM,CAAC9B,oBAAjC;QACD;MACF;IACF;EACF,CA1BkB,CAAZ,EAAP;EA4BAwB,IAAI,CAACtC,OAAL,GAAea,MAAM,CAAC,CAAD,CAArB;EACAyB,IAAI,CAACrC,OAAL,GAAeY,MAAM,CAClBiC,KADY,CACN,CADM,EAEZX,MAFY,CAELnC,OAAO,IAAIA,OAAO,CAACqB,MAAR,GAAiB,CAFvB,EAGZX,GAHY,CAGRV,OAAO,KAAK;IAAEA;EAAF,CAAL,CAHC,CAAf;EAIAsC,IAAI,CAACS,aAAL,GAAqBT,IAAI,CAACrC,OAAL,CAAaoB,MAAb,GAAsB,CAA3C;EAEA,OAAO;IACLzB,OAAO,EAAE0C,IADJ;IAELzB,MAAM,EAAEA,MAFH;IAGLC,oBAAoB,EAAE,IAAAkC,mBAAA,EAAgBlC,oBAAhB;EAHjB,CAAP;AAKD,CA9Jc,C;;;;AAgKf,SAASE,YAAT,CAA0CnB,OAA1C,EAAkEoD,EAAlE,EAA4E;EAC1E,OAAO,WAAWC,IAAX,EAA0BC,IAA1B,EAAyC;IAC9C,IAAI;MACF,OAAO,OAAOF,EAAE,CAACC,IAAD,EAAOC,IAAP,CAAhB;IACD,CAFD,CAEE,OAAO1B,CAAP,EAAU;MAGV,IAAI,CAAC,aAAa2B,IAAb,CAAkB3B,CAAC,CAAC4B,OAApB,CAAL,EAAmC;QAAA;;QACjC5B,CAAC,CAAC4B,OAAF,GAAa,WAAD,qBAAWxD,OAAO,CAACyD,QAAnB,gCAA+B,cAAe,KACxD7B,CAAC,CAAC4B,OACH,EAFD;MAGD;;MAED,MAAM5B,CAAN;IACD;EACF,CAdD;AAeD;;AAKD,MAAM8B,oBAAoB,GACxBC,UAD2B,IAM3B,IAAAC,sBAAA,EAAc,WACZ;EAAEC,KAAF;EAAS9D,OAAT;EAAkB+D,OAAlB;EAA2BC;AAA3B,CADY,EAEZC,KAFY,EAGe;EAE3B,IAAIjE,OAAO,KAAK,KAAhB,EAAuB,MAAM,IAAIM,KAAJ,CAAU,mBAAV,CAAN;EAEvBN,OAAO,GAAGA,OAAO,IAAI,EAArB;EAEA,MAAMkB,oBAAmC,GAAG,EAA5C;EAEA,IAAIR,IAAI,GAAGoD,KAAX;;EACA,IAAI,OAAOA,KAAP,KAAiB,UAArB,EAAiC;IAC/B,MAAMI,OAAO,GAAG,IAAAC,iBAAA,EACdL,KADc,EAEb,wFAFa,CAAhB;IAKA,MAAMM,GAAG,qBACJnE,OADI,EAEJ2D,UAAU,CAACK,KAAD,EAAQ/C,oBAAR,CAFN,CAAT;;IAIA,IAAI;MACFR,IAAI,GAAG,OAAOwD,OAAO,CAACE,GAAD,EAAMpE,OAAN,EAAe+D,OAAf,CAArB;IACD,CAFD,CAEE,OAAOlC,CAAP,EAAU;MACV,IAAImC,KAAJ,EAAW;QACTnC,CAAC,CAAC4B,OAAF,IAAc,uBAAsBY,IAAI,CAACC,SAAL,CAAeN,KAAf,CAAsB,GAA1D;MACD;;MACD,MAAMnC,CAAN;IACD;EACF;;EAED,IAAI,CAACnB,IAAD,IAAS,OAAOA,IAAP,KAAgB,QAA7B,EAAuC;IACrC,MAAM,IAAIJ,KAAJ,CAAU,yCAAV,CAAN;EACD;;EAED,IAAI,IAAAiE,iBAAA,EAAW7D,IAAX,CAAJ,EAAsB;IAEpB,OAAO,EAAP;IAEA,MAAM,IAAIJ,KAAJ,CACH,gDAAD,GACG,wDADH,GAEG,sCAFH,GAGG,oDAHH,GAIG,8DAJH,GAKG,sBAAqB+D,IAAI,CAACC,SAAL,CAAeN,KAAf,CAAsB,GAN1C,CAAN;EAQD;;EAED,IACE9C,oBAAoB,CAACO,MAArB,GAA8B,CAA9B,KACC,CAACwC,KAAK,CAACO,UAAN,EAAD,IAAuBP,KAAK,CAACQ,IAAN,OAAiB,SADzC,CADF,EAGE;IACA,IAAIC,KAAK,GACN,sDAAD,GACC,IAAGxD,oBAAoB,CAAC,CAAD,CAAI,mBAF9B;;IAGA,IAAI,CAAC+C,KAAK,CAACO,UAAN,EAAL,EAAyB;MACvBE,KAAK,IAAK,mFAAV;IACD,CAFD,MAEO;MACLA,KAAK,IAAK,gDAAV;IACD;;IACDA,KAAK,IACF,mFAAD,GACC,sEADD,GAEC,0DAFD,GAGC,sBAAqBL,IAAI,CAACC,SAAL,CAAeN,KAAf,CAAsB,GAJ9C;IAMA,MAAM,IAAI1D,KAAJ,CAAUoE,KAAV,CAAN;EACD;;EAED,OAAO;IACLZ,KAAK,EAAEpD,IADF;IAELV,OAFK;IAGL+D,OAHK;IAILC,KAJK;IAKL9C,oBAAoB,EAAE,IAAAkC,mBAAA,EAAgBlC,oBAAhB;EALjB,CAAP;AAOD,CA9ED,CANF;;AAsFA,MAAMyD,sBAAsB,GAAGhB,oBAAoB,CAGjDiB,wBAHiD,CAAnD;AAIA,MAAMC,sBAAsB,GAAGlB,oBAAoB,CAGjDmB,wBAHiD,CAAnD;;AAQA,UAAU7B,oBAAV,CACEvB,UADF,EAEEzB,OAFF,EAGmB;EACjB,IAAIyB,UAAU,CAACoC,KAAX,YAA4BiB,eAAhC,EAAwC;IACtC,IAAIrD,UAAU,CAAC1B,OAAf,EAAwB;MACtB,MAAM,IAAIM,KAAJ,CACJ,8DADI,CAAN;IAGD;;IAED,OAAOoB,UAAU,CAACoC,KAAlB;EACD;;EAED,OAAO,OAAOkB,iBAAiB,CAC7B,OAAOL,sBAAsB,CAACjD,UAAD,EAAazB,OAAb,CADA,EAE7BA,OAF6B,CAA/B;AAID;;AAED,MAAM+E,iBAAiB,GAAG,IAAAnB,sBAAA,EAAc,WACtC;EAAEC,KAAF;EAAS9D,OAAT;EAAkB+D,OAAlB;EAA2BC,KAA3B;EAAkC9C;AAAlC,CADsC,EAEtC+C,KAFsC,EAGrB;EACjB,MAAMgB,SAAS,GAAG,IAAAC,6BAAA,EAAqBpB,KAArB,CAAlB;EAEA,MAAMd,MAAM,qBACPiC,SADO,CAAZ;;EAGA,IAAIjC,MAAM,CAACmC,OAAX,EAAoB;IAClBnC,MAAM,CAACmC,OAAP,GAAiBC,mBAAA,CAASC,OAAT,mBACZrC,MAAM,CAACmC,OADK,EAAjB;EAGD;;EAED,IAAInC,MAAM,CAACsC,QAAX,EAAqB;IACnB,MAAMC,kBAAsC,GAAG;MAC7CC,IAAI,EAAEC,SADuC;MAE7CzB,KAAK,EAAG,GAAEA,KAAM,WAF6B;MAG7CF,KAAK,EAAEd,MAAM,CAACsC,QAH+B;MAI7CtF,OAJ6C;MAK7C+D;IAL6C,CAA/C;IAQA,MAAMuB,QAAQ,GAAG,OAAO,IAAAI,mBAAA,EAAazC,oBAAb,EAAmC0C,GAAG,IAAI;MAEhE,OAAO1B,KAAK,CAAC2B,UAAN,CAAiBC,IAAI,IAAIF,GAAG,CAACJ,kBAAD,EAAqBM,IAArB,CAA5B,CAAP;IACD,CAHuB,CAAxB;IAKA7C,MAAM,CAAC8C,GAAP,GAAa5D,KAAK,CAACoD,QAAQ,CAACQ,GAAV,EAAe9C,MAAM,CAAC8C,GAAtB,CAAlB;IACA9C,MAAM,CAAC+C,IAAP,GAAc7D,KAAK,CAACoD,QAAQ,CAACS,IAAV,EAAgB/C,MAAM,CAAC+C,IAAvB,CAAnB;IACA/C,MAAM,CAACgD,iBAAP,GAA2B9D,KAAK,CAC9BoD,QAAQ,CAACU,iBADqB,EAE9BhD,MAAM,CAACgD,iBAFuB,CAAhC;IAIAhD,MAAM,CAACmC,OAAP,GAAiBC,mBAAA,CAASa,QAAT,CAAkBC,KAAlB,CAAwB,CACvCZ,QAAQ,CAACH,OAAT,IAAoB,EADmB,EAEvCnC,MAAM,CAACmC,OAAP,IAAkB,EAFqB,CAAxB,CAAjB;;IAKA,IAAIG,QAAQ,CAACpE,oBAAT,CAA8BO,MAA9B,GAAuC,CAA3C,EAA8C;MAC5C,IAAIP,oBAAoB,CAACO,MAArB,KAAgC,CAApC,EAAuC;QACrCP,oBAAoB,GAAGoE,QAAQ,CAACpE,oBAAhC;MACD,CAFD,MAEO;QACLA,oBAAoB,GAAG,IAAAkC,mBAAA,EAAgB,CACrClC,oBADqC,EAErCoE,QAAQ,CAACpE,oBAF4B,CAAhB,CAAvB;MAID;IACF;EACF;;EAED,OAAO,IAAI6D,eAAJ,CAAW/B,MAAX,EAAmBhD,OAAnB,EAA4BgE,KAA5B,EAAmC9C,oBAAnC,CAAP;AACD,CArDyB,CAA1B;;AAuDA,MAAMiF,aAAa,GAAIC,GAAD,IAAkBA,GAAG,IAAI,OAAOA,GAAP,KAAe,UAA9D;;AAEA,MAAMC,6BAA6B,GAAG,CACpCrG,OADoC,EAEpC0B,UAFoC,KAG3B;EACT,IACEyE,aAAa,CAACnG,OAAO,CAACwD,IAAT,CAAb,IACA2C,aAAa,CAACnG,OAAO,CAACsG,OAAT,CADb,IAEAH,aAAa,CAACnG,OAAO,CAACuG,OAAT,CAHf,EAIE;IACA,MAAMC,mBAAmB,GAAG9E,UAAU,CAAC8D,IAAX,GACvB,IAAG9D,UAAU,CAAC8D,IAAK,GADI,GAExB,mBAFJ;IAGA,MAAM,IAAIiB,oBAAJ,CACJ,CACG,UAASD,mBAAoB,+DADhC,EAEG,QAFH,EAGG,8DAA6DA,mBAAoB,OAHpF,EAIG,QAJH,EAKG,uEALH,EAMEE,IANF,CAMO,IANP,CADI,CAAN;EASD;AACF,CAtBD;;AAwBA,MAAMC,cAAc,GAAG,CACrBhF,MADqB,EAErB1B,OAFqB,EAGrByB,UAHqB,KAIZ;EACT,IAAI,CAACzB,OAAO,CAACyD,QAAb,EAAuB;IACrB,MAAM;MAAE1D;IAAF,IAAc2B,MAApB;IACA0E,6BAA6B,CAACrG,OAAD,EAAU0B,UAAV,CAA7B;;IACA,IAAI1B,OAAO,CAAC4G,SAAZ,EAAuB;MACrB5G,OAAO,CAAC4G,SAAR,CAAkBnE,OAAlB,CAA0BoE,eAAe,IACvCR,6BAA6B,CAACQ,eAAD,EAAkBnF,UAAlB,CAD/B;IAGD;EACF;AACF,CAdD;;AAmBA,UAAUE,oBAAV,CACEF,UADF,EAEEzB,OAFF,EAMG;EACD,MAAM0B,MAAM,GAAGmF,iBAAiB,CAC9B,OAAOjC,sBAAsB,CAACnD,UAAD,EAAazB,OAAb,CADC,CAAhC;EAGA0G,cAAc,CAAChF,MAAD,EAAS1B,OAAT,EAAkByB,UAAlB,CAAd;EACA,OAAO;IACLQ,KAAK,EAAE,OAAO,IAAA6E,6BAAA,EAAiBpF,MAAjB,EAAyB1B,OAAzB,CADT;IAELiB,oBAAoB,EAAES,MAAM,CAACT;EAFxB,CAAP;AAID;;AAED,MAAM4F,iBAAiB,GAAG,IAAAE,0BAAA,EACxB,CAAC;EACClD,KADD;EAECC,OAFD;EAGCC,KAHD;EAIC9C;AAJD,CAAD,KAKwC;EACtC,OAAO;IACLlB,OAAO,EAAE,IAAAiH,iBAAA,EAAS,QAAT,EAAmBnD,KAAnB,CADJ;IAELE,KAFK;IAGLD,OAHK;IAIL7C;EAJK,CAAP;AAMD,CAbuB,CAA1B;;AAgBA,SAASgB,KAAT,CACEgF,CADF,EAEEC,CAFF,EAGE;EACA,MAAMC,GAAG,GAAG,CAACF,CAAD,EAAIC,CAAJ,EAAO5E,MAAP,CAAc8E,OAAd,CAAZ;EACA,IAAID,GAAG,CAAC3F,MAAJ,IAAc,CAAlB,EAAqB,OAAO2F,GAAG,CAAC,CAAD,CAAV;EAErB,OAAO,UAAyB,GAAGE,IAA5B,EAA6C;IAClD,KAAK,MAAMjE,EAAX,IAAiB+D,GAAjB,EAAsB;MACpB/D,EAAE,CAACkE,KAAH,CAAS,IAAT,EAAeD,IAAf;IACD;EACF,CAJD;AAKD"}