module.exports={A:{A:{"1":"F A B","2":"J D E 4B"},B:{"1":"C K L G M N O P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H"},C:{"1":"0 1 2 3 4 5 6 7 8 9 5B pB I q J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a d e f g h i j k l m n o p b H tB 6B 7B"},D:{"1":"0 1 2 3 4 5 6 7 8 9 I q J D E F A B C K L G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB qB VB rB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R S T U V W X Y Z a d e f g h i j k l m n o p b H tB 8B 9B"},E:{"1":"J D E F A B C K L G BC CC DC EC vB mB nB wB FC GC xB yB zB 0B oB 1B HC","2":"I AC uB","16":"q"},F:{"1":"0 1 2 3 4 5 6 7 8 9 B C G M N O r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB QB RB SB TB UB VB WB XB c YB ZB aB bB cB dB eB fB gB hB iB jB kB lB P Q R sB S T U V W X Y Z a JC KC LC mB 2B MC nB","16":"F IC"},G:{"1":"E OC PC QC RC SC TC UC VC WC XC YC ZC aC bC cC dC eC fC gC xB yB zB 0B oB 1B","16":"uB NC 3B"},H:{"2":"hC"},I:{"1":"pB I H kC lC 3B","16":"iC jC","132":"mC nC"},J:{"1":"D A"},K:{"1":"A B C c mB 2B nB"},L:{"132":"H"},M:{"132":"b"},N:{"1":"A B"},O:{"1":"oC"},P:{"2":"I","132":"pC qC rC sC tC vB uC vC wC xC yC oB zC 0C"},Q:{"1":"wB"},R:{"132":"1C"},S:{"1":"2C"}},B:7,C:"KeyboardEvent.which"};
